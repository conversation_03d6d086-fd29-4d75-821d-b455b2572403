	/*
		考试栏目--通用样式
	*/
   .exam-tab {
		height: auto;
		width: 100%;
		margin-bottom: 20rpx;
		padding: 20rpx 0;
		// 添加栏目底部阴影
		box-shadow: 0 4rpx 20rpx #dedede;
   }
   
   .tab {
		overflow: auto;
		min-height: 80rpx;
		height: auto;
   }
   
   
   // 头部图片
   .head-img{
   
		img{
		    display: block;
		    width: 100%;
		    height: auto;
		}
	  
		text:nth-child(2){
			position: absolute;
			padding: 0 20rpx;
			left: 40rpx;
			top: 130rpx;

			height: 60rpx;
			line-height: 60rpx;
		  	width: auto;
		  
		  	font-size: 28rpx;
		  	border-radius: 30rpx;
		  	color: #F9AE3D;
		  	background-color: rgba(254,251,251, 0.8);
	  }
   }
   /*
   	栏目头部以及标题部分
   */
      
   // 提示横幅
   .tab-tips {
		position: fixed;
		z-index: 1;
		// top: 80rpx;
		width: 100%;
		line-height: 80rpx;
		height: 80rpx;
		font-size: 32rpx;
		color: #ffffff;
		background-color: #1e1848;
		// background-color: rgba(249,174,61, 0.8);
		text {
			margin: 0 40rpx;
		}
   }
   // 栏目提示-小字
   .tab-tips-small {
		width: 90%;
		margin: 0 auto;
		line-height: 60rpx;
		height: 60rpx;
		font-size: 36rpx;
		color: #ffffff;
		background-color: rgba(249,174,61, 0.8);
		border-radius: 40rpx;
		text {
			margin: 0 40rpx;
		}
   }
   
   // 栏目大标题
   .tab-title {
		display: block;
		width: 80%;
		height: 60rpx;
		line-height: 60rpx;
		text-align: left;
		font-size: 40rpx;
		font-weight: bold;
		padding: 20rpx 40rpx;
   }
   
   // 栏目头部
   .tab-head {
		width: 100%;
		height: auto;
		background-color: #ffffff;
		margin: 20rpx 0;
		
		text {
			display: block;
			line-height: 50rpx;
		}
		
		text:first-child {
			font-size: 46rpx;
			padding: 0 0 0 40rpx;
			font-weight: bold;
		}
   	
		text:nth-child(2) {
			font-size: 32rpx;
			color: #909399;
			margin-left: 20rpx;
		}
   }
   
   .tab-head-smail {
		width: 90%;
		height: auto;
		background-color: #ffffff;
		padding: 0 5%;
		
		text {
			font-size: 36rpx;
			line-height: 80rpx;
		}
   }
   

	// 栏目头部
	.tab-head-title {
		width: 100%;
		height: 100rpx;
		background-color: #ffffff;

		text:first-child {
			font-size: 46rpx;
			padding: 0 0 0 40rpx;
			font-weight: bold;
		}
	}

	// 包含标签的题目
	.tab-head-tips {
		width: 96%;
		height: 100rpx;
		line-height: 100rpx;
		font-size: 28rpx;
		display: flex;
		margin-left: 40rpx;

		text:nth-child(1) {
			width: 63%;
			font-size: 46rpx;
			padding: 0 0 0 0rpx;
			font-weight: bold;
		}

		text:nth-child(2) {
			font-size: 32rpx;
			color: #909399;
			margin: 0 40rpx;
		}
	}
	
	.tab-head-tag {
		margin: 0 auto;
		width: 90%;
		line-height: 60rpx;
		height: auto;
		
		text:nth-child(1) {
			padding: 0 20rpx;
			margin: 0rpx 10rpx;
		
			font-size: 28rpx;
			border-radius: 10rpx;
			color: #fff;
			background-color: #1e1848;
		}
		
		text:nth-child(2) {
			font-size: 36rpx;
		}
	}
	
	// 栏目普通文本
	.tab-text {
		width: 90%;
		padding: 0 5%;
		line-height: 60rpx;
		
		text {
			font-size: 36rpx;
			font-weight: bold;
			line-height: 60rpx;
			margin-right: 10rpx;
		}
	}
	
	/*
		弹框组件
	*/
	
	// 筛选组件标题
	.filter-title {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
		padding: 0 40%;

		text {
			text-align: center;
			font-size: 36rpx;
			margin: 0 5rpx;
		}	
	}
	
	// 筛选组件内容
	.filter-content {
		width: 100%;
		height: 800rpx;
	}
	
	.filter-scroll-Y {
		display: block;
		width: 100%;
		height: 750rpx;
	}
	
	// 弹框组件文本
	.filter-content-text {
		width: 90%;
		padding: 0 5%;
		
		text {
			display: block;
			line-height: 60rpx;
			height: auto;
			font-size: 36rpx;
		}
	}
	
	// 底栏
	.bottom-tab {
		position: fixed;
		z-index: 999;
		bottom: 0rpx;
		width: 100%;
		height: 120rpx;
		line-height: 120rpx;
		background-color: #f4f4f5;
		display: flex;
		
		text {
			font-size: 36rpx;
		}
		
		view {
			width: 33%;
			text-align: center;
			
			text {
				margin: 0 5rpx;
			}
		}
	}
	
	// 小箭头
	.picker-text-arrow {
		display: block;
		float: right;
		height: 100rpx;
		line-height: 100rpx;
		font-size: 36rpx;
		padding-right: 40rpx;
		color: #88888c;
	}
	
	/*
		输入组件
	*/

	// 输入框
	.tab-inputbox-high {
		display: block;
		width: 90%;
		margin: 30rpx 40rpx;
		text {
			font-size: 36rpx;
			margin-right: 20rpx;
		}
	}
	
	// 多行输入框
	.multiline-input {
		padding: 20rpx 20rpx;
		width: 100%;
		height: 300rpx;
		line-height: 100rpx;
		border-radius: 20rpx;
		color: #000000;
		font-size: 36rpx;
	}
	
	/*
		考试排行榜
	*/
	.tab-rank {
		width: 100%;
		display: flex;
	}
	
	// 排行榜头像
	.rank-img {
		width: 33.33%;
		height: 200rpx;
		padding: 30rpx 0;
	
		img:nth-child(1) {
			display: block;
			z-index: 999;
			width: 120rpx;
			height: 120rpx;
			margin: 0 auto;
			border-radius: 50%;
		}
	
		img:nth-child(2) {
			position: absolute;
			width: 140rpx;
			height: 140rpx;
			margin: -130rpx 58rpx;
		}
	
	}
	
	.rank-img-text {
		margin-top: 30rpx;
		line-height: 50rpx;
		
		text:nth-child(1){
			display: block;
			text-align: center;
			font-size: 32rpx;
			color: #909399;
		} 
		
		text:nth-child(2){
			display: block;
			text-align: center;
			font-size: 36rpx;
		
		} 
	}
	
	
	/*
		栏目选框部分
	*/

	// 单选/多选框
	.tab-checkbox {
		width: 125rpx;
		height: 125rpx;
		line-height: 90rpx;
		text {
			text-align: center;
		}
	}

	.checkbox {
		border-radius: 50%;
		height: 90rpx;
		width: 90rpx;
		line-height: 90rpx;
		margin: 0 auto;

		text-align: center;
		font-size: 32rpx;
		background-color: #f9f9f9;
	}

	// 选框选中后样式
	.activeBox {
		color: #1e1848;
		border: #1e1848 4rpx dashed;
		background-color: #fff;
		height: 82rpx;
		width: 82rpx;
		line-height: 82rpx;
	}
	
	// 选项
	.tab-choice {
		width: 100%;
		line-height: 80rpx;
		display: flex;
		padding: 0 20rpx;
		
		text {
			font-size: 36rpx;
			margin: 0 5rpx;
		}
		
		text:nth-child(1){
			margin: 0 5rpx 0 20rpx;
		}
		
	}
	
	.choice-text {
		width: 85%;
		
		text{
			display: block;
		}
		
		img {
			margin: 20rpx 0;
			width: 200rpx;
			height: 200rpx;
		}
	}

	/*
		按钮部分
	*/

	// 大按钮
	.btn-big {
		padding-bottom: 60rpx;

		button {
			margin: 80rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	// 底部按钮
	.btn-bottom {
		button {
			bottom: 20rpx;
			margin: 20rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	// 底部固定按钮
	.btn-bottom-fixed {
		position: fixed;
		z-index: 999;
		bottom: 0rpx;
		width: 100%;
		height: 120rpx;
		background-color: #ffffff;

		button {
			margin: 20rpx 5%;
			width: 90%;
			height: 80rpx;
			line-height: 80rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}
	
	// 按钮组
	.btn-group {
		width: 100%;
		height: 90rpx;
		display: flex;
		flex-direction: row;
		padding: 80rpx 0 200rpx 0;
	
		button {
			width: 40%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}
	
	// 镂空按钮
	.btn-hollow {
		button {
			margin: 60rpx auto;
			width: 40%;
			height: 70rpx;
			line-height: 66rpx;
			color: #1e1848;
			background-color: #fff;
			border: #1e1848 2rpx solid;
			border-radius: 40rpx;
			font-size: 36rpx;
		}
	}
	
	.question-img,.question-video{
		width: 100%;
		height: auto;
		
		img {
			display: block;
			margin: 40rpx auto;
			width: 450rpx;
			height: auto;
		}
		
		video {
			display: block;
			margin: 40rpx auto;
			object-position: inherit;
			width: 80%;
			min-height: 400rpx;
		}
	}
