<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 学历选择器 -->
		<u-picker :show="show" @cancel="show = false" :columns="educationList" @confirm="confirmState(0)"
			@change="changeHandler" keyName="label" :defaultIndex="defaultIndex"></u-picker>

		<!-- 家庭成员选择器 -->
		<u-picker :show="show1" @cancel="show1 = false" :columns="familyList" @confirm="confirmState(1)"
			@change="changeHandler1" keyName="label" :defaultIndex="defaultIndex1"></u-picker>

		<!-- 		<address-picker :show="true" @change="changeHandler2"></address-picker> -->

		<!-- 人脸识别相机 -->
		<cameraPage :showCameraPage="showCameraPage" @submitCameraPhoto="submitCameraPhoto"
			@closeCameraPage="closeCameraPage" />

		<!-- 上传头像提示弹窗 -->
		<u-popup :show="popupShow" mode="bottom" @close="popupShow = false">
			<view class="popup-title">
				<text>头像照片示例</text>
			</view>
			<view class="popup-tips">
				<text style="color: #909399;text-align: left;">穿着整洁，头发整齐，站在白墙前拍摄照片，你的头像会更好看。</text>
			</view>
			<view class="popup-img" @longpress="uploadHeadPortrait(1)">
				<img :src="headUplpadTips" alt="" @click="uploadHeadPortrait(0)" mode="widthFix">
			</view>
			<view class="popup-tips" @longpress="uploadHeadPortrait(1)">
				<text :style="headImg !==''?'color: #19be6b;':'color: #ff4d4b;'">上传真实照片可获得10分</text>
				<text style="color: #ff4d4b;">若经审核头像不合格则-30分</text>
				<text style="color: #ff4d4b;">*无法上传可以尝试长按这里哦</text>
			</view>
			<view class="btn-big" style="padding-bottom: 0rpx;">
				<button style="margin: 40rpx auto 60rpx auto;" @click="uploadHeadPortrait(0)">我知道了</button>
			</view>
		</u-popup>

		<!-- 详细住址弹框 -->
		<u-popup :show="showAddressDetail" mode="bottom" @close="showAddressDetail = false">
			<view class="filter-title">
				<text>详细住址</text>
			</view>

			<view class="filter-content" style="height: 600rpx;">
				<view class="filter-tab">
					<view class="tab-inputbox-high" style="margin: 10rpx 40rpx 30rpx 40rpx;">
						<u--textarea class="multiline-input" confirmType="done" maxlength="200"
							v-model="employee.address" placeholder="请输入现居地详细地址" height="100" count></u--textarea>
					</view>
					<view class="tab-title">
						<text style="font-size: 32rpx;text-align: right;font-weight: 100;color: #909399;">
							* 地址越详细，附近派单准确率越高哦</text>
					</view>
				</view>
			</view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="employee.address=''">
						<text>清空输入</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="showAddressDetail=false">
					<view class="filter-button-right">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 家庭情况备注弹框 -->
		<u-popup :show="showFamilyRemark" mode="bottom" @close="showFamilyRemark = false">
			<view class="filter-title">
				<text>基本成员选择</text>
			</view>

			<view class="filter-content" style="height: 600rpx;">
				<view class="filter-tab flac-col">
					<!-- 	<view class="tab-inputbox-high" style="margin: 10rpx 40rpx 30rpx 40rpx;">
						<u--textarea class="multiline-input" confirmType="done" maxlength="200"
							v-model="employeeInfo.family" placeholder="请输入家庭情况具体备注" height="100"></u--textarea>
					</view> -->
					<view class="w10">
						<view class="tab-checkbox" v-for="(tabList, index) in familyMemberList" :key="index">
							<view class="checkbox" :class="{ activeBox: tabList.isCheck == 1 ? true : false }">
								<text @click="choiceTab(5, index)">{{ tabList.text }}</text>
							</view>
						</view>
					</view>
					<view class="tab-title">
						<text style="font-size: 32rpx;text-align: right;font-weight: 100;color: #909399;">
							* 填写详细情况，可优先派单哦</text>
					</view>
				</view>
			</view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="employeeInfo.family=''">
						<text>清空备注</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="showFamilyRemark=false">
					<view class="filter-button-right">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 擅长菜系弹框 -->
		<u-popup :show="showCookingDetail" mode="bottom" @close="showCookingDetail = false">
			<view class="filter-title">
				<text>选择菜系</text>
			</view>

			<view class="filter-content" style="height: 1100rpx;">
				<view class="filter-tab">
					<view class="tab-checkbox" v-for="(tabList, index) in cookingList" :key="index">
						<view class="checkbox">
							<text
								@click="baomuInfo.cooking+=baomuInfo.cooking?','+tabList.text:tabList.text">{{ tabList.text }}</text>
						</view>
					</view>
					<view class="tab-inputbox-high" style="margin: 10rpx 40rpx 30rpx 40rpx;">
						<u--textarea class="multiline-input" confirmType="done" maxlength="200"
							v-model="baomuInfo.cooking" placeholder="请输入掌握的菜系" height="100"></u--textarea>
					</view>
					<view class="tab-title">
						<text style="font-size: 32rpx;text-align: right;font-weight: 100;color: #909399;">
							* 菜系掌握越多，机会越多哦</text>
					</view>
				</view>
			</view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="baomuInfo.cooking=''">
						<text>清空输入</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="showCookingDetail=false">
					<view class="filter-button-right">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- <view class="tab-tips" v-if="isBaomuuInfo"> -->
		<view class="tab-tips" v-if="!showCameraPage"
			:style="isGetScore?'background-color: #19be6b;':'background-color: #1e1848;'">
			<text>{{isGetScore?"个人信息填写完整，已获得35分":"完善个人信息最多可以获得35分哦～"}}</text>

		</view>
		<view class="tab-tips" style="background-color: #ff4d4b;" v-if="isPortraitUnqualified">
			<text>当前头像不符合规范，请重新上传！</text>
		</view>

		<u-gap height="38"></u-gap>

		<view v-if="!showCameraPage">
			<view class="resume-tab">
				<view class="tab">
					<view class="img-upload">
						<img class="head-img" :src="headImg !== ''&&headImg !== null ? headImg : blankImg"
							@click="popupShow = true" @longpress="openImgPreview()" />
					</view>
					<view class="tab-head">
						<text>姓名</text>
						<text style="margin-right: 0rpx;padding-right: 0rpx;" @click="openIdentification">身份认证</text>
						<!-- <text
						:style="employee.realName!==null&&employee.realName!==''?'color: #19be6b;':'color: #ff4d4b;'">5分</text> -->
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="text" v-model="employee.realName"
								placeholder="可通过上传身份证修改" disabled="true" @click="openRealNameTips" /></view>
					</view>
				</view>
			</view>

			<view class="resume-tab">
				<view class="tab">
					<view class="tab-head">
						<text>手机号</text>
						<!-- <text
						:style="employee.phone!==null&&employee.phone!==''?'color: #19be6b;':'color: #ff4d4b;'">5分</text> -->
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="number" v-model="employee.phone"
								placeholder="暂未填写手机号" disabled="true" /></view>
					</view>
				</view>
			</view>

			<view class="resume-tab">
				<view class="tab">
					<view class="tab-head">
						<text>您的现居住地</text>
						<text :style="checkStr(employee.address)!='暂无'?'color: #19be6b;':'color: #ff4d4b;'">10分</text>
						<text @click="showAddressDetail = true"
							style="margin-right: 0rpx;padding-right: 0rpx;">详细住址</text>
						<uni-icons type="compose" style="margin-left: 5rpx;display: inline-block;" size="18"
							color="#909399" @click="showAddressDetail = true">
						</uni-icons>
					</view>

					<pickers @address="address">
						<view class="tab-picker">
							<text class="picker-text">{{ employee.address?employee.address:'点击选择地址' }}</text>
							<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
						</view>
					</pickers>
				</view>
			</view>

			<!-- <view v-if="isBaomuuInfo"> -->
			<view>
				<!-- 学历-选框 -->
				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head">
							<text>您的学历</text>
							<text
								:style="education!==''&&education!=='默认状态'?'color: #19be6b;':'color: #ff4d4b;'">10分</text>
						</view>
						<view class="tab-picker" @click="show = true">
							<text class="picker-text" v-if="education == ''">点击选择学历</text>
							<text class="picker-text" v-if="education !== ''">{{ education }}</text>
							<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
						</view>
					</view>
				</view>

				<!-- 工作情况-滑动选择 -->
				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head">
							<text>您做家政多少年？</text>
							<text :style="employeeInfo.workYear?'color: #19be6b;':'color: #ff4d4b;'">5分</text>
						</view>
						<view class="tab-inputbox">
							<view class="tab-input"><input class="single-input" type="number"
									v-model="employeeInfo.workYear" placeholder="暂未填写工作年限" /></view>
						</view>
						<!-- 					<view class="tab-picker" style="line-height: 60rpx;">
						<u-slider v-model="employeeInfo.workYear" block-width="40" activeColor="#F9AE3D"
							block-color="#F9AE3D" min="0" max="40" showValue>
						</u-slider>
					</view> -->
					</view>
				</view>

				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head">
							<text>您的身高？</text>
							<text style="margin-left: -10rpx;">（cm）</text>
						</view>
						<view class="tab-inputbox">
							<view class="tab-input"><input class="single-input" type="number"
									v-model="employeeInfo.height" placeholder="请填写您的身高（cm）" min="140" max="220"
									@input="numLimit" />
							</view>
						</view>
					</view>
				</view>

				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head">
							<text>您的体重？</text>
							<text style="margin-left: -10rpx;">（kg）</text>
						</view>
						<view class="tab-inputbox">
							<view class="tab-input"><input class="single-input" type="number"
									v-model="employeeInfo.weight" placeholder="请填写您的体重（kg）" min="35" max="100"
									@input="numLimit1" /></view>
						</view>
					</view>
				</view>

				<!-- 家庭情况-滑动选择 -->
				<!-- 			<view class="resume-tab">
				<view class="tab">
					<view class="tab-head"><text>您家里有几口人？</text></view>
					<view class="tab-picker" @click="show1 = true">
						<text class="picker-text" v-if="familyMember == ''">点击进行选择</text>
						<text class="picker-text" v-if="familyMember !== ''">{{ familyMember }}</text>
						<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
					</view>
				</view>
			</view> -->

				<!-- 家庭情况-选框 -->
				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head">
							<text>您的家庭成员情况？</text>
							<text @click="showFamilyRemark = true"
								style="margin-right: 0rpx;padding-right: 0rpx;">基本成员</text>
							<uni-icons type="compose" style="margin-left: 5rpx;display: inline-block;" size="18"
								color="#909399" @click="showFamilyRemark = true">
							</uni-icons>
						</view>
						<view class="tab-inputbox-high">
							<u--textarea class="multiline-input" confirmType="done" maxlength="50"
								v-model="employeeInfo.family"
								placeholder="填写您的家庭具体情况（例如：家里有几口人，几个孩子，孩子多大了，在哪读书等，10个字以上）" height="150"
								count></u--textarea>
						</view>
						<!-- <view class="tab-checkbox" v-for="(tabList, index) in familyMemberList" :key="index">
							<view class="checkbox" :class="{ activeBox: tabList.isCheck == 1 ? true : false }">
								<text @click="choiceTab(5, index)">{{ tabList.text }}</text>
							</view>
						</view> -->
					</view>
				</view>


				<!-- 婚姻情况-选择栏目 -->
				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head"><text>您的婚姻情况？</text></view>
						<view class="tab-checkbox" v-for="(tabList, index) in marriedList" :key="index">
							<view class="checkbox"
								:class="{ activeBox: employeeInfo.married == tabList.value ? true : false }">
								<text @click="choiceTab(4, index)">{{ tabList.label }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 语言-选择栏目 -->
				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head"><text>您掌握的语言？</text></view>
						<view class="tab-checkbox" v-for="(tabList, index) in languagesList" :key="index">
							<view class="checkbox" :class="{ activeBox: tabList.isCheck == 1 ? true : false }">
								<text @click="choiceTab(3, index)">{{ tabList.text }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 菜系-选择栏目 -->
				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head">
							<text>您的烹饪能力？</text>
							<text @click="showCookingDetail = true"
								style="margin-right: 0rpx;padding-right: 0rpx;">选择菜系</text>
							<uni-icons type="compose" style="margin-left: 5rpx;display: inline-block;" size="18"
								color="#909399" @click="showCookingDetail = true">
							</uni-icons>
						</view>
						<!-- 						<view class="tab-checkbox" v-for="(tabList, index) in cookingList" :key="index">
							<view class="checkbox" :class="{ activeBox: tabList.isCheck == 1 ? true : false }">
								<text @click="choiceTab(6, index)">{{ tabList.text }}</text>
							</view>
						</view> -->
						<view class="tab-inputbox">
							<view class="tab-input"><input class="single-input" type="text" v-model="baomuInfo.cooking"
									placeholder="请填写擅长的菜系" /></view>
						</view>
					</view>
				</view>

				<!-- 工作职责-选择栏目 -->
				<!-- 				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head"><text>您的工作职责？</text></view>
						<view class="tab-checkbox" v-for="(tabList, index) in workTypeList" :key="index">
							<view class="checkbox" :class="{ activeBox: tabList.isCheck == 1 ? true : false }">
								<text @click="choiceTab(0, index)">{{ tabList.text }}</text>
							</view>
						</view>
					</view>
				</view> -->

				<!-- 擅长项目-选择栏目 -->
				<view class="resume-tab">
					<view class="tab">
						<view class="tab-head"><text>您擅长做什么?</text></view>
						<view class="tab-head-smail"><text>保姆</text></view>
						<view class="flac-row" style="flex-wrap: wrap;">
							<view class="tab-checkbox" v-for="(item, index) in serverContentList" :key="index"
								v-if="item.type==0">
								<view class="checkbox" :class="{ activeBox: item.isCheck == 1 ? true : false }">
									<text @click="choiceTab(1, index)">{{ item.showText }}</text>
								</view>
							</view>
						</view>


						<view class="tab-head-smail"><text>育婴师</text></view>
						<view class="flac-row" style="flex-wrap: wrap;">
							<view class="tab-checkbox" v-for="(item, index) in serverContentList" :key="index"
								v-if="item.type==1">
								<view class="checkbox" :class="{ activeBox: item.isCheck == 1 ? true : false }">
									<text @click="choiceTab(1, index)">{{ item.showText }}</text>
								</view>
							</view>
						</view>

						<view class="tab-head-smail"><text>月嫂</text></view>
						<view class="flac-row" style="flex-wrap: wrap;">
							<view class="tab-checkbox" v-for="(item, index) in serverContentList" :key="index"
								v-if="item.type==2">
								<view class="checkbox" :class="{ activeBox: item.isCheck == 1 ? true : false }">
									<text @click="choiceTab(1, index)">{{ item.showText }}</text>
								</view>
							</view>
						</view>

						<view class="tab-head-smail"><text>护工</text></view>
						<view class="flac-row" style="flex-wrap: wrap;">
							<view class="tab-checkbox" v-for="(item, index) in serverContentList" :key="index"
								v-if="item.type==3">
								<view class="checkbox" :class="{ activeBox: item.isCheck == 1 ? true : false }">
									<text @click="choiceTab(1, index)">{{ item.showText }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 其他技能-选择栏目 -->
				<!-- <view class="resume-tab">
					<view class="tab">
						<view class="tab-head"><text>您的其他技能？</text></view>
						<view class="tab-checkbox" v-for="(tabList, index) in otherSkillsList" :key="index">
							<view class="checkbox" :class="{ activeBox: tabList.isCheck == 1 ? true : false }">
								<text @click="choiceTab(2, index)">{{ tabList.showText }}</text>
							</view>
						</view>
					</view>
				</view> -->
			</view>

			<!-- 保存按钮 -->
			<view class="btn-big"><button @click="trySave()">确 认</button></view>
		</view>

	</view>
</template>

<script>
	import pickers from "@/pages-mine/common/components/ming-picker/ming-picker.vue"
	import cameraPage from "@/pages-mine/common/components/cameraPage.vue"
	export default {
		components: {
			pickers,
			cameraPage
		},
		data() {
			return {
				// 可配置选项
				// 是否开启人脸识别相机
				isOpenCamera: true,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				roleId: uni.getStorageSync('roleId') || null,
				isGetScore: false,
				isPortraitUnqualified: false,
				showAddressDetail: false,
				showFamilyRemark: false,
				showCookingDetail: false,
				scrollTop: 0,
				show: false,
				show1: false,
				showCameraPage: false,
				showClipper: false,
				popupShow: false,
				isBaomuuInfo: true,
				memberId: null,
				baomuId: null,
				employeeId: null,
				memberName: '',
				headImg: '',
				blankImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png',
				headUplpadTips: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/head_upload_tips.png",
				baomuWorkType: '',
				changeNum: 0,
				choiceCount: 0,
				choiceCount1: 0,
				choiceCount2: 0,
				choiceCount3: 0,
				choiceCount4: 0,
				choiceCount5: 0,
				defaultIndex: [0],
				defaultIndex1: [0],
				education: '',
				familyMember: '',
				educationIndex: 0,
				familyIndex: 0,
				imageStyles: {
					width: 200,
					height: 110,
					border: {
						width: 2,
						style: 'dashed',
						radius: '100'
					}
				},
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					},
				},
				// 保姆信息
				baomuInfo: {
					baomuId: this.baomuId,
					workType: null,
					urgent: null,
					urgentPhone: null,
					urgentType: null,
					idCardTime: null,
					health: null,
					religion: null,
					zodiac: null,
					baomuId: null,
					updateTime: null,
					status: null,
					serverContent: null,
					otherSkills: null,
					introduce: null,
					languages: null
				},
				// 员工信息
				employee: {
					id: this.baomuId,
					password: null,
					realName: null,
					phone: null,
					cityId: null,
					areaId: null,
					address: null,
					addressCode: null,
					headPortrait: null,
					remark: null,
					updateDate: null,
					updatePerson: null,
					siteId: null,
					lsTime: null,
					leTime: null,
					baomuWorkType: null,
					score: null
				},
				employeeInfo: {
					employeeId: this.baomuId,
					hometown: null,
					education: null,
					familyMember: null,
					married: null
				},
				educationList: [
					[{
							value: 0,
							label: '无',
						},
						{
							value: 1,
							label: '默认状态',
						},
						{
							value: 3,
							label: '小学',
						},
						{
							value: 4,
							label: '中学',
						},
						{
							value: 5,
							label: '高中',
						},
						{
							value: 8,
							label: '中专',
						},
						{
							value: 6,
							label: '大专',
						},
						{
							value: 7,
							label: '本科及以上',
						},
						{
							value: 9,
							label: '研究生',
						}
					]
				],
				familyList: [
					[{
							value: "",
							label: '默认状态',
							index: 0
						},
						{
							value: "1",
							label: '独居',
							index: 1
						},
						{
							value: "1,2,本地",
							label: '本地，2口人',
							index: 2
						},
						{
							value: "1,2,3,本地",
							label: '本地，3口人',
							index: 3
						},
						{
							value: "1,2,3,4,本地",
							label: '本地，4口人',
							index: 4
						},
						{
							value: "1,2,3,4+,本地",
							label: '本地，4口人以上',
							index: 5
						},
						{
							value: "1,2,外地",
							label: '外地，2口人',
							index: 6
						},
						{
							value: "1,2,3,外地",
							label: '外地，3口人',
							index: 7
						},
						{
							value: "1,2,3,4,外地",
							label: '外地，4口人',
							index: 8
						},
						{
							value: "1,2,3,4+,外地",
							label: '外地，4口人以上',
							index: 9
						}
					]
				],
				familyMemberList: [{
					value: 0,
					text: '无',
					isCheck: 0
				}, {
					value: 1,
					text: '父母',
					isCheck: 0
				}, {
					value: 2,
					text: '小孩',
					isCheck: 0
				}, {
					value: 3,
					text: '配偶',
					isCheck: 0
				}],
				// 婚姻情况
				marriedList: [{
					value: 0,
					label: '未婚未育'
				}, {
					value: 1,
					label: '已婚未育'
				}, {
					value: 2,
					label: '已婚已育'
				}, {
					value: 3,
					label: '未婚已孕'
				}, {
					value: 4,
					label: '离异'
				}, {
					value: 5,
					label: '其它'
				}],
				languagesList: [{
					text: '普通话',
					isCheck: 0
				}, {
					text: '闽南话',
					isCheck: 0
				}, {
					text: '英语',
					isCheck: 0
				}, {
					text: '粤语',
					isCheck: 0
				}, {
					text: '客家话',
					isCheck: 0
				}],
				cookingList: [{
					text: '闽菜',
					isCheck: 0
				}, {
					text: '粤菜',
					isCheck: 0
				}, {
					text: '川菜',
					isCheck: 0
				}, {
					text: '浙菜',
					isCheck: 0
				}, {
					text: '鲁菜',
					isCheck: 0
				}, {
					text: '湘菜',
					isCheck: 0
				}, {
					text: '淮阳菜',
					isCheck: 0
				}, {
					text: '天津菜',
					isCheck: 0
				}, {
					text: '东北菜',
					isCheck: 0
				}],
				workTypeList: [
					// {
					// 	text: "育儿嫂",
					// 	isCheck: 0
					// },
					// {
					// 	text: "家务保姆",
					// 	isCheck: 0
					// },
					// {
					// 	text: "护工",
					// 	isCheck: 0
					// },
					// {
					// 	text: "月嫂",
					// 	isCheck: 0
					// }
					{
						text: '住家',
						isCheck: 0
					},
					{
						text: '不住家',
						isCheck: 0
					},
					{
						text: '单餐',
						isCheck: 0
					},
					{
						text: '育儿嫂',
						isCheck: 0
					},
					{
						text: '月嫂',
						isCheck: 0
					},
					{
						text: '钟点晚餐',
						isCheck: 0
					},
					{
						text: '钟点保洁',
						isCheck: 0
					},
					{
						text: '护工',
						isCheck: 0
					},
					{
						text: '陪读师',
						isCheck: 0
					},
					{
						text: '陪诊师',
						isCheck: 0
					},
					{
						text: '管家',
						isCheck: 0
					},
					{
						text: '专业清洗',
						isCheck: 0
					},
					{
						text: '专业维修',
						isCheck: 0
					},
					{
						text: '管道疏通',
						isCheck: 0
					}
				],
				serverContentList1: [{
						text: '带宝宝（带睡)',
						showText: '宝宝带睡',
						isCheck: 0
					},
					{
						text: '带宝宝（不带睡)',
						showText: '宝宝不带睡',
						isCheck: 0
					},
					{
						text: '照顾产妇',
						showText: '照顾产妇',
						isCheck: 0
					},
					{
						text: '看护病人',
						showText: '看护病人',
						isCheck: 0
					},
					{
						text: '看护老人',
						showText: '看护老人',
						isCheck: 0
					},
					{
						text: '做饭',
						showText: '做饭',
						isCheck: 0
					},
					{
						text: '纯做饭',
						showText: '纯做饭',
						isCheck: 0
					},
					{
						text: '做卫生',
						showText: '做卫生',
						isCheck: 0
					},
					{
						text: '纯做卫生',
						showText: '纯做卫生',
						isCheck: 0
					},

					// {
					// 	text: '陪诊',
					// 	showText: '陪诊',
					// 	isCheck: 0
					// },
					// {
					// 	text: '陪读',
					// 	showText: '陪读',
					// 	isCheck: 0
					// },
					// {
					// 	text: '清洗',
					// 	showText: '清洗',
					// 	isCheck: 0
					// },
					// {
					// 	text: '维修',
					// 	showText: '维修',
					// 	isCheck: 0
					// },
					// {
					// 	text: '疏通',
					// 	showText: '疏通',
					// 	isCheck: 0
					// }
				],
				serverContentList: [{
						text: '做饭',
						showText: '做饭',
						isCheck: 0,
						type: 0,
					},
					{
						text: '做卫生',
						showText: '做卫生',
						isCheck: 0,
						type: 0,
					}, {
						text: '科学喂养',
						showText: '科学喂养',
						isCheck: 0,
						type: 1,
					},
					{
						text: '早期智力开发',
						showText: '智力开发',
						isCheck: 0,
						type: 1,
					}, {
						text: '宝宝生活护理',
						showText: '生活护理',
						isCheck: 0,
						type: 1,
					}, {
						text: '辅食添加',
						showText: '辅食添加',
						isCheck: 0,
						type: 1,
					}, {
						text: '游戏互动',
						showText: '游戏互动',
						isCheck: 0,
						type: 1,
					}, {
						text: '新生儿照料',
						showText: '新生儿照料',
						isCheck: 0,
						type: 2,
					}, {
						text: '早产儿照料',
						showText: '早产儿照料',
						isCheck: 0,
						type: 2,
					}, {
						text: '剖腹产照料',
						showText: '剖腹产照料',
						isCheck: 0,
						type: 2,
					}, {
						text: '科学月子餐制作',
						showText: '月子餐制作',
						isCheck: 0,
						type: 2,
					}, {
						text: '催乳',
						showText: '催乳',
						isCheck: 0,
						type: 2,
					}, {
						text: '老人生活照料',
						showText: '老人照料',
						isCheck: 0,
						type: 3,
					}, {
						text: '看护病人',
						showText: '看护病人',
						isCheck: 0,
						type: 3,
					}, {
						text: '医院陪护',
						showText: '医院陪护',
						isCheck: 0,
						type: 3,
					},
				],
				otherSkillsList: [{
						text: '收纳',
						showText: '收纳整理',
						isCheck: 0
					},
					{
						text: '玻璃擦',
						showText: '擦玻璃',
						isCheck: 0
					},
					{
						text: '烫',
						showText: '会熨烫',
						isCheck: 0
					},
					{
						text: '电动车',
						showText: '骑电动车',
						isCheck: 0
					},
					{
						text: '开车',
						showText: '会开车',
						isCheck: 0
					},
					{
						text: '有做过高端别墅',
						showText: '高端别墅',
						isCheck: 0
					}
				]
			};
		},
		methods: {
			address(e) {
				let code = ""
				this.employee.address = ''
				for (var i = 0; i < e.value.length; i++) {
					this.employee.address += e.value[i]
					if (e.code[i] != "") {
						code = e.code[i]
					}
				}
				// 存储地址代码
				this.employee.addressCode = code
				console.log("输出：", e)
				console.log("输出地区代码：", code)
			},
			// 打开头像预览
			openImgPreview() {
				let data = []
				data.push(this.headImg)
				uni.previewImage({
					urls: data,
					current: this.headImg
				})
			},
			// 打开身份认证页面
			openIdentification() {
				uni.navigateTo({
					url: '/pages-mine/resume/certificate/identification?baomuId=' + this.baomuId,
				});
			},
			openRealNameTips() {
				this.$refs.uNotify.error('仅通过身份认证修改！')
			},
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 确认学历选择
			confirmState(index) {
				if (index == 0) {
					this.education = this.educationList[0][this.educationIndex].label
					this.employeeInfo.education = this.educationList[0][this.educationIndex].value
					this.show = false
				} else if (index == 1) {
					this.familyMember = this.familyList[0][this.familyIndex].label
					this.employeeInfo.familyMember = this.familyList[0][this.familyIndex].value
					// 同步插入家庭情况信息
					if (this.employeeInfo.family == null) {
						this.employeeInfo.family = this.familyList[0][this.familyIndex].label
					} else {
						if (this.employeeInfo.family.length < 10) {
							this.employeeInfo.family = this.familyList[0][this.familyIndex].label
						}
					}
					this.show1 = false
				}
			},
			changeHandler(e) {
				const {
					index
				} = e;
				this.educationIndex = index
			},
			changeHandler1(e) {
				const {
					index
				} = e;
				this.familyIndex = index
			},
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			// 选中多选框
			choiceTab(value, index) {
				if (value == 0) {
					if (this.workTypeList[index].isCheck == 1) {
						this.workTypeList[index].isCheck = 0
						this.choiceCount = this.choiceCount - 1
					} else {
						this.workTypeList[index].isCheck = 1
						this.choiceCount = this.choiceCount + 1
					}
				} else if (value == 1) {
					if (this.serverContentList[index].isCheck == 1) {
						this.serverContentList[index].isCheck = 0
						this.choiceCount1 = this.choiceCount1 - 1
					} else {
						this.serverContentList[index].isCheck = 1
						this.choiceCount1 = this.choiceCount1 + 1
					}
				} else if (value == 2) {
					if (this.otherSkillsList[index].isCheck == 1) {
						this.otherSkillsList[index].isCheck = 0
						this.choiceCount2 = this.choiceCount2 - 1
					} else {
						this.otherSkillsList[index].isCheck = 1
						this.choiceCount2 = this.choiceCount2 + 1
					}
				} else if (value == 3) {
					if (this.languagesList[index].isCheck == 1) {
						this.languagesList[index].isCheck = 0
						this.choiceCount3 = this.choiceCount3 - 1
					} else {
						this.languagesList[index].isCheck = 1
						this.choiceCount3 = this.choiceCount3 + 1
					}
				} else if (value == 4) {
					this.employeeInfo.married = this.marriedList[index].value
				} else if (value == 5) {
					if (this.familyMemberList[index].isCheck == 1) {
						this.familyMemberList[index].isCheck = 0
						this.choiceCount4 = this.choiceCount4 - 1
					} else {
						this.familyMemberList[index].isCheck = 1
						this.choiceCount4 = this.choiceCount4 + 1
					}
				} else if (value == 6) {
					if (this.cookingList[index].isCheck == 1) {
						this.cookingList[index].isCheck = 0
						this.choiceCount5 = this.choiceCount5 - 1
					} else {
						this.cookingList[index].isCheck = 1
						this.choiceCount5 = this.choiceCount5 + 1
					}
				}
			},
			// 打开对应的详细页面
			openMineDetail(url) {
				console.log(url);
				uni.navigateTo({
					url: url
				});
			},
			// 尝试保存
			trySave() {
				this.openCheck(0, '确定保存个人信息吗？', '内容越贴近实际越好哦～');
			},
			// 更新个人信息（更新前进行校验）
			save() {
				if (this.isBaomu) {
					if (!this.checkBoxToBaomuInfo()) {
						// this.$refs.uNotify.error('请补充完整你的个人信息哦～')
					} else if (this.checkInputData()) {
						this.checkBaomuAndInit()
						if (this.baomuId == null) {
							let timer = setTimeout(() => {
								if (this.baomuId !== null) {
									this.updateEmployee()
									this.updateEmployeeInfo()
									this.updateBaomuInfo()
								}
							}, 1500);
						} else {
							this.updateEmployee()
							this.updateEmployeeInfo()
							this.updateBaomuInfo()
						}
					}
				} else {
					// 如果是员工则创建员工（将来需要改方法）
					if (!this.checkBoxToBaomuInfo()) {
						// this.$refs.uNotify.error('请补充完整你的个人信息哦～')
					} else if (this.checkInputData()) {
						this.checkBaomuAndInit()
						if (this.baomuId == null) {
							let timer = setTimeout(() => {
								if (this.baomuId !== null) {
									this.updateEmployee()
									this.updateEmployeeInfo()
									this.updateBaomuInfo()
								}
							}, 1500);
						} else {
							this.updateEmployee()
							this.updateEmployeeInfo()
							this.updateBaomuInfo()
						}
					}
				}
				this.checkGetScore()
			},
			// 拼接多选框内容到保姆信息（工作类型和服务内容）
			checkBoxToBaomuInfo() {
				let workType = ''
				let serverContent = ''
				let otherSkills = ''
				let languages = ''
				let familyMember = ''
				let cooking = ''
				let choiceCount = this.choiceCount
				let choiceCount1 = this.choiceCount1
				let choiceCount2 = this.choiceCount2
				let choiceCount3 = this.choiceCount3
				let choiceCount4 = this.choiceCount4
				let choiceCount5 = this.choiceCount5
				if (choiceCount == 0) {
					// this.$refs.uNotify.error("请至少选择一项工作职责～")
					// return false
				} else if (choiceCount1 == 0) {
					// this.$refs.uNotify.error("请至少选择一项您擅长的～")
					// return false
				} else if (choiceCount2 == 0) {
					// this.$refs.uNotify.error("请至少选择一项其他技能～")
					// return false
				} else if (choiceCount3 == 0) {
					this.$refs.uNotify.error("请至少选择一项掌握的语言～")
					return false
				}

				for (let item of this.workTypeList) {
					if (item.isCheck == 1) {
						choiceCount = choiceCount - 1;
						if (choiceCount == 0) {
							workType = workType + item.text + ''
						} else {
							workType = workType + item.text + ','
						}
					}
				}
				for (let item1 of this.serverContentList) {
					if (item1.isCheck == 1) {
						choiceCount1 = choiceCount1 - 1;
						if (choiceCount1 == 0) {
							serverContent = serverContent + item1.text + ''
						} else {
							serverContent = serverContent + item1.text + ','
						}
					}
				}
				for (let item2 of this.otherSkillsList) {
					if (item2.isCheck == 1) {
						choiceCount2 = choiceCount2 - 1;
						if (choiceCount2 == 0) {
							otherSkills = otherSkills + item2.text + ''
						} else {
							otherSkills = otherSkills + item2.text + ','
						}
					}
				}

				this.languagesList.forEach(item => {
					if (item.isCheck == 1) {
						choiceCount3 = choiceCount3 - 1;
						if (choiceCount3 == 0) {
							languages = languages + item.text + ''
						} else {
							languages = languages + item.text + ','
						}
					}
				})

				if (this.choiceCount4 != 0 && this.familyMemberList[0].isCheck == 1) {
					this.familyMemberList[0].isCheck = 0
				}
				this.familyMemberList.forEach(item => {
					if (item.isCheck == 1) {
						choiceCount4 = choiceCount4 - 1;
						if (choiceCount4 == 0) {
							familyMember = familyMember + item.value + ''
						} else {
							familyMember = familyMember + item.value + ','
						}
					}
				})
				if (this.choiceCount4 == 0) {
					familyMember = "0"
				}
				if (familyMember.lastIndexOf(',') == familyMember.length - 1) {
					familyMember = familyMember.substring(0, familyMember.length - 1)
				}

				// this.cookingList.forEach(item => {
				// 	if (item.isCheck == 1) {
				// 		choiceCount5 = choiceCount5 - 1;
				// 		if (choiceCount5 == 0) {
				// 			cooking = cooking + item.text + ''
				// 		} else {
				// 			cooking = cooking + item.text + ','
				// 		}
				// 	}
				// })

				this.baomuInfo.workType = workType || null
				this.baomuInfo.serverContent = serverContent
				this.baomuInfo.otherSkills = otherSkills
				this.baomuInfo.languages = languages
				// this.baomuInfo.cooking = cooking
				this.employeeInfo.familyMember = familyMember
				return true
			},
			// 检查输入的内容
			checkInputData() {
				if (this.employee.realName == "" || this.employee.realName == null) {
					this.$refs.uNotify.error('请填写你的姓名哦～')
					return false
				}
				if (this.employee.phone == "" || this.employee.phone == null) {
					this.$refs.uNotify.error('请填写你的手机号哦～')
					return false
				}
				if (this.employee.address == "" || this.employee.address == null) {
					this.$refs.uNotify.error('请补充完整你的现居地哦～')
					return false
				}
				if (this.educationIndex == 1) {
					this.$refs.uNotify.error('请补充完整你的学历哦～')
					return false
				}

				if (!this.employeeInfo.family || this.employeeInfo.family.length < 10) {
					this.$refs.uNotify.error('请补充完整你的家庭情况哦（10字以上）～')
					return false
				}

				// if (!this.employeeInfo.height || !this.employeeInfo.weight) {
				// 	this.$refs.uNotify.error('请补充完整你的身高体重哦～')
				// 	return false
				// }

				// let height = this.employeeInfo.height || 160
				// let weight = this.employeeInfo.weight || 60
				// if (height < 140 || height > 220) {
				// 	this.$refs.uNotify.warning('身高不在填写范围内（140～220cm）')
				// 	return false
				// }
				// if (weight < 35 || weight > 100) {
				// 	this.$refs.uNotify.warning('体重不在填写范围内（35～100kg）')
				// 	return false
				// }

				// if (this.familyMember == "" || this.familyMember == "默认状态") {
				// 	this.$refs.uNotify.error('请补充完整你的家庭情况哦～')
				// 	return false
				// }
				return true
			},
			// 格式化家庭成员信息
			formatFamily() {
				// 旧版滑动选择
				// for (let item of this.familyList[0]) {
				// 	if (item.value == this.employeeInfo.familyMember) {
				// 		this.familyIndex = item.index
				// 		this.familyMember = item.label
				// 		this.defaultIndex1 = []
				// 		this.defaultIndex1.push(item.index)
				// 	}
				// }

				this.choiceCount4 = 0
				for (let item of this.familyMemberList) {
					if (this.employeeInfo.familyMember.includes(item.value.toString())) {
						item.isCheck = 1
						this.choiceCount4++
					}
				}
			},
			// 格式化学历信息
			formatEducation() {
				for (let i = 0; i < this.educationList[0].length; i++) {
					let item = this.educationList[0][i]
					if (item.value == this.employeeInfo.education) {
						this.educationIndex = i
						this.education = item.label
						this.defaultIndex = []
						this.defaultIndex.push(i)
					}
				}
			},
			// 格式化保姆信息到多选框
			formatBaomuInfo(workType, serverContent, otherSkills, languages, cooking) {
				let choiceCount = 0
				let choiceCount1 = 0
				let choiceCount2 = 0
				let choiceCount3 = 0
				let choiceCount5 = 0
				if (workType) {
					let array = workType.split(",")
					array.forEach(item => {
						this.workTypeList.forEach(item1 => {
							if (item == item1.text) {
								item1.isCheck = 1
								choiceCount = choiceCount + 1
							}
						})
					})
					this.choiceCount = choiceCount
				}

				if (serverContent) {
					for (let item of this.serverContentList) {
						if (serverContent.includes(item.text)) {
							item.isCheck = 1;
							choiceCount1 = choiceCount1 + 1;
						}
					}
					this.choiceCount1 = choiceCount1
				}

				if (otherSkills) {
					for (let item of this.otherSkillsList) {
						if (otherSkills.includes(item.text)) {
							item.isCheck = 1;
							choiceCount2 = choiceCount2 + 1;
						}
					}
					this.choiceCount2 = choiceCount2
				}

				if (languages) {
					for (let item of this.languagesList) {
						if (languages.includes(item.text)) {
							item.isCheck = 1;
							choiceCount3 = choiceCount3 + 1;
						}
					}
					this.choiceCount3 = choiceCount3
				}

				// if (cooking) {
				// 	for (let item of this.cookingList) {
				// 		if (cooking.includes(item.text)) {
				// 			item.isCheck = 1;
				// 			choiceCount5 = choiceCount5 + 1;
				// 		}
				// 	}
				// 	this.choiceCount5 = choiceCount5
				// }
			},
			closeCameraPage(flag) {
				console.log("关闭人脸相机！")
				this.showCameraPage = flag
			},
			// 提交照片
			submitCameraPhoto(tempFilePaths) {
				const url = 'https://api.xiaoyujia.com/system/imageUpload'
				uni.uploadFile({
					url: url,
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						route: 'userPhotos'
					},
					dataType: 'json',
					success: res => {
						this.headImg = tempFilePaths[0]
						let result = JSON.parse(res.data)
						this.employee.headPortrait = result.data
						this.isPortraitUnqualified = false
						this.employee.remark = ""
						this.showCameraPage = false
						console.log('上传图片后返回文件地址:', result.data)
						this.updateEmployee()
					}
				});
			},
			// 上传头像
			uploadHeadPortrait(index) {
				if (this.employee.state == 1 && this.employee.putTime && !this.roleId) {
					// return this.$refs.uNotify.warning('您已成功入驻并通过平台认证，头像暂时不支持修改哦，可以开始接单啦！')
				}
				this.popupShow = false

				// #ifdef  MP-WEIXIN
				// 打开人脸识别相机
				if (index == 0 && this.isOpenCamera) {
					this.showCameraPage = true
					return
				}
				// #endif

				const url = 'https://api.xiaoyujia.com/system/imageUpload';
				uni.chooseImage({
					success: chooseImageRes => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {
								route: 'userPhotos'
							},
							dataType: 'json',
							success: res => {
								this.headImg = tempFilePaths[0]
								let result = JSON.parse(res.data)
								this.employee.headPortrait = result.data
								this.isPortraitUnqualified = false
								this.employee.remark = ""
								console.log('上传图片后返回文件地址:', result.data)
								this.updateEmployee()
							}
						});
					}
				});
			},
			// 更新员工
			updateEmployee() {
				this.$set(this.employee, 'id', this.baomuId);
				this.http({
					url: 'updateBaomu',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: this.employee,
					success: res => {
						if (res.code == 0) {
							uni.setStorageSync('emeployHeadImg', this.employee.headPortrait)
							console.log('输出头像:', uni.getStorageSync('emeployHeadImg'))
							this.$refs.uNotify.success('个人信息更新成功！')
							this.changeNum = 0
							uni.setStorageSync("isUpdateResume", true)
							// let timer = setTimeout(() => {
							// 	return uni.navigateBack()
							// }, 1500);
						} else {
							this.$refs.uNotify.error('更新员工失败，请求错误！' + res.msg)
						}
					}
				});
			},
			// 更新员工信息
			updateEmployeeInfo() {
				this.$set(this.employeeInfo, 'employeeId', this.baomuId)
				this.http({
					url: 'updateEmployeeInfo',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					hideLoading: true,
					method: 'POST',
					data: this.employeeInfo,
					success: res => {
						if (res.code == 0) {
							if (res.data) {
								this.changeNum = 0
							} else {}
						} else {
							this.$refs.uNotify.error('更新员工信息失败，请求错误！' + res.msg)
						}
					}
				});
			},
			// 更新保姆信息
			updateBaomuInfo() {
				if (this.checkBoxToBaomuInfo()) {
					this.$set(this.baomuInfo, 'baomuId', this.baomuId)
					let cooking = this.baomuInfo.cooking
					if (cooking) {
						if (cooking.charAt(cooking.length - 1) == ',') {
							this.baomuInfo.cooking = cooking.substring(0, cooking.length - 1)
						}
						this.baomuInfo.cooking = this.baomuInfo.cooking.replace('null', '')
					}

					this.http({
						url: 'updateBaomuInfo',
						header: {
							'content-type': 'application/json;charset=UTF-8'
						},
						method: 'POST',
						hideLoading: true,
						data: this.baomuInfo,
						success: res => {
							if (res.code == 0) {
								this.changeNum = 0
							} else {
								this.$refs.uNotify.error('更新保姆信息失败，返回错误！' + res.msg)
							}
						}
					});
				}
			},
			// 检查是否得分
			checkGetScore() {
				let isGetScore = true
				if (this.employee.headPortrait == null || this.employee.headPortrait == "") {
					isGetScore = false
				}
				// if (this.employee.realName == null || this.employee.realName == "") {
				// 	isGetScore = false
				// }
				if (this.employee.address == null || this.employee.address == "") {
					isGetScore = false
				}
				if (this.educationIndex == 1) {
					isGetScore = false
				}
				if (this.employeeInfo.workYear == null) {
					isGetScore = false
				}
				let remark = this.employee.remark || ""
				if (remark.includes("头像不合格")) {
					isGetScore = false
					this.isPortraitUnqualified = true
				}


				this.isGetScore = isGetScore
			},
			// 获取保姆详细信息成功
			getBaomuDetail() {
				if (this.baomuId !== null) {
					this.http({
						url: 'getBaomuDetail',
						method: 'GET',
						hideLoading: true,
						path: this.baomuId,
						success: res => {
							if (res.code == 0) {
								let baomuDetail = res.data

								this.employee = baomuDetail.employee
								this.headImg = this.employee.headPortrait
								this.employeeInfo = baomuDetail.employeeInfo
								this.formatFamily()
								this.formatEducation()

								this.baomuInfo = baomuDetail.baomuInfo
								this.formatBaomuInfo(this.baomuInfo.workType, this.baomuInfo.serverContent,
									this.baomuInfo.otherSkills, this.baomuInfo.languages, this.baomuInfo
									.cooking)

								this.checkGetScore()

								console.log('获取保姆详细信息成功-请求成功！')
							} else {
								console.log('获取保姆详细信息成功-请求失败！')
							}
						}
					});
				}
			},
			// 判断是否存在保姆信息（不存在则初始化）
			checkBaomuAndInit() {
				if (this.baomuId == null) {
					this.http({
						url: 'getBaomuCollectByMemberId',
						method: 'POST',
						hideLoading: true,
						data: {
							memberId: this.memberId,
							nearStoreId: uni.getStorageSync("nearStoreId") || -1
						},
						success: res => {
							if (res.code == 0) {
								this.baomuId = res.data.baomuId
								uni.setStorageSync('baomuId', this.baomuId)
								console.log('通过会员ID获取保姆关联信息-成功！')
								console.log('初始化的保姆ID为' + this.baomuId)
							} else {
								this.$toast.toast('初始化员工信息失败，请稍候再试！' + res.msg)
							}
						}
					});
				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：保存个人信息
				if (this.checkType == 0) {
					this.save()
				}
			},
			// 检查员工身份
			checkEmployee() {
				if (this.baomuId !== uni.getStorageSync("baomuId")) {
					this.isBaomuuInfo = false
				}
				this.isEmployee = uni.getStorageSync("isEmployee") == true ? true : false
				this.isBaomu = uni.getStorageSync("isBaomu") == true ? true : false
				console.log("是否员工：", this.isEmployee, "是否保姆：", this.isBaomu)
				if (this.isEmployee) {
					if (this.isBaomu) {
						this.isBaomuuInfo = true
					} else {
						this.isBaomuuInfo = false
					}
				}
			},
			getMemberInfor() {
				let memberId = uni.getStorageSync('memberId')
				let baomuId = uni.getStorageSync('baomuId')
				baomuId = baomuId == '' ? null : baomuId;
				this.memberId = memberId
				// 如果在上一个页面没有获取参数，则从缓存中取值
				if (this.baomuId == undefined || this.baomuId == null) {
					this.baomuId = baomuId
				}
			},
			// 用户信息初始化
			orginInfo() {
				this.checkEmployee()
				this.getMemberInfor()
				this.getBaomuDetail()
			}
		},
		onLoad(options) {
			this.baomuId = JSON.parse(options.baomuId)
			this.orginInfo()
		},
		created() {}
	};
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/resume-tab.scss";
	@import "@/pages-mine/common/css/tab-menu.scss";


	page {
		height: 100%;
		background-color: #ffffff;
	}

	// 提示弹窗标题
	.popup-title {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;

		text {
			display: block;
			text-align: center;
			font-weight: bold;
			font-size: 40rpx;
		}
	}

	// 提示图片
	.popup-img {
		width: 100%;
		height: auto;

		img {
			display: block;
			width: 600rpx;
			height: auto;
			margin: 40rpx auto 0 auto;
		}
	}

	// 上传提示
	.popup-tips {
		width: 80%;
		line-height: 60rpx;
		margin: 20rpx auto 0 auto;

		text {
			display: block;
			font-size: 36rpx;
			text-align: center;
		}
	}

	.img-upload {
		width: 200rpx;
		height: 200rpx;
		margin: 100rpx auto;
	}

	.head-img {
		display: block;
		width: 200rpx;
		height: 200rpx;
		border-radius: 50%;
	}
</style>