<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 须知提示弹窗 -->
		<u-popup :show="popupShow" mode="bottom" @close="popupShow = false">
			<view class="filter-title" @click="popupShow = false">
				<text style="font-weight: bold;">考试须知</text>
			</view>
			<view class="filter-content" style="height: 500rpx;">
				<view class="filter-content-text">
					<text v-for="(item,index) in attentionList" :key="index">{{item}}</text>
				</view>
			</view>
		</u-popup>

		<!-- 考试基本信息 -->
		<view class="exam-tab">
			<view class="tab flac-col">
				<view class="tab-head">
					<text>{{(exam.examTitle)}}</text>
				</view>
				<view class="tab-head-smail flac-row">
					<uni-icons type="notification" size="24" style="margin-right: 20rpx;">
					</uni-icons>
					<text>考试限时：{{exam.examDuration}}分钟</text>
				</view>

				<view class="tab-head-smail flac-row">
					<uni-icons type="calendar" size="24" style="margin-right: 20rpx;">
					</uni-icons>
					<text>合格：{{exam.examPassScore}}分</text>
				</view>

				<view class="tab-head-smail flac-row">
					<uni-icons type="medal" size="24" style="margin-right: 20rpx;">
					</uni-icons>
					<text>满分：{{exam.examScore}}分</text>
				</view>

			</view>
		</view>

		<!-- 考试说明 -->
		<view class="exam-tab">
			<view class="tab flac-col">
				<view class="tab-head">
					<text>考试说明</text>
				</view>
				<view class="tab-head-smail">
					<text style="font-size: 32rpx;line-height: 50rpx;">{{checkStr(exam.examRemark)}}</text>
				</view>
				<view class="tab-head-smail" v-if="recordCourseId||recordCourseIdList" @click="openCourse()">
					<text
						style="font-size: 32rpx;line-height: 50rpx;color: #ff4d4b;">*该考试已关联【学习中心】课程，若未通过将回退部分学习进度，待重新学习后才可补考！</text>
				</view>

				<view class="tab-head-smail" style="text-align: right;color: #ff4d4b;margin-top: 20rpx;"
					@click="popupShow = true">
					<text class="fb" style="font-size: 32rpx;line-height: 40rpx;">*阅读考试须知</text>
				</view>
				<u-gap height="10"></u-gap>
			</view>
		</view>

		<!-- 排行榜 -->
		<view class="exam-tab">
			<view class="tab" style="padding-bottom: 100rpx;">
				<view class="tab-head-tips">
					<text @longpress="openRank(1)">排行榜</text>
					<text @click="openRank(0)"
						style="margin-right: 0rpx;">{{rankNum!=-1&&recordState==2?"我的排名 "+rankNum:"查看排行榜 "}}</text>
					<uni-icons @click="openRank(0)" type="forward" size="18" color="#909399"
						style="padding: 0 0rpx;"></uni-icons>
				</view>

				<!-- 会员信息 -->
				<view class="tab-rank" v-if="examRank.length>=3&&!showRealName">
					<view class="rank-img">
						<img :src="checkImg(examRank[1].headImg)!=''?examRank[1].headImg : blankImg"
							style="margin-top: 40rpx;">
						<img :src="rankImg[1]">
						<view class="rank-img-text">
							<text>{{checkName(examRank[1].name)}}</text>
							<text>{{examRank[1].recordScore}}</text>
						</view>
					</view>
					<view class="rank-img">
						<img :src="checkImg(examRank[0].headImg)!=''?examRank[0].headImg : blankImg"
							style="width: 160rpx;height: 160rpx;">
						<img :src="rankImg[0]" style="width: 182rpx;height: 182rpx;margin: -170rpx 37rpx;">
						<view class="rank-img-text">
							<text>{{checkName(examRank[0].name)}}</text>
							<text>{{examRank[0].recordScore}}</text>
						</view>
					</view>
					<view class="rank-img">
						<img :src="checkImg(examRank[2].headImg)!=''?examRank[2].headImg : blankImg"
							style="margin-top: 40rpx;">
						<img :src="rankImg[2]">
						<view class="rank-img-text">
							<text>{{checkName(examRank[2].name)}}</text>
							<text>{{examRank[2].recordScore}}</text>
						</view>

					</view>
				</view>

				<!-- 员工信息 -->
				<view class="tab-rank" v-if="examRank.length>=3&&showRealName">
					<view class="rank-img">
						<img :src="checkImg(examRank[1].headPortrait)!=''?examRank[1].headPortrait : blankImg"
							style="margin-top: 40rpx;">
						<img :src="rankImg[1]">
						<view class="rank-img-text">
							<text>{{checkName(examRank[1].realName)}}</text>
							<text>{{examRank[1].recordScore}}</text>
						</view>
					</view>
					<view class="rank-img">
						<img :src="checkImg(examRank[0].headPortrait)!=''?examRank[0].headPortrait : blankImg"
							style="width: 160rpx;height: 160rpx;">
						<img :src="rankImg[0]" style="width: 182rpx;height: 182rpx;margin: -170rpx 37rpx;">
						<view class="rank-img-text">
							<text>{{checkName(examRank[0].realName)}}</text>
							<text>{{examRank[0].recordScore}}</text>
						</view>
					</view>
					<view class="rank-img">
						<img :src="checkImg(examRank[2].headPortrait)!=''?examRank[2].headPortrait : blankImg"
							style="margin-top: 40rpx;">
						<img :src="rankImg[2]">
						<view class="rank-img-text">
							<text>{{checkName(examRank[2].realName)}}</text>
							<text>{{examRank[2].recordScore}}</text>
						</view>

					</view>
				</view>

				<u-empty v-if="examRank.length<3" text="暂无排名" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</view>
		</view>

		<u-gap height="200"></u-gap>

		<view class="btn-bottom-fixed">
			<button v-if="item.value==recordState" v-for="(item, index) in recordStateList" :key="index"
				@click="openDetail()">{{item.text}}</button>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 可设置
				// 显示真名
				showRealName: false,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				popupShow: false,

				service: "https://biapi.xiaoyujia.com/",
				rankImg: [
					"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_img1.png",
					"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_img2.png",
					"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_img3.png"
				],
				attentionList: [
					"1.开始考试后，不得中途退出，否则将视为放弃，并重新答题！",
					"2.考试时间到，不得继续答题，否则将自动交卷，请注意把握时间！",
					"3.诚信考试，多次切屏将视为违规，并自动交卷结束考试！"
				],
				headImg: '',
				blankImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png',
				rankNum: -1,
				recordId: 0,
				memberId: null,
				recordStateList: [{
						text: "开始考试",
						value: 0
					},
					{
						text: "继续考试",
						value: 1
					},
					{
						text: "查看结果",
						value: 2
					},
					{
						text: "继续重考",
						value: 3
					},
					{
						text: "查看结果",
						value: 4
					}
				],
				searchCondition: {
					examId: null,
					memberId: null,
					recordState: 2,
					orderBy: "recordScore DESC",
					current: 1,
					size: 10
				},
				recordState: 0,
				examId: 1,
				exam: {},
				examRank: [],
				recordCourseId: null,
				recordCourseIdList: null
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 打开详情
			openDetail() {
				let index = this.recordState
				switch (index) {
					case 0:
						this.openCheck(0, "即将开始考试", "请确保已阅读【考试须知】！确定开始考试吗？")
						break;
					case 1:
						this.openCheck(0, "继续考试", "确定继续考试吗？")
						break;
					case 2:
						uni.navigateTo({
							url: "/pages-mine/exam/exam-result?id=" + this.recordId
						})
						break;
					case 3:
						this.openCheck(0, "重新考试", "确定重新开始考试吗？")
						break;
					case 4:
						uni.navigateTo({
							url: "/pages-mine/exam/exam-result?id=" + this.recordId
						})
						break;
				}
			},
			// 打开课程
			openCourse() {
				uni.navigateTo({
					url: '/pages-mine/studyCenter/course-detail?id=' + this.recordCourseId
				})
			},
			openRank(index) {
				if (this.recordState != 2 && index == 0) {
					this.$refs.uNotify.error("考试结束后才可查看完整榜单哦！")
					return
				}

				uni.navigateTo({
					url: "/pages-mine/exam/exam-rank?id=" + this.examId + "&examTitle=" + this.exam.examTitle
				})
			},
			// 格式化字符
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无内容"
				} else {
					return str
				}
			},
			checkName(str) {
				if (str == null || str == "") {
					return "匿名用户"
				} else {
					let max = 8
					if (str.length > max) {
						str = str.substring(0, max) + '...'
					}
					return str
				}
			},
			checkImg(str) {
				if (str == null || str == "") {
					return ""
				} else {
					return str
				}
			},
			// 获取考试排行榜
			getExamRank() {
				this.searchCondition.examId = this.examId
				this.searchCondition.memberId = this.memberId
				this.http({
					url: 'getUnionExamRank',
					method: 'POST',
					hideLoading: true,
					data: this.searchCondition,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.examRank = res.data.records
							this.rankNum = this.examRank[0].rankNum
						} else {
							console.log('获取考试排行榜-返回错误！' + res.msg)
						}
					}
				});
			},
			// 获取考试列表
			listUnionExamRecord() {
				this.http({
					url: 'listUnionExamRecord',
					method: 'POST',
					hideLoading: true,
					data: {
						id: this.recordId,
						current: 1,
						size: 10
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.exam = res.data[0]
							this.examId = this.exam.examId
							this.recordState = this.exam.recordState
							this.getExamRank()
							this.showRealName = this.exam.showRealName == 1 ? true : false
							this.recordCourseId = this.exam.recordCourseId
							this.recordCourseIdList = this.exam.recordCourseIdList
						} else {
							console.log('获取考试列表-返回错误！' + res.msg)
						}
					}
				});
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					uni.navigateTo({
						url: "/pages-mine/exam/exam?id=" + this.recordId
					})
				}
			},
		},
		onLoad(options) {
			this.recordId = options.id || -1
			this.memberId = uni.getStorageSync("memberId")

			// 测试时加上
			// this.recordId = 5
			// this.service = "http://localhost:8063/"

			this.listUnionExamRecord()
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/exam.scss";
</style>