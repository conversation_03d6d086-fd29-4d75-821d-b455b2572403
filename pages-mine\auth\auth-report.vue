<template>
	<!-- 背景色 -->
	<view class="bj" style="height: 300rpx;" :style="backgroundImg">

		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="100"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 权益提示弹窗 -->
		<u-popup :show="popupShow" mode="bottom" @close="popupShow = false">
			<view class="popup-title">
				<text>权益内容</text>
			</view>
			<view class="popup-tips">
				<text>{{equityText}}</text>
			</view>
			<view class="popup-img">
				<img :src="equityImg" alt="" mode="widthFix">
			</view>
			<view class="popup-tips">
				<text style="color: #909399;">{{equityContent}}</text>
			</view>
			<view class="btn-big" style="padding-bottom: 0rpx;"><button style="margin: 40rpx auto 60rpx auto;"
					@click="popupShow=false">我知道了</button></view>
		</u-popup>

		<view class="service cf">
			<view style="margin: auto 20rpx;">规则说明</view>
			<u-icon name="server-fill" color="#fff" size="25"></u-icon>
		</view>
		<!-- 等级卡片 -->
		<view class="section1" :style="levelBackgroundImg">
			<!-- 进度条 -->
			<view class="progress lh36" style="">
				<view class="f12"><text class="f16"
						style="margin-right: 10rpx;">{{exValue(0)}}</text>升级还需要{{exValue(1)}}经验值
				</view>
				<u-line-progress :percentage="exValue(2)" :showText="true" height="8"></u-line-progress>
				<view class="section1_text" style="">
					<text>{{levelList[authReport.level].text}}</text>
					<text>{{levelList[authReport.level+1].text}}</text>
				</view>
			</view>
			<u-button :customStyle="btnUpStyle" text="去升级" shape="circle" @click="upgrade()"></u-button>
		</view>
		<!-- 内容区 -->
		<view class="section2 w95" style="padding-bottom: 30rpx;">
			<!-- 新增权益 -->
			<view class="section2_add bacf" v-if="authReport.level>2&&workTypeId==0">
				<view class="section2_title w9 mg-at lh45">
					<view class="f18 fb">比{{levelList[authReport.level-1].text}}等级<text
							:style="fontStyle">{{EquityAdd}}</text>权益</view>
					<view class="" @click="openAll(1)">查看全部></view>
				</view>
				<view class="w95 mg-at" v-for="(item,index) in list1" :key="index">
					<view class="section2_list">
						<view class="" style="display: flex;align-items: center;">
							<u-image :src="item.equityImg" width="80rpx" height="80rpx"
								customStyle="margin: auto 20rpx;">
							</u-image>
							<view class="" style="width: 380rpx;">
								<view class="f16 fb">{{item.equityTitle}}</view>
								<view class="f12" v-if="item.equityText!=null">{{item.equityText}}</view>
							</view>
						</view>
						<u-button :customStyle="btnStyle" text="查看详情" size="mini" shape="circle"
							@click="openItem(0,index)">
						</u-button>
					</view>
				</view>
			</view>

			<!-- 课程学习 -->
			<view class="section2_add bacf" v-if="workTypeId!=0">
				<view class="section2_title w9 mg-at lh45">
					<view class="f18 fb">课程学习内容</view>

					<view class="" @click="openAll(0)">查看全部></view>
				</view>

				<!-- 			<view style="display: flex;flex-direction: row;">
					<view v-for="(item,index) in list1" :key="index" style="width: 33%;height: 200rpx;"
						@click="openItem(1,index)">
						<img :src="item.equityImg" style="margin: ;" shape="circle"></img>
						<view class="f14 lh32 text-c">{{item.equityTitle}}</view>
					</view>
				</view> -->

				<view class="tab-content" style="display: flex;flex-wrap: wrap;">
					<view class="icon-content" v-for="(item,index) in list1" :key="index" @click="openItem(1,index)">
						<img :src="item.equityImg"></img>
						<text>{{item.equityTitle}}</text>
					</view>
				</view>
			</view>

			<!-- 全部权益 -->
			<view class="section2_all w10 bacf">
				<view class="section2_title w9 mg-at lh45">
					<view class="f18 fb" v-if="workTypeId==0">当前等级可<text :style="fontStyle">{{EquityAll}}</text>权益
					</view>
					<view class="f18 fb" v-if="workTypeId!=0&&authReport.level!=6">证书获取</view>

					<view class="" @click="openAll(0)">查看全部></view>
				</view>

				<view class="tab-content" style="display: flex;flex-wrap: wrap;">
					<view class="icon-content" v-for="(item,index) in list2" :key="index" @click="openItem(1,index)">
						<img :src="item.equityImg"></img>
						<text>{{item.equityTitle}}</text>
					</view>
				</view>
			</view>
			<!-- 等级提升区（三星，四星才有） -->
			<view class="section2_promote w10 bacf" v-if="authReport.level<4">
				<view class="section2_title w9 mg-at lh45" style="border-bottom: 2rpx solid #eee;">
					<view class="f18 fb">做任务提升等级</view>
				</view>
				<view class="w9 mg-at lh45" v-for="(item,index) in taskList" :key="index">
					<view class="promote_con">
						<view class="f16" style="width: 500rpx;">{{item.taskName}}</view>
						<u-button customStyle=";width:130rpx;height: 50rpx;color: #9b8241;background-color: #fae9cf;"
							text="去完成" size="mini" shape="circle" v-if="item.state==0" @click="completeTask(0,index)">
						</u-button>
						<u-button customStyle=";width:130rpx;height: 50rpx;color: #9b8241;background-color: #fae9cf;"
							text="领取奖励" size="mini" shape="circle" v-if="item.state==1" @click="completeTask(1,index)">
						</u-button>
						<u-button customStyle=";width:130rpx;height: 50rpx;color: #9b8241;background-color: #fae9cf;"
							text="已完成" size="mini" shape="circle" v-if="item.state==2"></u-button>
					</view>
				</view>
			</view>
			<!-- 任务提示区（三星，四星才有） -->
			<view id="task" class="w10 f18 fb lh28" style="margin: auto 20rpx;" v-if="authReport.level<4">
				任务完成任意2个可晋升{{levelList[authReport.level+1].text}}{{job}}，<text
					class="textColor">{{levelList[authReport.level+1].text}}</text>等级<text
					class="textColor">{{EquityAll}}</text>，比{{levelList[authReport.level].text}}等级<text
					class="textColor">{{EquityAddMoney}}</text>。当前等级薪资鉴定为<text class="textColor">{{salary}}元</text>。
			</view>
			<view id="task" class="w10 f18 fb lh28" style="margin: auto 20rpx;" v-if="authReport.level==4">
				您已达到
				<text class="textColor">{{levelList[authReport.level].text}}{{job}}</text>等级，当前等级薪资鉴定为<text
					class="textColor">{{salary}}元</text>。
			</view>
			<!-- 海报区(翡翠及以上有) -->
			<view class="poster-img" v-if="authReport.level>=4">
				<img :src="posterSrc" mode="widthFix" />
			</view>

			<!-- 鉴定结果 -->
			<view v-if="employeeState!=1"
				style="margin: 40rpx 0;padding: 20rpx 0;border-radius: 20rpx;background-color: #ffffff;">
				<view class="tab">
					<view class="tab-head-smail" style="font-weight: bold;text-align: center;"><text>工作技能鉴定记录</text>
					</view>
					<view class="tab-head-smail"><text>鉴定人：{{updaterName}}（{{updaterNo}}）</text></view>
					<view class="tab-head-smail"><text>鉴定等级：{{levelName}}</text></view>
					<view class="tab-head-smail"><text>鉴定工资：{{salary}}</text></view>
					<view class="tab-head-smail"><text>鉴定时间：{{updateTime}}</text></view>
				</view>
			</view>
			<view v-if="employeeState==1" style="margin: 40rpx 20rpx;font-size: 30rpx;text-align: right;">
				<text class="textColor">*等级权益已确认</text>
			</view>

			<view v-if="isSendSms&&employeeState!=1&&isAdmin" style="margin: 0 20rpx;">
				<text>*员工收到的短信验证码：{{smsCodeTips}}（5分钟内有效）</text>
			</view>
			<!-- 短信验证码输入框 -->
			<view class="tab-inputbox" v-if="isSendSms&&employeeState!=1" style="margin-top: 80rpx;">
				<view class="tab-input">
					<input class="single-input" type="number" maxlength="6" v-model="smsCode" @input="checkSms"
						placeholder="输入员工收到的6位短信验证码" />
				</view>
			</view>
			<!-- 确认按钮 -->
			<view class="btn-big" v-if="employeeState!=1">
				<button @click="tryConfirmReport()"
					:style="!isSendSms?'background-color:#1e1848;':'background-color:#909399;color:#fff'">{{isSendSms?(smsTimeLimit-smsTime)+'s':'确认等级权益'}}</button>
			</view>

			<view style="margin: 40rpx 20rpx;font-size: 30rpx;text-align: right;" @click="openCert">
				<text class="textColor">*下载证书</text>
			</view>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 可设置
				// 短信重新发送时间（秒）
				smsTimeLimit: 60,
				// 员工入驻成功-订阅消息模板
				templateId: "XSPkvv4X5gph2Pny0dzeFz7Qwt2mffO6q6yzzAARI-w",

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					},
				},

				isSendSms: false,
				smsTime: 60,
				smsCode: '',
				smsCodeTips: '',

				equityText: '',
				equityContent: '',
				equityImg: '',

				isAdmin: false,
				popupShow: false,
				employee: {},
				baomuId: null,
				memberId: null,
				memberName: "",
				employeeState: 2,
				levelName: '',
				updaterNo: '',
				updaterName: '',
				updateTime: '',
				salary: '',
				authReport: {
					level: 0
				},
				// 证件照片
				certificate: {
					title: "技能证书",
					employeeId: this.baomuId,
					certificateImg: null,
					certificateType: 23,
				},
				employeeEquity: [],
				posterSrc: '',
				backgroundImg: '',
				levelBackgroundImg: '',
				fontStyle: '',
				btnStyle: '',
				btnUpStyle: '',
				EquityAll: '',
				EquityAdd: '',
				EquityAddMoney: '',
				show: true,
				screenHeight: 0,
				score: 0,
				zjy: 114,
				jy: 36,
				num: 12,
				job: '保姆',
				money: 1000,
				workTypeId: 0,
				levelList: [{
						value: 0,
						text: "未认证",
						type: "info"
					}, {
						value: 1,
						text: "不通过",
						type: "info"
					},
					{
						value: 2,
						text: "三星",
						type: "danger",
						fontStyle: "color: #ccae55;",
						btnStyle: "width:130rpx;height: 50rpx;color: #a68c4e;background-color: #fbe9cf;",
						btnUpStyle: "position: absolute;width: 150rpx;height: 50rpx;color: #caab4f;top: 72%;left: 65.5%",
						exValue: 800,
					}, {
						value: 3,
						text: "四星",
						type: "warning",
						fontStyle: "color: #d07016;",
						btnStyle: "width:130rpx;height: 50rpx;color: #fff;background-color: #f8c529;",
						btnUpStyle: "position: absolute;width: 150rpx;height: 50rpx;color: #caab4f;top: 72%;left: 65.5%",
						exValue: 1200,
					}, {
						value: 4,
						text: "五星",
						type: "success",
						fontStyle: "color: #439a88;",
						btnStyle: "width:130rpx;height: 50rpx;color: #fff;background-color: #95cfc8;",
						btnUpStyle: "position: absolute;width: 150rpx;height: 50rpx;color: #4b7e73;top: 72%;left: 65.5%",
						exValue: 1600,
					}, {
						value: 5,
						text: "六星",
						type: "",
						fontStyle: "color: #3f9783;",
						btnStyle: "width:130rpx;height: 50rpx;color: #fff;background-color: #95cfc8;",
						btnUpStyle: "",
						exValue: 2000,
					},
					{
						value: 6,
						text: "一星",
						type: "danger",
						fontStyle: "color: #ccae55;",
						btnStyle: "width:130rpx;height: 50rpx;color: #a68c4e;background-color: #fbe9cf;",
						btnUpStyle: "position: absolute;width: 150rpx;height: 50rpx;color: #caab4f;top: 72%;left: 65.5%",
						exValue: 0,
					},
					{
						value: 7,
						text: "二星",
						type: "danger",
						fontStyle: "color: #ccae55;",
						btnStyle: "width:130rpx;height: 50rpx;color: #a68c4e;background-color: #fbe9cf;",
						btnUpStyle: "position: absolute;width: 150rpx;height: 50rpx;color: #caab4f;top: 72%;left: 65.5%",
						exValue: 400,
					},
					{
						value: 8,
						text: "预留",
						type: "danger",
						fontStyle: "color: #ccae55;",
						btnStyle: "width:130rpx;height: 50rpx;color: #a68c4e;background-color: #fbe9cf;",
						btnUpStyle: "position: absolute;width: 150rpx;height: 50rpx;color: #caab4f;top: 72%;left: 65.5%",
						exValue: 0,
					}
				],
				list1: [],
				list2: [],
				taskList: []
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			openCert() {
				uni.navigateTo({
					url: '/pages-mine/auth/auth-certificate?id=' + this.baomuId
				})
			},
			// 查看全部
			openAll(index) {
				if (index == 0) {
					this.$refs.uNotify.success("已显示全部权益！")
				} else if (index == 1) {
					this.$refs.uNotify.success("努力升级获取更多权益吧！")
				}
			},
			// 查看单个权益
			openItem(type, index) {
				if (type == 0) {
					this.equityText = this.list1[index].equityText
					this.equityContent = this.list1[index].equityContent
					this.equityImg = this.list1[index].equityImg
					let url = this.list1[index].equityUrl
					if (url != null && url != "") {
						uni.navigateTo({
							url: url
						})
						return
					}

					this.popupShow = true
				} else if (type == 1) {
					this.equityText = this.list2[index].equityText
					this.equityContent = this.list2[index].equityContent
					this.equityImg = this.list2[index].equityImg
					let url = this.list2[index].equityUrl
					if (url != null && url != "") {
						uni.navigateTo({
							url: url
						})
						return
					}
					this.popupShow = true
				}
			},
			// 去升级
			upgrade() {
				if (this.authReport.level > 3) {
					this.$refs.uNotify.success("恭喜您，已经完成所有升级任务啦!")
					return
				}
				// 滚动到任务菜单
				uni.pageScrollTo({
					selector: '#task',
					// scrollTop: 400,
					duration: 100
				})
			},
			// 去完成/领取奖励
			completeTask(value, index) {
				let task = this.taskList[index]
				if (value == 0) {
					if (task.taskName.includes("晋级")) {
						this.$refs.uNotify.success("完成上面的所有任务才可以晋级哦!")
					}

					uni.navigateTo({
						url: task.taskUrl
					})
				} else if (value == 1) {
					this.completeEmployeeTaskRecord(index)
				}
			},
			// 完成任务
			completeEmployeeTaskRecord(index) {
				let id = this.taskList[index].id
				this.http({
					url: 'completeEmployeeTaskRecord',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						taskId: id,
						employeeId: this.baomuId,
						state: 2
					},
					success: res => {
						if (res.code == 0) {
							this.taskList[index].state = 2
							this.getEmployeeTask()
							this.$refs.uNotify.success("恭喜您，已经完成该任务!")
						} else {
							this.$refs.uNotify.error("完成该任务失败!")
						}
					}
				})
			},
			// 经验值计算
			exValue(index) {
				// 获取当前经验值
				let nowValue = this.score
				let nowLevelValue = this.levelList[this.authReport.level].exValue
				let nextLevelValue = this.levelList[this.authReport.level + 1].exValue
				if (this.authReport.level == 7) {
					nextLevelValue = this.levelList[2].exValue
				}
				// 0：计算当前经验值 1：计算还差多少经验值升级
				if (index == 0) {
					return nowValue
				} else if (index == 1) {
					return nextLevelValue - nowValue
				} else if (index == 2) {
					return (nowValue - nowLevelValue) / nextLevelValue * 100
				}
			},
			tryConfirmReport() {
				if (!this.isAdmin) {
					this.openCheck(1, "即将发送验证短信", "请注意查收等级权益确认短信哦")
				} else {
					this.openCheck(1, "即将发送验证短信", "发送至员工，鉴定师可直接获取验证码")
				}
			},
			// 发送短信验证码，确认等级权益
			confirmReport() {
				if (!this.isSendSms) {
					this.http({
						url: 'confirmWorkSkillAuth',
						header: {
							'content-type': "application/json;charset=UTF-8"
						},
						method: 'POST',
						hideLoading: true,
						data: {
							"isSendSms": true,
							"employeeId": this.baomuId,
							"salary": this.salary
						},
						success: res => {
							if (res.code == 0) {
								this.openCheck(0, "短信验证码发送成功!", "请在下方输入框输入验证码完成确认（可自动识别）")
								this.isSendSms = true
								this.smsTime = 0
								// 鉴定师自动获取验证码
								if (this.isAdmin) {
									this.getWorkSkillAuthSmsCode()
								}
							} else {
								this.$refs.uNotify.success("短信验证码发送失败!" + res.msg)
							}
						}
					})
				} else {
					this.$refs.uNotify.error("请稍候再发送短信!")
				}
			},
			// 校验短信验证码
			checkSms() {
				let code = this.smsCode
				if (code.length == 6) {
					this.http({
						url: 'confirmWorkSkillAuth',
						header: {
							'content-type': "application/json;charset=UTF-8"
						},
						method: 'POST',
						hideLoading: true,
						data: {
							"isSendSms": false,
							"employeeId": this.baomuId,
							"code": code
						},
						success: res => {
							if (res.code == 0) {
								this.$refs.uNotify.success("等级权益确认完成!")
								// 更改员工上架状态（权限）
								this.employeeState = 1
								uni.setStorageSync("employeeState", 1)
								this.getBaomuDetail()
							} else {
								this.$refs.uNotify.error("验证码输入错误，请重新输入!")
							}
						}
					})
				}
			},
			// 获取工作鉴定短信验证码（鉴定师专用）
			getWorkSkillAuthSmsCode() {
				this.http({
					url: 'getWorkSkillAuthSmsCode',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.baomuId,
					},
					success: res => {
						if (res.code == 0) {
							this.smsCodeTips = res.data
						}
					}
				})
			},
			// 发送订阅消息给确认过的员工
			sendSubscribeMsg() {
				this.http({
					url: 'sendSubscribeMsg',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: {
						source: "xyjacn",
						memberId: null,
						employeeId: this.baomuId,
						templateId: this.templateId,
						page: "/pages-mine/index/index",
						data: {
							"name1": {
								"value": this.employee.realName
							},
							"phone_number2": {
								"value": this.employee.phone
							},
							"time6": {
								"value": this.employee.putTime
							},
							"phrase3": {
								"value": "成功"
							},
							"thing7": {
								"value": "赶快进入家姐联盟查看吧！"
							}
						}
					},
					success: res => {
						if (res.code == 0) {
							this.$toast.toast("入驻成功提醒已发送给员工！")
						} else {
							this.$toast.toast("入驻成功提醒发送失败！" + res.msg)
						}
					}
				})
			},
			// 格式化页面样式
			formatStyle(img, img1) {
				let style = ""
				let style1 = ""
				style = "background: url(" + img +
					") no-repeat center center;background-size: 100% 100%;width: 100%;min-height: 30%;background-repeat: no-repeat;"
				style1 = "background: url(" + img1 +
					") no-repeat center center;background-size: 100% 100%;min-height: 32%;background-repeat: no-repeat;"
				this.backgroundImg = style
				this.levelBackgroundImg = style1
				this.fontStyle = this.levelList[this.authReport.level].fontStyle
				this.btnStyle = this.levelList[this.authReport.level].btnStyle
				this.btnUpStyle = this.levelList[this.authReport.level].btnUpStyle
			},
			// 格式化将来的薪资
			formatSalaryFuture() {
				let salaryFuture = parseInt(this.authReport.authSalary) + 2000
				return salaryFuture
			},
			// 保存技能证书（提交到后台）
			saveReport() {

			},
			// 获取保姆详细信息成功
			getBaomuDetail() {
				if (this.baomuId !== null) {
					this.http({
						url: 'getBaomuDetail',
						method: 'GET',
						hideLoading: true,
						path: this.baomuId,
						success: res => {
							if (res.code == 0) {
								let baomuDetail = res.data
								this.employee = baomuDetail.employee
								this.sendSubscribeMsg()
							} else {
								// this.$toast.toast('获取保姆详细信息失败，请求错误！' + res.msg)
							}
						}
					});
				}
			},
			// 获取员工技能鉴定记录
			getEmployeeSkill() {
				this.http({
					url: 'getEmployeeSkill',
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.baomuId || 0
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.levelName = res.data.levelName
							this.salary = res.data.salary
							this.updaterNo = res.data.updaterNo
							this.updaterName = res.data.updaterName
							this.updateTime = res.data.updateTime
							this.employeeState = res.data.state
						} else {
							this.$refs.uNotify.error("鉴定记录获取失败！")
						}
					}
				})
			},
			// 获取鉴定报告
			getAuthReport() {
				if (this.baomuId !== null) {
					this.http({
						url: 'getAuthReport',
						method: 'GET',
						hideLoading: true,
						path: this.baomuId,
						success: res => {
							if (res.code == 0) {
								this.authReport = res.data
								this.workTypeId = this.authReport.workTypeId
								this.listUnionLevelEquity()
								this.getEmployeeTask()
								console.log('获取保姆信息-请求成功！');
							} else {
								console.log('获取保姆信息-返回错误！');
							}
						},
					});
				}
			},
			// 格式化等级素材
			formartEmployeeEquity() {
				// 测试时加上
				// this.authReport.level = 2
				// this.authReport.workTypeName = "搬家师"
				// this.workTypeId = 2

				// 识别员工工种
				let workType = "保姆"
				workType = this.authReport.workTypeName

				// 获取员工等级
				let level = this.authReport.level
				let levelText = this.levelList[level].text
				let levelTextSearch = levelText + workType
				let levelTextNext = this.levelList[level + 1].text
				// 对二星进行判断
				if (level == 7) {
					levelTextNext = this.levelList[2].text
				}
				this.list1 = []
				this.list2 = []
				let back = ''
				let levelBack = ''
				for (let item of this.employeeEquity) {
					if (item.imgType == 3) {
						this.posterSrc = item.equityImg
					}
					if (levelTextSearch.includes(item.equityName)) {
						// 等级卡片图
						if (item.imgType == 0) {
							levelBack = item.equityImg
						}
						// 比上一等级多享受权益 素材
						else if (item.imgType == 1) {
							this.list1.push(item)
						}
						// 当前等级所有权益素材
						else if (item.imgType == 2) {
							this.list2.push(item)
						}
						// 等级背景图
						else if (item.imgType == 4) {
							back = item.equityImg
						}
						// 当前等级可xxx权益
						else if (item.imgType == 5) {
							this.EquityAll = item.equityText
						}
						// 比上一等级xxx权益
						else if (item.imgType == 6) {
							this.EquityAdd = item.equityText
						}
						// 比上一等级薪资高xx员
						else if (item.imgType == 7) {
							this.EquityAddMoney = item.equityText
						}
					}

					if (levelTextNext.includes(item.equityName) && item.imgType == 5) {
						this.EquityAll = item.equityText
					}
					this.formatStyle(back, levelBack)
				}
			},
			// 分享到好友
			onShareAppMessage(res) {
				return {
					title: '小羽佳-家姐联盟',
					path: "/pages-mine/auth/auth-report?baomuId=" + this.baomuId +
						"&employeeState=" + this.employeeState,
					mpId: 'wx8342ef8b403dec4e'
				};
			},
			//分享到朋友圈
			onShareTimeline(res) {
				return {
					title: '小羽佳-家姐联盟',
					type: 0,
					summary: '还在为找家政类工作而烦恼吗？加入小羽佳，成为我们的一员！'
				};
			},
			// 获取员工等级任务列表
			getEmployeeTask() {
				this.http({
					url: 'getEmployeeTask',
					method: 'GET',
					hideLoading: true,
					path: this.baomuId,
					success: res => {
						if (res.code == 0) {
							this.taskList = res.data.taskList
							this.score = res.data.score
							console.log('获取员工等级任务-请求成功！当前经验：' + this.score);
						} else {
							console.log('获取员工等级任务-返回错误！');
						}
					},
				});
			},
			listUnionLevelEquity() {
				this.http({
					url: 'listUnionLevelEquity',
					method: 'POST',
					hideLoading: true,
					data: {
						equityType: 0
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.employeeEquity = res.data
							this.formartEmployeeEquity()
							console.log('获取员工等级素材-请求成功！');
						} else {
							console.log('获取员工等级素材-返回错误！');
						}
					},
				});
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {

				} else if (this.checkType == 1) {
					this.confirmReport()
				}
			},
			// 登录状态检查
			checkLogin() {
				console.log('开始检查登录状态！')
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.error('您还未进行登录哦，先去登录吧！')
					uni.setStorageSync('redirectUrl', "/pages-mine/auth/auth-report?baomuId=" + this.baomuId +
						"&employeeState=" + this.employeeState)
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						});
					}, 2000);
				} else {
					this.getAuthReport()
					this.getEmployeeSkill()
				}
			},
		},
		watch: {
			smsTime: {
				handler(newValue, oldVal) {
					if (this.smsTime == this.smsTimeLimit) {
						this.isSendSms = false
					}
				},
				deep: true
			}
		},
		onLoad(options) {
			this.screenHeight = uni.getSystemInfoSync().windowHeight
			this.baomuId = options.baomuId || uni.getStorageSync('baomuId') || null
			this.employeeState = options.employeeState || uni.getStorageSync('employeeState') || null
			// 判断是否是鉴定师查看报告
			if (options.isAdmin) {
				this.isAdmin = true
				console.log("当前为鉴定师查看报告，无需输入验证码！")
			}

			setInterval(() => {
				this.smsTime += 1
			}, 1000);

			// 初始化部分样式
			this.fontStyle = this.levelList[2].fontStyle
			this.btnStyle = this.levelList[2].btnStyle
			this.btnUpStyle = this.levelList[2].btnUpStyle
			this.checkLogin()
		},
	}
</script>

<style>
	page {
		width: 100%;
		height: 100%;
		background-color: #ebebeb;
	}
</style>
<style lang="scss" scoped>
	@import "@/pages-mine/common/css/resume-tab.scss";

	.bj {
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669791276740fcbj.png') no-repeat center center;
		background-size: 100% 100%;
		width: 100%;
		min-height: 30%;
		background-repeat: no-repeat;
	}

	.service {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		padding: 20rpx 30rpx;
	}

	.section1 {
		width: 100%;
		position: absolute;
		z-index: 10;
		top: 0;
		left: 0;
		margin: 80rpx auto;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669873173495level1.png') no-repeat center center;
		background-size: 100% 100%;
		min-height: 35%;
		background-repeat: no-repeat;
		// box-shadow: 0rpx 30rpx 30rpx 0rpx rgba(0, 0, 0, 0.2);
	}

	.progress {
		position: absolute;
		width: 45%;
		height: 50rpx;
		color: #f5eccf;
		top: 50%;
		left: 12%
	}

	.section1_text {
		display: flex;
		justify-content: space-between;
	}

	.section2 {
		position: absolute;
		top: 30%;
		left: 2.5%;
	}

	.section2_add {
		padding: 13% 0 1% 0;
		border-radius: 20rpx;
	}

	.section2_all {
		margin: 30rpx auto;
		border-radius: 20rpx;
		padding: 6% 0 1% 0;
	}

	.section2_title {
		display: flex;
		justify-content: space-between;
	}

	.section2_list {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 30rpx auto;
	}

	.section2_promote {
		margin: 30rpx auto;
		border-radius: 20rpx;
		padding: 5% 0
	}

	.promote_con {
		display: flex;
		align-items: center;
		border-bottom: 2rpx solid #eee;
	}

	.textColor {
		color: #907124;
	}

	// 栏目输入框（单行高）
	.tab-inputbox {
		display: block;
		margin: 20rpx auto;
		width: 90%;
		height: 100rpx;
		background-color: #f9f9f9;
		border-radius: 20rpx;
		box-shadow: 2rpx 2rpx 10rpx #dedede;
	}

	// 单行输入框
	.single-input {
		display: block;
		float: left;
		width: 80%;
		height: 80rpx;
		line-height: 80rpx;
		padding-left: 30rpx;
		font-size: 32rpx;
		text-align: left;
		margin: 10rpx 0 0 20rpx;
		border-style: hidden;
	}

	.btn-big {
		padding-bottom: 60rpx;

		button {
			margin: 80rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	.poster-img {
		width: 100%;
		height: auto;

		img {
			display: block;
			margin: 20rpx auto;
			width: 96%;
			height: auto;
			border-radius: 20rpx;
		}
	}

	// 提示弹窗标题
	.popup-title {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;

		text {
			display: block;
			text-align: center;
			font-weight: bold;
			font-size: 40rpx;
		}
	}

	// 提示图片
	.popup-img {
		width: 100%;
		height: auto;

		img {
			display: block;
			width: 30%;
			height: auto;
			margin: 40rpx auto 0 auto;
		}
	}

	// 上传提示
	.popup-tips {
		width: 80%;
		line-height: 60rpx;
		margin: 20rpx auto 0 auto;

		text {
			display: block;
			font-size: 36rpx;
			text-align: center;
		}
	}

	.grid-text {
		font-size: 14px;
		color: #909399;
		padding: 10rpx 0 20rpx 0rpx;
		/* #ifndef APP-PLUS */
		box-sizing: border-box;
		/* #endif */
	}

	// 证件照片
	.icon-content {
		width: 25%;
		height: auto;
		padding: 30rpx 0 30rpx 6.25%;

		img {
			display: block;
			width: 80rpx;
			height: 80rpx;
			margin: 0 auto 20rpx auto;
		}

		text {
			line-height: 30rpx;
			height: 60rpx;
			display: block;
			text-align: center;
			font-size: 30rpx;
		}
	}
</style>