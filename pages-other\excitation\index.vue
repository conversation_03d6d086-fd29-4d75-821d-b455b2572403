<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 评论 -->
		<u-popup :show="showPopup" @close="showPopup = false" round="10">
			<view class="f18 fb text-c lh60">我的评论</view>
			<scroll-view scroll-y="true" class="scroll-Y">
				<view class="tab-inputbox-high" style="margin: 10rpx 0rpx 30rpx 30rpx;">
					<u--textarea class="multiline-input" confirmType="done" maxlength="200"
						v-model="excellentEmployeeComment.commentContent" placeholder="请填写您对该员工的评论（参与评论与打赏，一起进步吧！）"
						height="100" count></u--textarea>
				</view>
			</scroll-view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="showPopup = false">
						<text>取消</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="updateExcellentEmployeeComment()">
					<view class="filter-button-right">
						<text>提交</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 打赏 -->
		<u-popup :show="showPopup1" @close="showPopup1 = false" round="10">
			<view class="f18 fb text-c lh60">积分打赏</view>
			<scroll-view scroll-y="true" class="scroll-Y">
				<view class="flac-row-b f16">
					<view>当前余额：<text :style="rewardAuth?'text-decoration: line-through;':''">{{jiaBiAmount}}积分</text>
					</view>
					<uni-number-box v-model="rewardAmount" :min="0" :max="rewardAuth?rewardLimit:jiaBiAmount" />
				</view>
				<view class="f16 lh40" style="color: #ff4d4b;">
					{{rewardAuth?'tips：管理员无需消耗余额，上限'+rewardLimit+'积分':'参与打赏，共同进步吧！'}}
				</view>
			</scroll-view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="showPopup1 = false">
						<text>取消</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="rewardExcellentEmployee()">
					<view class="filter-button-right">
						<text>提交</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 头部显示 -->
		<view class="back-img flac-col">
			<view class="head-tab flac-row-b f16">
				<view class="w2">
					<button plain="true" style="border: none;">
						<uni-icons type="person" size="20" color="#fff"></uni-icons>
					</button>
				</view>
				<view class="w2" @click="shareType=0">
					<button plain="true" open-type="share" style="border: none;">
						<uni-icons type="redo" size="20" color="#fff"></uni-icons>
					</button>
				</view>
			</view>
			<img :src="backImg" alt="" />
			<view class="head-tab1">
				<img :src="headImg||blankHeadImg" class="" />
				<text class="f18">{{userName}}</text>
			</view>
			<view class="head-tab2 flac-row-b f16 ">
				<view class="flac-col-c" v-for="(item,index) in rewardDataList" :key="index"
					@click="openTabHead(index)">
					<text class="fb">{{item.num}}</text>
					<text class="text-bar">{{item.text}}</text>
				</view>
			</view>
		</view>

		<view class="flac-row-b w9 mg-at" style="margin: 40rpx auto;">
			<view class="flac-col" style="align-items: center;" v-for="(item,index) in tabList" :key="index"
				v-if="item.show" @click="openTab(index)">
				<img :src="item.icon||backImg" class="tab-icon" />
				<text style="margin-top: 10rpx;">{{item.text}}</text>
			</view>
		</view>

		<!-- 海报图 -->
		<swiper class="swiper" circular :indicator-dots="false" :autoplay="true" :interval="interval"
			:duration="duration">
			<swiper-item v-for="(item,index) in postImgList" :key="index">
				<img :src="item" class="post-img" mode="widthFix" />
			</swiper-item>
		</swiper>

		<!-- 排行榜 -->
		<view class="flac-col f16 " v-if="rankList.length>=3">
			<view class="w9 mg-at flac-row-b lh50">
				<view class="f20 fb">
					月度排行榜
				</view>
				<view @click="openRank()">
					详情 >
				</view>
			</view>
			<view class="flac-row-c rank-tab ">
				<view class="rank-img">
					<img :src="rankList[1].employeeHeadImg||blankHeadImg" style="margin-top: 40rpx;">
					<img :src="rankImg[1]">
					<view class="flac-col-c" style="margin-top: 20rpx;">
						<text>{{rankList[1].employeeName}}</text>
						<text>{{rankList[1].num}}</text>
					</view>
				</view>
				<view class="rank-img">
					<img :src="rankList[0].employeeHeadImg||blankHeadImg"
						style="width: 160rpx;height: 160rpx;margin-left: 35rpx;">
					<img :src="rankImg[0]" style="width: 182rpx;height: 182rpx;margin: -170rpx 22rpx;">
					<view class="flac-col-c" style="margin-top: 20rpx;">
						<text>{{rankList[0].employeeName}}</text>
						<text>{{rankList[0].num}}</text>
					</view>
				</view>
				<view class="rank-img">
					<img :src="rankList[2].employeeHeadImg||blankHeadImg" style="margin-top: 40rpx;">
					<img :src="rankImg[2]">
					<view class="flac-col-c" style="margin-top: 20rpx;">
						<text>{{rankList[2].employeeName}}</text>
						<text>{{rankList[2].num}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 员工列表 -->
		<view class="main-bar f16" v-for="(item,index) in list" :key="index">
			<view class="flac-col" style="padding: 0 20rpx;">
				<view class="flac-row">
					<view class="w2" @click="openImgPreview(formatHeadImg(item))">
						<u-avatar :src="formatHeadImg(item)" size="50" />
					</view>
					<view class="flac-col w8" style="padding: 10rpx 0;" @click="openDetail(index)">
						<view class="flac-row-b">
							<text class="main-text f18" style="display: block;">{{item.name || '匿名用户'}}</text>
							<text class="f14" style="display: block;">{{item.createDate}}</text>
						</view>
						<view style="margin: 15rpx 0rpx;" v-if="formatCreatorName(item)">
							获得<text class="main-text" style="margin: 0 10rpx;">{{formatCreatorName(item)}}</text>的赞赏
						</view>
					</view>
				</view>
				<view class="w8" style="margin: 0 20%;">
					<view @click="openDetail(index)">
						<view v-if="item.introduce">工作介绍：{{item.introduce}}</view>
						<view v-if="item.workDeeds">工作事迹：{{item.workDeeds}}</view>
					</view>
					<view v-if="item.dailyReviewId" style="color: #00aaff;text-decoration:underline;margin: 20rpx 0;">
						<text @click="goPage('/pages-other/excitation/review-detail?id='+item.dailyReviewId)">每日复盘详情
						</text>
					</view>
					<view style="margin: 20rpx 0;">
						<u-album :maxCount="maxShowPhoto" rowCount="3" multipleSize="90" space="3"
							:urls="item.excellentEmployeeImgList" />
					</view>
					<!-- 奖励类型 -->
					<view class="flac-row-b" style="margin: 10rpx 0" @click="openDetail(index)" v-if="item.rewardTypeImg">
						<view class="flac-row-b">
							<view>
								<img :src="item.rewardTypeImg" alt=""
									style="width: 50rpx;height: 50rpx;border-radius: 50%;margin-top: 10rpx;" />
							</view>
							<view style="margin-left: 20rpx;">
								<text>{{item.rewardTypeName}}</text>
							</view>
						</view>
						<view>
							{{item.rewardAmount}}
						</view>
					</view>
					<!-- 操作栏 -->
					<view class="flac-row-b bar f16" style="margin-top: 20rpx;">
						<view class="flac-row" @click="like(index)">
							<uni-icons :type="item.liked==1?'hand-up-filled':'hand-up'" size="20"
								:color="item.liked==1?'#ff4d4b':'#dedede'"></uni-icons>
							<text
								style="margin-left: 10rpx;">{{item.excellentEmployeeLikeList?item.excellentEmployeeLikeList.length:'0'}}</text>
						</view>
						<view class="flac-row" @click="openPopup(index,0)">
							<uni-icons type="chat" size="20"></uni-icons>
							<text style="margin-left: 10rpx;">评论</text>
						</view>
						<view class="flac-row" @click="openPopup(index,1)">
							<uni-icons type="medal" size="20"></uni-icons>
							<text style="margin-left: 10rpx;">打赏</text>
						</view>
						<view class="flac-row" style="margin-left: -30rpx;" @click="choiceIndex=index;shareType=1">
							<button plain="true" open-type="share" style="border: none;"><uni-icons type="redo"
									size="20"></uni-icons></button>
							<text style="margin-left: -10rpx;">分享</text>
						</view>
					</view>
					<!-- 点赞 -->
					<view class="flac-row f16" style="flex-wrap: wrap;" v-if="item.excellentEmployeeLikeList.length">
						<view class="flac-row" v-for="(item1,index1) in item.excellentEmployeeLikeList" :key="index1">
							<text>{{formatName(item1)}}</text>
							<uni-icons type="heart" size="20" color="#ff4d4b" style="margin-left: 10rpx;"></uni-icons>
							<text v-if="parseInt(index1)!=item.excellentEmployeeLikeList.length-1">、</text>
						</view>
					</view>
					<view class="flac-col f16" v-if="item.excellentEmployeeCommentList.length"
						style="background-color: #f4f4f5;border-radius: 20rpx;margin:20rpx 0;padding: 20rpx 20rpx;">
						<view v-for="(item2,index2) in item.excellentEmployeeCommentList" :key="index2">
							<text class="main-text">{{formatName(item2)}}：</text>
							<text>{{item2.commentContent||'-'}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<u-empty v-if="list.length==0" text="暂无表彰" icon="http://cdn.uviewui.com/uview/empty/data.png" />

		<view class="list-bottom" v-if="list.length">
			<text v-if="searchCondition.current>=pageCount">已显示全部内容</text>
			<text v-else>加载中...</text>
		</view>

		<u-gap height="400"></u-gap>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 打赏权限
				rewardAuth: false,
				// 最大显示图片数
				maxShowPhoto: 3,
				// 打赏上限
				rewardLimit: 10000,
				// 打赏管理员会员id
				rewardMemberId: 298434,
				// 分享模式（0：本页面分享 1：详情分享）
				shareType: 0,
				interval: 4500,
				duration: 1000,

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				rewardAmount: 0,
				jiaBiAmount: 0,
				choiceIndex: 0,
				blankHeadImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				backImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/excitation_back.png',
				postImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/excitation_post.png',
				postImgList: [
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/excitation_post.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/excitation_post1.png',
					'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/excitation_post2.png',
				],
				headImg: uni.getStorageSync("employeeHeadImg") || uni.getStorageSync("memberHeadImg") || '',
				userName: uni.getStorageSync("employeeName") || uni.getStorageSync("memberName") || '匿名用户',
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				tabList: [{
						text: '赞赏',
						icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/赞赏.png',
						url: '',
						show: true

					},
					{
						text: '表彰',
						icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/表彰.png',
						url: '/pages-other/excitation/excitation',
						show: true
					},
					{
						text: '悬赏',
						icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/悬赏.png',
						url: '',
						show: true
					},
					{
						text: '投票',
						icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/红包.png',
						url: '/pages-other/excitation/excitation-vote',
						show: true
					},
					{
						text: '兑换',
						icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/兑换.png',
						url: '/pages-mine/integralMall/productIndex',
						show: true
					},
					{
						text: '复盘',
						icon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/黄牌.png',
						url: '/pages-other/excitation/review',
						show: true
					}
				],
				rankImg: [
					"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_img1.png",
					"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_img2.png",
					"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_img3.png"
				],
				rewardDataList: [],
				excellentEmployeeComment: {
					excellentEmployeeId: 0,
					memberId: uni.getStorageSync("memberId") || 0,
					employeeId: uni.getStorageSync("employeeId") || null,
					commentContent: '',
					commentScore: 0,
				},
				showPopup: false,
				showPopup1: false,
				total: 0,
				pageCount: 0,
				searchCondition: {
					orderBy: "t.createDate DESC",
					current: 1,
					size: 10
				},
				list: [],
				rankList: [],
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 选择菜单
			choiceMenu(e) {
				this.choiceIndex = e.index
			},
			// 选择头部栏目
			openTabHead(index) {
				let url = '/pages-other/excitation/reward-detail'
				if (index == 0) {
					url = '/pages-mine/signIn/IntegralPage'
				}
				if (index == 2) {
					url += '?choiceIndex=1'
				}
				uni.navigateTo({
					url: url
				})
			},
			// 选择栏目
			openTab(index) {
				let item = this.tabList[index]
				if (!item.url) {
					return this.$refs.uNotify.warning("功能开发中，敬请期待！")
				}
				uni.navigateTo({
					url: item.url
				})
			},
			// 跳转页面
			goPage(url) {
				uni.navigateTo({
					url: url
				})
			},
			// 打开详情
			openDetail(index) {
				uni.navigateTo({
					url: '/pages-other/excitation/excitation-detail?id=' + this.list[index].id
				})
			},
			// 打开弹出栏
			openPopup(index, value) {
				this.choiceIndex = index
				if (value == 0) {
					this.showPopup = true
				} else if (value == 1) {
					this.showPopup1 = true
				}
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 打开排行
			openRank() {
				uni.navigateTo({
					url: '/pages-other/excitation/rank'
				})
			},
			// 格式化时间
			formatDate(value) {
				if (value == null) {
					return "-"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '-' + MM + '-' + d
			},
			formatName(val) {
				return val.name || val.employeeName || val.memberName || '匿名用户'
			},
			formatCreatorName(val) {
				if (val.selfReference && val.selfReference == val.name) {
					return '自己'
				}
				return val.reference || val.selfReference || val.creatorName || ''
			},
			formatHeadImg(val) {
				return val.employeeHeadImg || val.memberHeadImg || this.blankHeadImg
			},
			// 获取打赏排行
			listMonthExcitationRank() {
				let date = new Date()
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/content/listMonthExcitationRank',
					method: 'POST',
					hideLoading: true,
					data: {
						searchYear: date.getFullYear(),
						searchMonth: date.getMonth() + 1
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.rankList = res.data
						}
					}
				});
			},
			// 获取优秀员工列表
			pageExcellentEmployee() {
				this.$set(this.searchCondition, 'memberIdSearch', this.memberId || 0)
				this.http({
					url: 'pageExcellentEmployee',
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: this.searchCondition,
					success: res => {
						if (res.code == 0) {
							this.total = res.data.total
							this.pageCount = res.data.pages
							this.list = this.list.concat(res
								.data.records)
						} else {

						}
					},
				})
			},
			// 点赞
			like(index) {
				if (!this.checkLogin()) {
					return
				}
				let excellentEmployee = this.list[index]
				let liked = excellentEmployee.liked
				this.http({
					url: 'updateExcellentEmployeeLike',
					method: 'POST',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						excellentEmployeeId: excellentEmployee.id || 0,
						memberId: this.memberId || 0,
						employeeId: this.employeeId || null,
					},
					success: res => {
						if (res.code == 0) {
							if (liked == 0) {
								this.$refs.uNotify.success("点赞成功！")
							}
							excellentEmployee.liked = liked == 1 ? 0 : 1
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
				})
			},
			// 评论
			updateExcellentEmployeeComment() {
				if (!this.checkLogin()) {
					return
				}

				if (!this.excellentEmployeeComment.commentContent) {
					return this.$refs.uNotify.warning("请填写评论内容！")
				}
				this.$set(this.excellentEmployeeComment, "excellentEmployeeId", this.list[this.choiceIndex].id)
				this.http({
					url: 'updateExcellentEmployeeComment',
					method: 'POST',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: this.excellentEmployeeComment,
					success: res => {
						if (res.code == 0) {
							this.showPopup = false
							this.$refs.uNotify.success("评论成功！")
							this.excellentEmployeeComment.commentContent = ''
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
				})
			},
			// 打赏
			rewardExcellentEmployee() {
				if (!this.checkLogin()) {
					return
				}
				if (this.rewardAmount == 0) {
					return this.$refs.uNotify.warning("请输入打赏积分数量！")
				}
				this.http({
					url: 'rewardExcellentEmployee',
					method: 'POST',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						memberId: this.rewardAuth ? this.rewardMemberId : this.memberId,
						employeeId: this.employeeId || null,
						excellentEmployeeId: this.list[this.choiceIndex].id,
						rewardAmount: this.rewardAmount
					},
					success: res => {
						if (res.code == 0) {
							this.rewardAmount = 0
							this.$refs.uNotify.success("打赏成功！")
							this.getjiaBiAmount()
							this.showPopup1 = false
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
				})
			},
			// 获取打赏数据
			getRewardData() {
				this.http({
					url: 'getRewardData',
					method: 'GET',
					hideLoading: true,
					path: this.memberId || 0,
					success: res => {
						if (res.code == 0) {
							this.rewardDataList = res.data
						}
					},
				})
			},
			// 获取佳币数量
			getjiaBiAmount() {
				this.http({
					url: 'getJiaBi',
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.memberId || 0
					},
					success: res => {
						if (res.code == 0) {
							this.jiaBiAmount = res.data.jiaBiAmount
						}
					},
				})
			},
			// 校验打赏权限
			checkRewardAuth() {
				this.http({
					url: 'checkRewardAuth',
					method: 'POST',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					hideLoading: true,
					data: {
						roleId: uni.getStorageSync('roleId') || 0,
						employeeId: uni.getStorageSync('employeeId') || null,
					},
					success: res => {
						if (res.code == 0) {
							this.rewardAuth = true
						}
					},
				})
			},
			checkLogin() {
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.warning("您还未进行登录哦，请先登录吧！")
					uni.setStorageSync('redirectUrl', '/pages-other/employee/excitation')
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						})
					}, 2000)
					return false
				} else {
					return true
				}
			},
			// 分享到好友或朋友圈
			onShareAppMessage(res) {
				return new Promise((resolve, reject) => {
					let shareContent = {}
					if (this.shareType == 0) {
						shareContent = {
							title: '来激励宝一起进步吧！',
							path: '/pages-other/excitation/index',
							mpId: 'wx8342ef8b403dec4e',
						}
					} else {
						let excellentEmployee = this.list[this.choiceIndex]
						let img = excellentEmployee.rewardTypeImg || this.blankHeadImg
						let reward = excellentEmployee.rewardTypeName ? '【' + (excellentEmployee.rewardTypeName ||
								'') +
							'】的' : ''
						let title = '恭喜您获得了' + reward + '赞赏，快来看看吧！'
						shareContent = {
							title: title,
							path: '/pages-other/excitation/excitation-detail?id=' + excellentEmployee.id,
							mpId: 'wx8342ef8b403dec4e',
							imageUrl: img
						}
					}
					resolve(shareContent)
				})
			},
		},
		onReachBottom() {
			this.searchCondition.current++
			this.pageExcellentEmployee()
		},
		onLoad(options) {
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
			}
			this.listMonthExcitationRank()
			this.pageExcellentEmployee()
			this.getjiaBiAmount()
			this.getRewardData()
			this.checkRewardAuth()
		},
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";
	@import "@/pages-mine/common/css/resume-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}

	.back-img {
		img {
			display: block;
			width: 100%;
			height: 550rpx;
		}
	}

	.swiper {
		width: 100%;
		height: 240rpx;
	}

	.post-img {
		display: block;
		width: 90%;
		margin: 20rpx auto;
	}

	.head-tab {
		position: absolute;
		width: 100%;
		top: 20rpx;
		left: 0%;
		color: #fff;
	}

	.head-tab1 {
		position: absolute;
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 40%;
		top: 100rpx;
		left: 30%;

		img {
			display: block;
			width: 180rpx;
			height: 180rpx;
			border-radius: 50%;
		}

		text {
			margin-top: 20rpx;
			text-align: center;
			color: #ffffff;
		}
	}

	.head-tab2 {
		position: absolute;
		width: 80%;
		top: 380rpx;
		left: 10%;
		color: #fff;
	}

	.text-bar {
		margin-top: 10rpx;
		width: 160rpx;
		height: 45rpx;
		line-height: 45rpx;
		background-color: rgba(47, 47, 47, 0.1);
		border-radius: 40rpx;
		text-align: center;
		font-size: 28rpx;
	}


	.tab-icon {
		display: block;
		width: 80rpx;
		height: 80rpx;
		-webkit-border-radius: 30rpx;
		-moz-border-radius: 30rpx;
		box-shadow: 5rpx 10rpx 20rpx #dedede;
	}

	.main-text {
		color: #1e1848;
		font-weight: bold;
	}

	.main-bar {
		width: 94%;
		height: auto;
		padding: 40rpx 3%;
		margin: 20rpx 0;
		border-bottom: 1rpx #dedede solid;
	}

	.bar {
		width: 100%;
		height: 80rpx;
		border-top: 1rpx #dedede dashed;
		border-bottom: 1rpx #dedede dashed;
		margin: 40rpx 0 20rpx 0;
	}

	.small-tab {
		width: 100%;
		height: 140rpx;
		border-bottom: 2rpx #dedede solid;
		margin: 20rpx 0;
	}

	.list-bottom {
		width: 100%;
		height: 40rpx;
		line-height: 40rpx;

		text {
			display: block;
			text-align: center;
			font-weight: 100;
		}
	}

	.scroll-Y {
		display: block;
		width: 90%;
		height: 850rpx;
		margin: 0rpx 5%;
	}

	/*
		排行榜
	*/
	.rank-img {
		width: 33.33%;
		height: 200rpx;
		padding: 30rpx 0;

		img:nth-child(1) {
			display: block;
			width: 120rpx;
			height: 120rpx;
			margin: 0 50rpx;
			border-radius: 50%;
		}

		img:nth-child(2) {
			position: absolute;
			width: 140rpx;
			height: 140rpx;
			margin: -130rpx 40rpx;
		}
	}

	.rank-tab {
		height: auto;
		width: 90%;
		margin: 0 auto;
		padding: 20rpx 0 60rpx 0;
		box-shadow: 0 4rpx 20rpx #dedede;
		border-radius: 20rpx;
	}
</style>