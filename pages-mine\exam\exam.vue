<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 选题弹窗 -->
		<u-popup :show="popupShow" mode="bottom" @close="popupShow = false">
			<view class="filter-title" @click="popupShow = false">
				<uni-icons type="map" size="20" color="#909399"></uni-icons>
				<text>{{choiceQuestionIndex+1}}/{{questionAmount}}</text>
			</view>
			<view class="filter-content">
				<scroll-view :scroll-top="scrollTop1" scroll-y class="filter-scroll-Y">
					<view style="display: flex;flex-wrap: wrap;">
						<view class="tab-checkbox" v-for="(item,index) in examQuestion" :key="index"
							@click="choiceTab(index)">
							<view class="checkbox" :style="[formatStyle(0,index)]">
								<text v-model="index">{{index+1}}</text>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</u-popup>


		<view v-for="(item,index) in examQuestion" :key="index" v-if="choiceQuestionIndex==index">
			<view class="exam-tab">
				<!-- 倒计时 -->
				<view class="countdown" v-if="flag==0">
					<uni-countdown color="#FFFFFF" :background-color="remainTime<=remindTime?'#ff4d4b':'#00B26A'"
						border-color="#00B26A" :day="0" :hour="0" :minute="0" :second="remainTime" :show-day="false"
						@timeup="examEnd()" style="float: right;">
					</uni-countdown>
				</view>

				<!-- 题目 -->
				<view class="tab" style="width: 100%;">
					<view class="tab-head-tag">
						<text @click="flag==0?isShow=!isShow:''"
							:style="[formatStyle(2,index)]">{{questionTypeList[item.questionType].text}}</text>
						<text>{{formatQuestion(index)}}</text>
						<text v-if="isShow">{{item.unionAnswerContent}}</text>
					</view>
				</view>

				<!-- 图片 -->
				<view class="tab" style="width: 100%;" v-if="showQuestionSource(0,index)">
					<view class="question-img" @click="openImgPreview(item.questionImg)">
						<img :src="item.questionImg" alt="" mode="widthFix" />
					</view>
				</view>

				<!-- 视频 -->
				<view class="tab" style="width: 100%;" v-if="showQuestionSource(1,index)">
					<view class="question-video">
						<video :controls="true" :enable-progress-gesture="true" :initial-time="0"
							:show-center-play-btn="true" :src="item.questionVideo" custom-cache="false" />
					</view>
				</view>
			</view>

			<view class="exam-tab" :style="[formatStyle(1,0)]">
				<!-- 单选/判断选项 -->
				<view class="tab" style="width: 100%;" v-if="item.questionType==0 || item.questionType==2">
					<view class="tab-choice" v-for="(item1,index1) in item.questionContent.choice" :key="index1"
						@click="clickChoice(index,index1)" :style="item.isCheck==index1?'background-color: #f4f4f5':''">
						<uni-icons type="circle" size="18" color="#909399" v-if="item.isCheck!=index1">
						</uni-icons>
						<uni-icons type="circle-filled" size="18" color="#909399" v-if="item.isCheck==index1">
						</uni-icons>
						<view class="choice-text" :style="item1.imgUrl?'display:flex;':''">
							<text>{{item1.flag}}. {{item1.content}}</text>
							<img :src="item1.imgUrl" mode="widthFix" v-if="item1.imgUrl" />
						</view>
					</view>
				</view>

				<!-- 多选选项 -->
				<view class="tab" style="width: 100%;" v-if="item.questionType==1">
					<view class="tab-choice" v-for="(item1,index1) in item.questionContent.choice" :key="index1"
						@click="clickChoice(index,index1)" :style="item1.isCheck==1?'background-color: #f4f4f5':''">
						<uni-icons type="circle" size="18" color="#909399" v-if="item1.isCheck==0">
						</uni-icons>
						<uni-icons type="circle-filled" size="18" color="#909399" v-if="item1.isCheck==1">
						</uni-icons>
						<view class="choice-text" :style="item1.imgUrl?'display:flex;':''">
							<text>{{item1.flag}}. {{item1.content}}</text>
							<img :src="item1.imgUrl" mode="widthFix" v-if="item1.imgUrl" />
						</view>
					</view>
				</view>

				<!-- 填空题输入框 -->
				<view class="tab" style="width: 100%;" v-if="item.questionType==3">
					<view class="tab-inputbox-high" v-for="(it,index1) in choiceCountList" :key="index1"
						v-if="index1<item.questionContent.choiceCount">
						<text>填空项{{index1+1}}</text>
						<u--input v-model="item.questionContent.answerContent[index1]" @input="inputAnswer(0,index)"
							placeholder="请输入填空项" border="bottom" clearable v-if="flag==0"
							customStyle="width: 400rpx;display: inline-block;margin-left: 20rpx;">
							填空项{{index}}
						</u--input>
						<text v-if="flag==1">
							{{checkStr(item.questionContent.answerContent[index1])}}
						</text>
					</view>
				</view>

				<!-- 问答题输入框 -->
				<view class="tab" style="width: 100%;" v-if="item.questionType==4">
					<view class="tab-inputbox-high">
						<u--textarea class="multiline-input" confirmType="done" maxlength="200"
							v-model="item.answerContent" @input="inputAnswer(1,index)"
							:placeholder="questionTypeList[4].tips" height="150" count v-if="flag==0">
						</u--textarea>
						<text v-if="flag==1">
							回答项：{{item.answerContent}}
						</text>
					</view>
				</view>
			</view>


			<!-- 答案解析 -->
			<view class="exam-tab" v-if="flag==1" style="height: 1200rpx;">
				<view class="tab-text">
					<text>你的回答：</text>
					<text style="font-weight: 100;">{{formatAnswer(item.answerRecord.answerContent)}}</text>
					<uni-icons type="closeempty" size="18" color="#ff4d4b" v-if="item.answerRecord.isRight==0">
					</uni-icons>
					<uni-icons type="checkmarkempty" size="18" color="#19be6b" v-if="item.answerRecord.isRight==1">
					</uni-icons>
				</view>
				<view class="tab-text">
					<text>正确答案：</text><text style="font-weight: 100;">{{formatExpect(index)}}</text>
				</view>
				<view class="tab-text" v-if="true">
					<text>本题得分：</text><text style="font-weight: 100;">{{item.answerRecord.answerScore||0}}分</text>
				</view>
				<view class="tab-text">
					<text>答案解析</text>
				</view>
				<view class="tab-text">
					<text style="font-weight: 100;">{{checkStr(item.unionAnswerContent.analysis)}}</text>
				</view>
			</view>

		</view>

		<!-- 底栏切换 -->
		<view class="bottom-tab">
			<view @click="changeTab(0)">
				<uni-icons type="arrow-left" size="20" color="#909399" v-if="choiceQuestionIndex!=0"></uni-icons>
				<text v-if="choiceQuestionIndex!=0">上一题</text>
			</view>
			<view @click="popupShow = true">
				<uni-icons type="map" size="20" color="#909399"></uni-icons>
				<text>{{choiceQuestionIndex+1}}/{{questionAmount}}</text>
			</view>
			<view @click="changeTab(1)" v-if="choiceQuestionIndex+1!=questionAmount">
				<text>下一题</text>
				<uni-icons type="arrow-right" size="20" color="#909399"></uni-icons>
			</view>
			<view @click="changeTab(2)" v-if="choiceQuestionIndex+1==questionAmount&&flag!=1">
				<text>交卷</text>
				<uni-icons type="arrow-right" size="20" color="#909399"></uni-icons>
			</view>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 参数设置部分
				// 0：考试 1：查看答案
				flag: 0,
				// 考试用时
				recordDuration: 0,
				// 提醒时间-考试即将结束
				remindTime: 60,
				// 是否查看完所有问题
				isViewAll: false,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				isFinished: false,
				isShow: false,
				popupShow: false,
				scrollTop: 0,
				scrollTop1: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},

				service: "https://biapi.xiaoyujia.com/",

				choiceQuestionIndex: 0,
				questionAmount: 0,
				notCheckAmount: 0,
				memberId: null,
				recordId: 0,
				examId: 1,
				examDuration: 0,
				remainTime: 0,
				recordState: 0,
				postDate: [],
				as: 0,
				exam: {},
				getQuestionData: {},
				examQuestion: [],
				choiceCountList: [0, 1, 2, 3, 4, 5],
				questionTypeList: [{
					text: "单选题",
					value: 0,
					tips: "只能选择一个选项",
					style: {
						backgroundColor: '#F9AE3D'
					}
				}, {
					text: "多选题",
					value: 1,
					tips: "可以选择多个选项",
					style: {
						backgroundColor: '#19be6b'
					}
				}, {
					text: "判断题",
					value: 2,
					tips: "只能选择一个选项",
					style: {
						backgroundColor: '#909399'
					}
				}, {
					text: "填空题",
					value: 3,
					tips: "将空格处的答案补充完整",
					style: {
						backgroundColor: '#2979ff'
					}
				}, {
					text: "问答题",
					value: 4,
					tips: "开放答案，将由人工阅卷",
					style: {
						backgroundColor: '#ff4d4b'
					}
				}]
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 考试结束
			examEnd() {
				console.log("考试时间到!")
				this.$refs.uNotify.error("考试时间到！")
			},
			// 选择题目
			choiceTab(index) {
				this.choiceQuestionIndex = index
				this.popupShow = false
			},
			// 切换题目（上一题/下一题）
			changeTab(index) {
				if (index == 0) {
					if (this.choiceQuestionIndex == 0) {
						// this.$refs.uNotify.error("没有上一题了！")
						return
					}
					this.choiceQuestionIndex--
				} else if (index == 1) {
					this.choiceQuestionIndex++
					// 查看完所有答案之后进行提示
					if (this.choiceQuestionIndex + 1 == this.questionAmount && this.flag == 1 && !this.isViewAll) {
						this.afterViewAll()
					}
				} else if (index == 2) {
					if (this.choiceQuestionIndex == this.examQuestion.length - 1) {
						this.submitExam()
						return
					}
				}
			},
			// 选中选项
			clickChoice(index, index1) {
				console.log("选择选项！", index, "-", index1)
				let question = this.examQuestion[index]
				let questionType = question.questionType
				let choice = question.questionContent.choice
				// 查看答案解析中，不可选择
				if (this.flag == 1) {
					return
				}

				// 单选/判断
				if (questionType == 0 || questionType == 2) {
					this.examQuestion[index].isCheck = index1
				}
				// 多选
				else if (questionType == 1) {
					if (choice[index1].isCheck == 0) {
						this.examQuestion[index].questionContent.choice[index1].isCheck = 1
						this.examQuestion[index].questionContent.choiceCount++
					} else {
						this.examQuestion[index].questionContent.choice[index1].isCheck = 0
						this.examQuestion[index].questionContent.choiceCount--
					}

					// 清空所有多选项
					if (this.examQuestion[index].questionContent.choiceCount != 0) {
						this.examQuestion[index].isCheck = 1
					} else {
						this.examQuestion[index].isCheck = -1
					}
				}
				// 填空题
				else if (questionType == 3) {

				}
				// 问答题
				else if (questionType == 4) {

				}
			},
			// 格式化题目
			formatQuestion(index) {
				let question = this.examQuestion[index]
				let title = question.questionTitle
				let remark = question.questionRemark
				let result = title
				if (remark != null && remark != "") {
					result += "（" + remark + "）"
				}
				return result
			},
			// 格式化回答
			formatAnswer(answer) {
				return answer.replace(/\|/g, "、")
			},
			// 格式化答案
			formatExpect(index) {
				let result = ""
				let expect = this.examQuestion[index].unionAnswerContent.expect
				let keyword = this.examQuestion[index].unionAnswerContent.keyword

				if (keyword != null && keyword != "") {
					result = keyword + "（答案包含关键词即可得分）"
				} else {
					result = expect.replace(/\|/g, "、")
				}
				return result
			},
			// 格式化字符串
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			// 显示试题资源（图片或视频）
			showQuestionSource(value, index) {
				let result = false
				let questionImg = this.examQuestion[index].questionImg
				let questionVideo = this.examQuestion[index].questionVideo
				if (value == 0) {
					if (this.checkStr(questionImg) != '暂无' && this.checkStr(questionVideo) == '暂无') {
						result = true
					}
				} else if (value == 1) {
					if (this.checkStr(questionVideo) != '暂无') {
						result = true
					}
				}
				return result
			},
			// 打开图片预览
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 输入答案
			inputAnswer(value, index) {
				// 填空题
				if (value == 0) {
					this.examQuestion[index].isCheck = 1
				}
				// 问答题
				else if (value == 1) {
					let answerContent = this.examQuestion[index].answerContent
					this.examQuestion[index].isCheck = answerContent != "" ? 1 : -1
				}
			},
			// 检查未答的题目
			getNotCheck() {
				let count = 0
				let result = true
				for (let item of this.examQuestion) {
					if (item.isCheck == -1) {
						count++
						result = false
					}
				}
				this.notCheckAmount = count
				return result
			},
			// 查看所有答案后
			afterViewAll() {
				if (this.examId != 55 || this.exam.recordPassed == 1) {
					return
				}
				this.isViewAll = true
				uni.showModal({
					title: '已经查看完所有的错题啦！',
					content: '是否重新进行服务分规则考试？查漏补缺通过率更高哦！',
					success: res => {
						if (res.confirm) {
							this.http({
								outsideUrl: "https://api.xiaoyujia.com/exam/pushServiceScoreExam",
								method: 'post',
								data: {
									memberId: uni.getStorageSync("memberId") || null,
									employeeId: uni.getStorageSync("employeeId") || null,
								},
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								success: res => {
									if (res.code == 0) {
										uni.navigateTo({
											url: '/pages-mine/exam/exam-center'
										})
									} else {
										this.$refs.uNotify.warning(res.msg)
									}
								}
							})
						}
					}
				});
			},
			// 提交试卷前检查
			submitExam() {
				if (!this.getNotCheck()) {
					// this.$refs.uNotify.error("请答完所有题再交卷哦！")
					this.openCheck(1, "确定交卷吗？", "当前还有" + this.notCheckAmount + "题未回答完整")
					return
				} else {
					this.openCheck(1, "确定交卷吗？", "当前已经是最后一题了！")
				}
			},
			// 处理交卷答案
			dealPost() {
				var data = []
				// 拼接格式化各类试题的答案
				for (let item of this.examQuestion) {
					let questionId = item.id
					let answerContent = ""
					let questionType = item.questionType
					let isCheck = item.isCheck
					let answer = {}

					// 未选择，则直接跳过不提交该题
					if (isCheck == -1) {
						continue
					}

					// 单选/判断题
					if (questionType == 0 || questionType == 2) {
						let choice = item.questionContent.choice
						answerContent = choice[isCheck].flag
					}
					// 多选题
					else if (questionType == 1) {
						let choice = item.questionContent.choice
						for (let it of choice) {
							if (it.isCheck == 1) {
								if (answerContent != "") {
									answerContent += "|"
								}
								answerContent += it.flag
							}
						}
					}
					// 填空题
					else if (questionType == 3) {
						let content = item.questionContent.answerContent
						let choiceCount = item.questionContent.choiceCount || 0
						for (let i = 0; i < choiceCount; i++) {

							answerContent += content[i]
							if (i != choiceCount - 1) {
								answerContent += "|"
							}

						}
					}
					// 问答题
					else if (questionType == 4) {
						answerContent = item.answerContent
					}

					this.$set(answer, "questionId", questionId)
					this.$set(answer, "answerContent", answerContent)
					data.push(answer)
				}
				this.postDate = data
			},
			// 交卷
			answerAllQuestion() {
				if (this.isFinished) {
					this.$refs.uNotify.error("请勿重复交卷！")
				}
				this.updateUnionExamRecord()
				this.dealPost()
				this.isFinished = true
				this.http({
					url: 'answerAllQuestion',
					method: 'POST',
					path: this.recordId,
					data: this.postDate,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("已成功交卷！考试结束！")
							this.checkExamRecord()
							this.isFinished = true
						} else {
							this.$refs.uNotify.error(res.msg)
							this.isFinished = false
						}
					}
				});
			},
			// 结束考试
			checkExamRecord() {
				this.http({
					url: 'checkExamRecord',
					method: 'POST',
					data: {
						id: this.recordId,
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code != 0) {
							this.$refs.uNotify.error(res.msg)
						}
						// 跳转查看考试结果
						let timer = setTimeout(() => {
							return uni.navigateTo({
								url: "/pages-mine/exam/exam-result?id=" + this.recordId
							})
						}, 1000);
					}
				});
			},
			// 更新考试用时
			updateUnionExamRecord() {
				this.http({
					outsideUrl: this.service + 'unionExamRecord/updateUnionExamRecord',
					method: 'POST',
					hideLoading: true,
					data: {
						id: this.recordId,
						recordDuration: this.recordDuration
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.status == 200) {
							console.log('更新了一次考试记录！')
						}
					}
				});
			},
			// 获取考试记录
			listUnionExamRecord() {
				this.http({
					url: 'listUnionExamRecord',
					method: 'POST',
					data: {
						id: this.recordId,
						current: 1,
						size: 100
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.exam = res.data[0]
							this.examId = this.exam.examId
							this.recordState = this.exam.recordState
							// this.recordDuration = parseInt(this.exam.recordDuration) || 0
							// 每次重新读题则重新计时，不追溯原有记录
							this.recordDuration = 0
							this.examDuration = parseInt(this.exam.examDuration) || 0
							this.remainTime = parseInt(this.examDuration * 60 - this.recordDuration)

							if (this.flag == 0) {
								this.getExamQuestion()
							} else if (this.flag == 1) {
								this.getExamQuestionAnalysis()
							}
						} else {
							console.log('获取考试记录-返回错误！' + res.msg)
						}
					}
				});
			},
			// 获取考试试题解析
			getExamQuestionAnalysis() {
				this.http({
					url: 'getExamQuestionAnalysis',
					method: 'POST',
					data: {
						id: this.recordId,
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.examQuestion = res.data
							this.questionAmount = this.examQuestion.length
						} else {
							console.log('获取考试试题列表-返回错误！' + res.msg)
						}
					}
				});
			},
			// 获取考题
			getExamQuestion() {
				this.$set(this.getQuestionData, "id", this.recordId)
				this.$set(this.getQuestionData, "examId", this.examId)
				this.$set(this.getQuestionData, "memberId", this.memberId)
				this.$set(this.getQuestionData, "state", this.as)
				this.http({
					url: 'getExamQuestion',
					method: 'POST',
					data: this.getQuestionData,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.examQuestion = res.data
							this.questionAmount = this.examQuestion.length
						} else {
							this.$refs.uNotify.error("考题获取失败！请返回后重试！" + res.msg)
						}
					}
				});
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：xxx确认
				if (this.checkType == 0) {

				} else if (this.checkType == 1) {
					this.answerAllQuestion()
				}
			},
		},
		computed: {
			// 格式化样式
			formatStyle() {
				return (value, index) => {
					let style = ""
					let recordState = this.recordState
					// 题目选框样式
					if (value == 0) {
						let isCheck = this.examQuestion[index].isCheck
						if (isCheck != -1) {
							style = {
								backgroundColor: '#19be6b',
								color: '#fff'
							}
							if (this.choiceQuestionIndex == index) {
								style = {
									color: '#19be6b',
									border: '#19be6b 4rpx dashed',
									backgroundColor: '#fff'
								}
							} else {

							}

							// 根据答题正确和错误显示不同样式
							if (this.flag == 1) {
								let isRight = this.examQuestion[index].answerRecord.isRight
								if (isRight == 0) {
									if (this.choiceQuestionIndex == index) {
										style = {
											color: '#ff4d4b',
											border: '#ff4d4b 4rpx dashed',
											backgroundColor: '#fff'
										}
									} else {
										style = {
											color: '#fff',
											backgroundColor: '#ff4d4b'
										}
									}
								} else if (isRight == 1) {
									if (this.choiceQuestionIndex == index) {
										style = {
											color: '#19be6b',
											border: '#19be6b 4rpx dashed',
											backgroundColor: '#fff'
										}
									} else {
										style = {
											color: '#fff',
											backgroundColor: '#19be6b'
										}
									}
								}
							}
						} else {
							if (this.choiceQuestionIndex == index) {
								style = {
									backgroundColor: '#fff',
									border: '#1e1848 4rpx dashed',
									color: '#000'
								}
							} else {

							}
						}
					}
					// 选项区域样式
					else if (value == 1) {
						// 若正在考试，不显示答案解析
						if (this.flag == 0) {
							style = {
								// boxShadow: '0 0rpx 0rpx #dedede',
								height: '1200rpx',
							}
						} else if (this.flag == 1) {

						}
					}
					// 题目类型标签颜色
					else if (value == 2) {
						let type = this.examQuestion[index].questionType
						style = this.questionTypeList[type].style
					}
					return style
				}
			},
		},
		mounted() {

		},
		onLoad(options) {
			this.recordId = options.id || -1
			this.flag = options.flag || 0
			this.as = options.as || 0
			this.memberId = uni.getStorageSync("memberId")

			// 测试时加上
			// this.recordId = 22
			// this.flag = 1
			// this.service = "http://localhost:8063/"

			this.listUnionExamRecord()
			if (this.flag == 0) {
				// 开启计时器（仅开始考试时）
				let time = setInterval(() => {
					if (this.examDuration * 60 - this.recordDuration > 0) {
						this.recordDuration += 1
						this.remainTime = parseInt(this.examDuration * 60 - this.recordDuration)
					}
				}, 1000);
			}
			// 查看答案
			else if (this.flag == 1) {
				uni.setNavigationBarTitle({
					title: "答案解析"
				})
			}
		},
		watch: {
			remainTime: {
				handler(newValue, oldVal) {
					if (this.flag != 0) {
						return
					}

					// 自动交卷
					if (this.remainTime == 0) {
						this.answerAllQuestion()
					}

					if (this.remainTime == 60) {
						this.openCheck(0, "注意考试时间哦！", "仅剩余1分钟，结束后将自动交卷！")
					}
					if (this.remainTime == 300) {
						this.openCheck(0, "注意考试时间哦！", "还剩余5分钟，请抓紧时间！")
					}
				},
				deep: true
			},
		},
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/exam.scss";

	.countdown {
		width: 90%;
		line-height: 100rpx;
		height: 100rpx;
		margin: 0 5%;
	}

	.tag-small {
		position: absolute;
		width: 100%;

		text {
			position: absolute;
			padding: 0 20rpx;
			left: 40rpx;
			top: 130rpx;

			height: 60rpx;
			line-height: 60rpx;
			width: auto;

			font-size: 28rpx;
			border-radius: 30rpx;
			color: #F9AE3D;
			background-color: rgba(254, 251, 251, 0.8);
		}
	}
</style>