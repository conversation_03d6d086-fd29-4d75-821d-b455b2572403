<template>
	 <view>
	        <jushi-signature :settings="settings" @change="signatureChange"></jushi-signature>
	        <view class="" style="margin-top: 20rpx;">
	            <text class="text">保存后的签名图片</text>
	            <view class="preview">
	                <image :src="imgUrl" mode="" style="width: 100%;"></image>
	            </view>
	        </view>
			<u-button customStyle="color:#f6cc70" color="#1e1848" @click="submitMsg" text="上传签名"></u-button>
			
			<!-- 弹窗展示图片 -->
			<view v-if="showPopup" class="popup-overlay" @click="closePopup">
				<view class="popup-content" @click.stop>
					<view class="close-btn" @click="closePopup">
						<text class="close-icon">×</text>
					</view>
					<view class="popup-body">
						<image 
							:src="popupImageUrl" 
							mode="widthFix" 
							class="popup-image"
							@error="onImageError"
						></image>
					</view>
				</view>
			</view>
	    </view>
</template>
<script>
	import {
		imgBase64
	} from '@/pages-work/business/js/imgUrlTool.js'
    export default {
        data() {
            return {
				billNo: '',
				id: null,
                settings:{ //签名设置
                    width: '750',//签名区域的宽
                    height: '500',//签名区域的高
                    lineWidth:3,//签名时线宽
                    textColor:'#007AFF' //签名文字颜色
                },
                imgUrl: '',
				showPopup: false,
				popupImageUrl: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jjlm/jjlm_sy.jpg'
            }
        },
		onLoad(option) {
			this.billNo = option.billNo
			this.id = option.id
			
		},
        methods: {
            signatureChange(e) {
				// #ifdef MP-WEIXIN
				let type = "jpg"
				if (!!uni.getFileSystemManager()) {
				    uni.getFileSystemManager().readFile({
				        filePath: e, //选择图片返回的相对路径
				        encoding: "base64", //编码格式
				        success: (res) => {
							this.imgUrl = "data:image/" +
				                    type.toLocaleLowerCase() +
				                    ";base64," +
				                    res.data
				        },
				    });
				} else {
				    uni.request({
				        url: e,
				        method: "GET",
				        responseType: "arraybuffer",
				        success: (ress) => {
				            let base64 = wx.arrayBufferToBase64(ress.data); //把arraybuffer转成base64
				            this.imgUrl = "data:image/jpeg;base64," + base64; //不加上这串字符，在页面无法显示的
				        },
				    });
				}
				// #endif
				
				// #ifdef H5
				this.imgUrl = e
				// #endif
            },
			submitMsg() {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/system/imageUploadByBase64',
					method: 'POST',
					data:{
						base64: this.imgUrl
					},
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					success: res=>{
						if(res.code==0){
							this.linkJump(res.data)
						}
					}
				})
			},
			linkJump(imgUrl){
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/serviceAcceptanceForm/linkJump?orderNo='+this.billNo,
					method: 'GET',
					success: res=>{
						if(res.status==200){
							this.http({
									url: 'saveOrderQm',
									method: 'POST',
									data:{
										productPicture: res.data.productPicture,
										productName: res.data.productName,
										productType: res.data.productType,
										productPrice: res.data.productPrice,
										orderNo: this.billNo,
										serviceProject: res.data.serviceProject,
										serviceNumberOfPeople: res.data.serviceNumberOfPeople,
										isSOP:	res.data.isSOP,
										autographPicture: imgUrl
							
									},
									header: {
										'content-type': "application/json;charset=UTF-8"
									},
								success: res=>{
									if(res.code==0){
									uni.showToast({
										icon:'none',
										title: '上传签名成功!'
									})
									setTimeout(()=>{
									// return	uni.redirectTo({
									// 		url: '../order/orderDetail?id=' + this.id
									// 	})
									// 页面加载完成后显示弹窗
									this.showPopup = true
									},2000)
									}
								}
							})
						}
					}
				})
			},
			// 关闭弹窗
			closePopup() {
				this.showPopup = false
			},
			// 图片加载失败处理
			onImageError() {
				console.log('弹窗图片加载失败')
			}
        }
    }
</script>

<style>
    .preview{
        margin: 10rpx;
        border: 1rpx solid #aaaaaa;
        border-radius: 10rpx;
    }
    .text {
        margin: 20rpx;
        color: #aaaaaa;
    }
	
	/* 弹窗样式 */
	.popup-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.7);
		z-index: 9999;
	}
	
	.popup-content {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		background-color: white;
		border-radius: 24rpx;
		width: 90%;
		max-width: 700rpx;
		max-height: 85vh;
		overflow: hidden;
		box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.2);
	}
	
	.close-btn {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		width: 60rpx;
		height: 60rpx;
		background-color: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		z-index: 10;
	}
	
	.close-icon {
		color: white;
		font-size: 36rpx;
		font-weight: bold;
		width: 60rpx;
		height: 60rpx;
		text-align: center;
		line-height: 60rpx;
		display: block;
	}
	
	.popup-body {
		padding: 0;
		text-align: center;
		background-color: white;
		border-radius: 24rpx;
		overflow: hidden;
	}
	
	.popup-image {
		width: 100%;
		border-radius: 24rpx;
		display: block;
	}
</style>