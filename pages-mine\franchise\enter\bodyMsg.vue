<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 认证类型选择器-->
		<view>
			<u-picker :show="showPickerB" :defaultIndex="defaultIndex" :columns="columnsB" @cancel="showPickerB = false"
				@confirm="confirmPickerB">
			</u-picker>
		</view>

		<!--营业执照底部弹窗-->
		<u-popup :show="popupShow" mode="bottom" @close="popupShow = false">
			<view class="textA">
				<text>上传照片示例</text>
			</view>
			<view class="icon-state">
				<u-icon @click="popupShow = false" size="20" name="close"></u-icon>
			</view>
			<view class="text-popup">
				<image @click="updateImg" class="imgD" :src="examplePhotoImgUrl" />
				<view>
					<text class="text-up">点击上方任意示例照片位置开始</text>
				</view>
				<view>
					<text>1.需提供三证合一的营业执照原件扫描件或加盖公司公章的复印件</text>
				</view>
				<view>
					<text>2.确保未在企业经营异常名录中，且所售商品在营业执照经营范围内</text>
				</view>
				<view>
					<text>3.营业执照有效期截止时间距今应大于3个月</text>
				</view>
				<view>
					<text>4.露出证件四角，请勿遮挡或模糊，保持信息清晰可见</text>
				</view>
				<view>
					<text>5.图片尺寸为800*800px以上，支持PNG、JPG和JPEG格式，大小不超过5M</text>
				</view>
				<view>
					<u-link class="ulink"></u-link>
				</view>
			</view>
		</u-popup>

		<!--身份证正面底部弹窗-->
		<u-popup :show="popupShowB" mode="bottom" @close="popupShowB = false">

			<view class="textA">
				<text>上传照片示例</text>
			</view>
			<view class="icon-state">
				<u-icon class="icon-state" @click="popupShowB = false" size="20" name="close"></u-icon>
			</view>
			<view class="text-popup">
				<view class="iconClass">
					<view>
						<u-icon name="checkmark-circle-fill" color="#1e1848" label="四角完整"></u-icon>
					</view>
					<view style="height: 20rpx;" />
					<view>
						<u-icon name="checkmark-circle-fill" color="#1e1848" label="亮度均匀"></u-icon>
					</view>
					<view style="height: 20rpx;" />
					<view>
						<u-icon name="checkmark-circle-fill" color="#1e1848" label="照片清晰"></u-icon>
					</view>
				</view>
				<image @click="updateIdentity(0)" class="imgA" :src="iCardEgA" />
				<image @click="updateIdentity(0)" class="imgAA" :src="iCardEgB" />
				<view>
					<text class="text-up">点击上方任意示例照片位置开始</text>
				</view>
				<view>
					<text>证件文字能清晰辨认，勿遮挡或模糊，参考示例照片拍摄；格式jpg、png、jpeg，小于5M。</text>
				</view>
				<view>
					<u-link class="ulink"></u-link>
				</view>
			</view>
		</u-popup>

		<!--身份证背面底部弹窗-->
		<u-popup :show="popupShowC" mode="bottom" @close="popupShowC = false">
			<view class="textA">
				<text>上传照片示例</text>
			</view>
			<view class="icon-state">
				<u-icon class="icon-state" @click="popupShowC = false" size="20" name="close"></u-icon>
			</view>
			<view class="text-popup">
				<view class="iconClass">
					<view>
						<u-icon name="checkmark-circle-fill" color="#1e1848" label="四角完整"></u-icon>
					</view>
					<view style="height: 20rpx;" />
					<view>
						<u-icon name="checkmark-circle-fill" color="#1e1848" label="亮度均匀"></u-icon>
					</view>
					<view style="height: 20rpx;" />
					<view>
						<u-icon name="checkmark-circle-fill" color="#1e1848" label="照片清晰"></u-icon>
					</view>
				</view>
				<image @click="updateIdentity(1)" class="imgA" :src="iCardEgC" />
				<image @click="updateIdentity(1)" class="imgAA" :src="iCardEgD" />
				<view>
					<text class="text-up">点击上方任意示例照片位置开始</text>
				</view>
				<view>
					<text>证件文字能清晰辨认，勿遮挡或模糊，参考示例照片拍摄；格式jpg、png、jpeg，小于5M。</text>
				</view>
				<view>
					<u-link class="ulink"></u-link>
				</view>
			</view>
		</u-popup>

		<view class="shadow">
			<image class="img" :src="enterImgUrl" />
			<view class="text-state">
				<text>检测到你还未入驻联盟，现只需5步即可完成入驻</text>
			</view>
			<u-steps activeColor="#1e1848" current="1" class="steps">
				<u-steps-item title="认证缴费" />
				<u-steps-item title="主体信息" />
				<u-steps-item title="商户信息" />
				<u-steps-item title="平台审核" />
				<u-steps-item title="账户验证" />
			</u-steps>
			<view style="height: 50rpx;">
			</view>
		</view>

		<view class="shadow">
			<view class="companyMsg">
				<text>公司信息</text>
			</view>

			<text class="authentication-type">认证类型</text>
			<view class="input-state" @click="showPickerB = true">
				<u--input border="bottom" disabled placeholder="请选择认证类型" v-model="pickerData"
					disabledColor="Light#f4f4f5"></u--input>
				<text class="text-arrow">❯</text>
			</view>

			<view v-if="authenticationType==2">
				<text class="companyMsg">营业执照照片</text>
				<view class="text-stateB">
					<text>新办理的营业执照，可能因国家市场监督管理总局信息更新有延迟，建议在办理成功后等待至少14个工作日后在再进行认证</text>
				</view>

				<!-- 上传营业执照 -->
				<view class="uploadA" v-if="imgFlag==2">
					<img class="upload-imgB" :src="upImgUrl" @click="popupShow = true">
				</view>

				<view class="upload-img" v-if="imgFlag==1">
					<view>
						<image @click="popupShow = true" class="img" :src="upImgUrl" />
					</view>
				</view>

				<view class="text-stateC">营业执照照片</text></view>
				<view class="companyMsg">公司名称</text></view>
				<view class="input-state">
					<u--input placeholder="上传营业执照自动识别" v-model="companyName" disabled border="bottom"
						disabledColor="Light#f4f4f5"></u--input>
				</view>

				<view class="companyMsg">统一社会信用代码</text></view>
				<view class="input-state">
					<u--input placeholder="上传营业执照自动识别" border="bottom" disabled v-model="creditCode"
						disabledColor="Light#f4f4f5">
					</u--input>
				</view>

				<view class="companyMsg">营业期限</text></view>
				<view class="input-state">
					<u--input placeholder="上传营业执照自动识别" border="bottom" disabled ref="kkl" disabledColor="Light#f4f4f5"
						v-model="doBusinessTime"></u--input>
				</view>

				<view class="companyMsg">经营地址</text></view>
				<view class="input-stateB">
					<u--input placeholder="上传营业执照自动识别" disabled border="bottom" v-model="doBusinessAddress"
						disabledColor="Light#f4f4f5"></u--input>
				</view>
			</view>
		</view>
		<view class="shadow" v-if="authenticationType==1">
			<view class="companyMsg">
				<text>个人画像信息</text>
			</view>
			<view style="height: 10rpx;"></view>
			<view class="companyMsg">个人介绍</text></view>
			<view class="input-state">
				<u--input placeholder="请输入个人介绍" v-model="selfIntroduction" border="bottom" disabledColor="Light#f4f4f5">
				</u--input>
			</view>

			<view class="companyMsg">所在地区</text></view>
			<view class="input-state">
				<u--input placeholder="请输入所在地区" v-model="nowAdd" border="bottom" disabledColor="Light#f4f4f5">
				</u--input>
			</view>

			<view class="companyMsg">从事职业</text></view>
			<view class="input-state">
				<u--input placeholder="请输入从事职业" v-model="career" border="bottom" disabledColor="Light#f4f4f5">
				</u--input>
			</view>

			<view class="companyMsg">年收入范围</text></view>
			<view class="input-state">
				<u--input placeholder="请输入年收入范围" v-model="income" border="bottom" disabledColor="Light#f4f4f5">
				</u--input>
			</view>

		</view>


		<view class="shadow">
			<view class="companyMsg">
				<text>经营者信息</text>
			</view>
			<view style="height: 10rpx;"></view>
			<text class="companyMsgC">身份证照片</text>
			<view style="height: 5rpx;"></view>
			<view class="upload">
				<img class="upload-imgB" :src="identityFront" @click="popupShowB = true">
			</view>

			<view class="uploadB">
				<img class="upload-imgB" :src="identityBack" @click="popupShowC = true">
			</view>

			<view style="height: 30rpx;"></view>

			<text class="text-stateE">身份证人面像</text>
			<text class="text-stateD">身份证国徽面</text>

			<view class="companyMsg">经营者姓名</text></view>
			<view class="input-state">
				<u--input placeholder="上传经营者身份证自动识别" v-model="name" border="bottom" disabled
					disabledColor="Light#f4f4f5"></u--input>
			</view>

			<view class="companyMsg">经营者归属地</text></view>
			<view class="input-state">
				<u--input placeholder="上传经营者身份证自动识别" v-model="hometown" border="bottom" disabled
					disabledColor="Light#f4f4f5"></u--input>
			</view>

			<view class="companyMsg">经营者身份证号码</text></view>
			<view class="input-state">
				<u--input placeholder="上传经营者身份证自动识别" v-model="idCard" border="bottom" disabled
					disabledColor="Light#f4f4f5"></u--input>
			</view>

			<view class="companyMsg">证件开始日期</text></view>
			<view class="input-state">
				<u--input placeholder="上传经营者身份证自动识别" disabled v-model="iCardStartTime" border="bottom" disabled
					disabledColor="Light#f4f4f5"></u--input>
			</view>

			<view class="companyMsg">证件截止日期</text></view>
			<view class="input-state">
				<u--input placeholder="上传经营者身份证自动识别" disabled v-model="iCardEndTime" border="bottom" disabled
					disabledColor="Light#f4f4f5"></u--input>
			</view>

			<view class="companyMsg">介绍人</text></view>
			<view class="input-state">
				<u--input placeholder="暂无" disabled v-model="memberId" border="bottom" disabled
					disabledColor="Light#f4f4f5"></u--input>
			</view>

			<view class="link-state">
				<u--input disabled border="bottom" disabled disabledColor="Light#f4f4f5"></u--input>
			</view>
			<image class="imgB" :src="serviceImgUrl" @click="openChat()" />
			<image class="imgC" @click="toEnterStrategy" :src="strategyImgUrl" />
			<text class="text-stateF" @click="openChat()">联系客服</text>
			<text class="text-stateG" @click="toEnterStrategy">入驻攻略</text>
			<view class="button-state">
				<u-button customStyle="color:#f6cc70" color="#1e1848" @click="submitMsg" text="下一步"></u-button>
			</view>
			<view style="height: 25rpx;">
			</view>
		</view>

	</view>
</template>
<script>
	import {
		pathToBase64,
		base64ToPath
	} from '@/pages-mine/common/js/image-tools/index.js'
	export default {
		data() {
			return {
				imgFlag: 1,
				enterImgUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-rz.png",
				examplePhotoImgUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1666330605149examplePhoto.jpg",
				serviceImgUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1666330623335service.png",
				strategyImgUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1666330634959strategy.png",
				upImgUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1666330663768up.png",
				iCardEgA: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1666771573419身份证eg1.jpg",
				iCardEgB: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/*************身份证eg2.jpg",
				iCardEgC: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1667472450425ae4494150d34ca9e922ffeb54181bbaf(1)(1)(1).png",
				iCardEgD: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1667472590381ae4494150d34ca9e922ffeb54181bbaf(1).jpg",
				showPickerB: false,
				hometown: "",
				name: "",
				idCard: "",
				unionFlag: 1,
				productId: null,
				authenticationTypeFlag: null,
				bankReservedPhone: null,
				defaultIndex: [1],
				sex: null,
				birthTime: null,
				iCardStartTime: "",
				iCardEndTime: "",
				columnsB: [
					['企业', '个体']
				],
				companyName: "",
				creditCode: "",
				msgType: "success",
				pickerData: "个体",
				authenticationType: 1,
				msgText: "",
				isAuth: 0,
				show: false,
				popupShow: false,
				popupShowB: false,
				popupShowC: false,
				doBusinessTime: "",
				selfIntroduction: "",
				nowAdd: "",
				career: "",
				income: "",
				doBusinessAddress: "",
				legalPerson: "",
				type: null,
				mode: 'single',
				memberId: uni.getStorageSync("memberId"),
				idcard1: [{
					content: ""
				}],
				idcard2: [{
					content: ""
				}],
				identityFront: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663226047582identity-front.png",
				identityBack: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663224975448identity-back.png",
				idPositive: "",
				idBack: "",
				businessLicenseUrl: "",
				uploadUrl: null,
				authFlag: null,
			}
		},
		onLoad(option) {
			this.authFlag = option.authFlag
			console.log("this.authFlag---------->", this.authFlag);
			if (option.scene !== undefined) {
				let scene = decodeURIComponent(option.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				this.isAuth = obj.isAuth || null
			}
			this.isAuth = option.isAuth && !this.isAuth ? option.isAuth : this.isAuth
			console.log("this.isAuth--------------》", this.isAuth);
			this.type = option.type
			if (option.type != 3) {
				this.checkLogin()
			}
		},
		methods: {
			checkLogin() {
				console.log("正在检查登录状态...")
				if (!uni.getStorageSync('memberId')) {
					this.$toast.toast('还未登录哦，请重新登录')
					if (this.isAuth != 1) {
						uni.setStorageSync('redirectUrl', '/pages-mine/franchise/enter/bodyMsg')
					} else {
						uni.setStorageSync('redirectUrl', '/pages-mine/index/index')
					}
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						})
					}, 2000)
				} else {
					if (this.isAuth == 1) {
						this.http({
							url: 'openMemberIsAuth',
							data: {
								id: uni.getStorageSync("memberId"),
							},
							header: {
								"content-type": "application/json;charset=UTF-8"
							},
							method: 'POST',
							success: res => {
								uni.showToast({
									title: res.msg,
									icon: 'none'
								})
							}
						})
						setTimeout(() => {
							//校验是否有付款
							this.http({
								url: "getFranchiseMsgById",
								method: 'GET',
								path: uni.getStorageSync("memberId"),
								hideLoading: true,
								success: res => {
									if (res.code == 0) {
										this.bankReservedPhone = res.data.bankReservedPhone
										this.authenticationTypeFlag = res.data.authenticationType
										//是否付款  1：已付款
										if (res.data.flag !== 1 && this.authFlag != 1 && !this
											.isAuth == 1) {
											uni.redirectTo({
												url: '/pages-mine/franchise/enter/topUpMoney'
											})
										} else {
											this.productId = res.data.productId
											this.getIfEmployeeByMemberId()
											setTimeout(() => {
												if (!this.authenticationTypeFlag && this
													.unionFlag !== 2) {
													return
												}
												if (!this.bankReservedPhone && this.type !=
													1) {
													//跳转账户验证
													return uni.redirectTo({
														url: '/pages-mine/franchise/enter/platformAudit'
													})
												}
												if (this.unionFlag == 2) {
													this.$toast.toast('您已入驻！请勿重复操作!')
													setTimeout(() => {
														uni.redirectTo({
															url: '/pages-work/index'
														})
													}, 2000)
												}
											}, 1000)
										}
									}
								},
								fail: err => {
									console.log('请求失败！' + res)
								}
							})
						}, 2000)
					} else {
						//校验是否有付款
						this.http({
							url: "getFranchiseMsgById",
							method: 'GET',
							path: uni.getStorageSync("memberId"),
							hideLoading: true,
							success: res => {
								if (res.code == 0) {
									this.bankReservedPhone = res.data.bankReservedPhone
									this.authenticationTypeFlag = res.data.authenticationType
									//是否付款  1：已付款
									if (res.data.flag !== 1 && this.authFlag != 1 && this.isAuth !== 1) {
										uni.redirectTo({
											url: '/pages-mine/franchise/enter/topUpMoney'
										})
									} else {
										this.productId = res.data.productId
										this.getIfEmployeeByMemberId()
										setTimeout(() => {
											if (!this.authenticationTypeFlag && this.unionFlag !== 2) {
												return
											}
											if (!this.bankReservedPhone && this.type != 1) {
												//跳转账户验证
												return uni.redirectTo({
													url: '/pages-mine/franchise/enter/platformAudit'
												})
											}
											if (this.unionFlag == 2) {
												this.$toast.toast('您已入驻！请勿重复操作!')
												setTimeout(() => {
													uni.redirectTo({
														url: '/pages-work/index'
													})
												}, 2000)
											}
										}, 1000)
									}
								}
							},
							fail: err => {
								console.log('请求失败！' + res)
							}
						})
					}
				}
			},
			//如果是员工并开通体验权限就生成商户信息
			getIfEmployeeByMemberId() {
				this.http({
					outsideUrl: "https://api.xiaoyujia.com/unionMerchant/getIfEmployeeByMemberId",
					method: 'GET',
					data: {
						memberId: uni.getStorageSync("memberId"),
						agent: this.agent,
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							//返回值为2  则生成商户信息成功或存在商户信息
							this.unionFlag = res.data
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			},
			openChat() {
				// #ifdef  MP-WEIXIN
				console.log("开始微信聊一聊")
				wx.openCustomerServiceChat({
					extInfo: {
						url: 'https://work.weixin.qq.com/kfid/kfc1647873dd6ff2741' //客服地址链接
					},
					corpId: 'wx25c9a236883d741d', //必须和你小程序上的一致
					success(res) {
						console.log(res, 1)
					},
					fail(res) {
						console.log(res, 2)
					},
				})
				// #endif
				// #ifdef  APP-PLUS || H5
				console.log("开始H5聊一聊")
				let param = {
					url: 'https://work.weixin.qq.com/kfid/kfc1647873dd6ff2741'
				}
				let data = JSON.stringify(param)
				uni.navigateTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
				})
				// #endif
			},
			updateICad(val) {
				if (val == 1) {
					this.uploadUrl = this.idPositive
				} else {
					this.uploadUrl = this.idBack
				}
				let url = "https://api.xiaoyujia.com/system/imageUpload"
				uni.uploadFile({
					url: url,
					filePath: this.uploadUrl,
					name: 'file',
					formData: {
						route: 'idCard'
					},
					dataType: 'json',
					success: (uploadFileRes) => {
						let result = JSON.parse(uploadFileRes.data)
						if (val == 1) {
							this.idPositive = result.data
						} else {
							this.idBack = result.data
						}
					},
					fail: err => {
						console.log('上传图片错误!!!')
					}
				});
			},
			// 上传营业执照
			updateImg() {
				let url = "https://api.xiaoyujia.com/system/imageUpload"
				uni.chooseImage({
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {
								route: 'idCard'
							},
							dataType: 'json',
							success: (uploadFileRes) => {
								this.companyName = ""
								this.creditCode = ""
								this.doBusinessTime = ""
								this.legalPerson = ""
								this.doBusinessAddress = ""
								let result = JSON.parse(uploadFileRes.data)
								this.examplePhotoImgUrl = result.data
								this.upImgUrl = result.data
								this.imgFlag = 2
								this.businessLicenseUrl = result.data
								let url = "OcrBusinessLicense"
								let param = {
									imageUrl: this.businessLicenseUrl
								}
								// 请求：营业执照识别
								this.http({
									url: url,
									method: 'POST',
									header: {
										"content-type": "application/json;charset=UTF-8"
									},
									data: param,
									success: res => {
										// 请求成功之后
										if (res.code == 0) {
											let {
												entname,
												creditCode,
												opfrom,
												opto,
												verifyDom,
												frname,
											} = res.data || {}
											opto = opto || '至今';
											this.companyName = entname
											this.creditCode = creditCode
											this.doBusinessTime = `自${opfrom}至${opto}`
											this.doBusinessAddress = verifyDom
											this.legalPerson = frname
										} else {
											this.businessLicenseUrl = ""
											this.imgFlag = 1
											this.upImgUrl =
												"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1666330663768up.png"
											this.examplePhotoImgUrl =
												"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1666330605149examplePhoto.jpg"
										}
									},
									fail: err => {
										this.$refs.uNotify.error("请求失败")
									}
								})
								this.popupShow = false
							},
							fail: err => {
								console.log('上传图片错误!!!')
							}
						});
					}
				});
			},
			urlTobase64(url) {
				const imgData = uni.getFileSystemManager().readFileSync(url, 'base64')
				const base64 = 'data:image/jpeg;base64,' + imgData
				return base64
			},
			updateIdentity(value) {
				let url = "https://api.xiaoyujia.com/system/imageUpload"
				uni.chooseImage({
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						// #ifdef  H5
						pathToBase64(tempFilePaths)
							.then(base64 => {
								if (value == 0) {
									this.sex = null
									this.birthTime = null
									this.name = ""
									this.idCard = ""
									this.hometown = ""
									this.identityFront = tempFilePaths[0]
									this.iCardEgA = tempFilePaths[0]
									this.idPositive = tempFilePaths[0]
									this.idcard1[0].content = base64
									this.vidcardNext()
								} else if (value == 1) {
									this.iCardStartTime = ""
									this.iCardEndTime = ""
									this.identityBack = tempFilePaths[0]
									this.idBack = tempFilePaths[0]
									this.iCardEgC = tempFilePaths[0]
									this.idcard2[0].content = base64
									this.vidcard1Next()
								}
							})
							.catch(error => {
								if (value == 0) {
									this.sex = null
									this.birthTime = null
									this.popupShowB = false
									this.name = ""
									this.idCard = ""
									this.hometown = ""
									this.idcard1[0].content = ""
									this.iCardEgA =
										"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1666771573419身份证eg1.jpg"
									this.identityFront =
										"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663226047582identity-front.png"
									this.idPositive = ""
								} else {
									this.popupShowC = false
									this.iCardStartTime = ""
									this.iCardEndTime = ""
									this.idcard2[0].content = ""
									this.iCardEgC =
										"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1667472450425ae4494150d34ca9e922ffeb54181bbaf(1)(1)(1).png"
									this.identityBack =
										"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663224975448identity-back.png"
									this.idBack = ""
								}
								this.$refs.uNotify.error("解析异常,请重新上传！");
								console.error("转化失败！")
							})
						// #endif
						// #ifdef  MP-WEIXIN
						const base64 = this.urlTobase64(tempFilePaths[0])
						if (value == 0) {
							this.identityFront = tempFilePaths[0]
							this.idPositive = tempFilePaths[0]
							this.idcard1[0].content = base64
							this.vidcardNext()
						} else if (value == 1) {
							this.identityBack = tempFilePaths[0]
							this.idcard2[0].content = base64
							this.idBack = tempFilePaths[0]
							this.vidcard1Next()
						}
						// #endif
					}
				});
			},
			// 识别身份证正面
			vidcardNext() {
				let url = "https://agentapi.xiaoyujia.com/files/imgsToIdInfo"
				console.log("读取图片后进行请求！")
				// 请求：识别身份证
				let data = {
					file: this.idcard1[0].content,
					cardTpe: 0
				}
				uni.request({
					url: url,
					method: 'POST',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: JSON.stringify(data),
					success: res => {
						// 请求成功之后
						let status = res.data.image_status;
						if (status === 'normal') {
							this.$refs.uNotify.success("身份证人面像上传成功!")
							this.name = res.data.words_result.姓名.words
							this.idCard = res.data.words_result.公民身份号码.words
							this.hometown = res.data.words_result.住址.words
							if (res.data.words_result.性别.words == '女') {
								this.sex = 2
							} else {
								this.sex = 1
							}
							this.birthTime = this.getMyTime(res.data.words_result.出生.words)
							this.popupShowB = false
							this.updateICad(1)
						} else if (status === 'unknown' && res.data.idcard_number_type === 0) {
							// this.name = res.data.words_result.姓名.words
							// this.idCard = res.data.words_result.公民身份号码.words
							// this.hometown = res.data.words_result.住址.words
							// this.$refs.uNotify.error("因第三方接口不识别，请自行输入身份证号码！")
							console.log("res.data------------------------->", res.data);
						} else {
							let msg = '身份证认证失败，请重新上传';
							if (status != null) {
								if (status === 'reversed_side') {
									msg = '身份证应上传照片面'
								}
								if (status === 'non_idcard') {
									msg = '上传的图片中不包含身份证'
								}
								if (status === 'blurred') {
									msg = '身份证模糊'
								}
								if (status === 'other_type_card') {
									msg = '其他类型证照'
								}
								if (status === 'over_exposure') {
									msg = '身份证关键字段反光或过曝'
								}
								if (status === 'over_dark') {
									msg = '身份证欠曝（亮度过低）'
								}
							}
							this.iCardEgA =
								"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1666771573419身份证eg1.jpg"
							this.identityFront =
								"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663226047582identity-front.png"
							this.idPositive = ""
							this.popupShowB = false
							this.idcard1[0].content = ""
							this.$refs.uNotify.error(msg)
						}
					},
					fail: err => {
						console.log('主体信息身份证上传-请求失败！' + res.data.code)
					}
				})
			},
			// 识别身份证背面
			vidcard1Next() {
				let url = "https://agentapi.xiaoyujia.com/files/imgsToIdInfo"
				// 请求：识别身份证
				let data = {
					file: this.idcard2[0].content,
					cardTpe: 1
				}
				uni.request({
					url: url,
					method: 'POST',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: JSON.stringify(data),
					success: res => {
						// 请求成功之后
						let status = res.data.image_status;
						if (status === 'normal') {
							this.$refs.uNotify.success("身份证国徽面上传成功!")
							this.iCardStartTime = this.getMyTime(res.data.words_result.签发日期.words)
							if (res.data.words_result.失效日期.words == '长期') {
								this.iCardEndTime = res.data.words_result.失效日期.words
							} else {
								this.iCardEndTime = this.getMyTime(res.data.words_result.失效日期.words)
							}
							this.popupShowC = false
							this.updateICad(2)
						} else {
							this.iCardEgC =
								"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1667472450425ae4494150d34ca9e922ffeb54181bbaf(1)(1)(1).png"
							this.identityBack =
								"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663224975448identity-back.png"
							this.idBack = ""
							this.popupShowC = false
							this.idcard2[0].content = ""
							this.$refs.uNotify.error("身份证背面认证失败，请检查有效期限后请重新上传")
						}
					},
					fail: err => {
						console.log('请求失败！' + res.data.code)
					}
				})
			},
			confirmPickerB(data) {
				if (data.value[0] == "企业") {
					this.authenticationType = 2
				} else {
					this.authenticationType = 1
				}
				this.pickerData = data.value[0]
				this.showPickerB = false
			},
			getMyTime(time) {
				let dateTime = time.substring(0, 4) + "-" + time.substring(4, 6) + "-" + time.substring(6, 8);
				return dateTime;
			},
			submitMsg() {
				if (this.type != 1) {
					if (!this.authenticationType) {
						return this.$refs.uNotify.error("请选择认证类型!")
					}
					if (!this.businessLicenseUrl && this.authenticationType == 2) {
						return this.$refs.uNotify.error("请上传营业执照!")
					}
					if (!this.idBack && this.productId !== 361) {
						return this.$refs.uNotify.error("请上传身份证人面像!")
					}
					if (!this.idPositive && this.productId !== 361) {
						return this.$refs.uNotify.error("请上传身份证国徽面!")
					}
					if (this.authenticationType == 2) {
						if (this.iCardEndTime == '长期') {
							this.iCardEndTime = '2099' + this.iCardStartTime.substring(4, 10);
						}
						uni.navigateTo({
							url: '/pages-mine/franchise/enter/storeMsg?authenticationType=' + this
								.authenticationType +
								'&businessLicenseUrl=' + this.businessLicenseUrl + '&companyName=' + this
								.companyName +
								'&creditCode=' + this.creditCode + '&doBusinessTime=' + this.doBusinessTime +
								'&doBusinessAddress=' + this.doBusinessAddress + '&idPositive=' + this.idPositive +
								'&idBack=' + this.idBack + '&name=' + this.name + '&hometown=' + this.hometown +
								'&idCard=' + this.idCard + '&iCardStartTime=' + this.iCardStartTime +
								'&iCardEndTime=' + this.iCardEndTime + '&legalPerson=' + this.legalPerson +
								'&sex=' + this.sex + '&birthTime=' + this.birthTime + '&introducer=' + this
								.memberId +
								'&type=' + this.type
						})
					} else {
						this.partnerEnter()
					}
				} else {
					if (this.authenticationType == 1) {
						uni.navigateTo({
							url: '/pages-mine/franchise/enter/platformAudit'
						})
					} else {
						uni.navigateTo({
							url: '/pages-mine/franchise/enter/storeMsg?type=' + this.type
						})
					}
				}
			},
			partnerEnter() {
				if (this.iCardEndTime == '长期') {
					this.iCardEndTime = '2099' + this.iCardStartTime.substring(4, 10);
				}
				let type = 1
				if (this.type == 3) {
					type = 3
				}
				let param = {
					//主体信息
					authenticationType: this.authenticationType,
					introducer: this.introducer,
					hometown: this.hometown,
					iCardEndTime: this.iCardEndTime,
					iCardStartTime: this.iCardStartTime,
					idBack: this.idBack,
					idCard: this.idCard,
					sex: this.sex,
					birthTime: this.birthTime,
					idPositive: this.idPositive,
					nowAdd: this.nowAdd,
					selfIntroduction: this.selfIntroduction,
					career: this.career,
					income: this.income,
					name: this.name,
					legalPerson: this.legalPerson,
					//商户信息
					type: type,
					userId: uni.getStorageSync("memberId")
				}
				// 提交审核
				this.http({
					url: 'partnerEnter',
					method: 'POST',
					data: param,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("提交成功!")
							uni.navigateTo({
								url: '/pages-mine/franchise/enter/platformAudit'
							})
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			},
			toEnterStrategy() {
				uni.navigateTo({
					url: '/pages-mine/franchise/enter/enterStrategy'
				})
			},
			getFranchiseMsgById() {
				if (this.type == 1 || this.type == 3) {
					this.http({
						url: "getFranchiseMsgById",
						method: 'GET',
						path: uni.getStorageSync("memberId"),
						success: res => {
							console.log("res--------->", res);
							if (res.code == 0) {
								if (res.data.authenticationType == 2) {
									this.pickerData = "企业"
								} else {
									this.pickerData = "个人"
								}
								this.authenticationType = res.data.authenticationType
								this.examplePhotoImgUrl = res.data.businessLicenseUrl
								this.upImgUrl = res.data.businessLicenseUrl
								this.businessLicenseUrl = res.data.businessLicenseUrl
								this.imgFlag = 2
								this.companyName = res.data.companyName
								this.creditCode = res.data.creditCode
								this.doBusinessTime = res.data.doBusinessTime
								this.doBusinessAddress = res.data.doBusinessAddress
								this.birthTime = res.data.birthTime
								this.iCardEgC = res.data.idBack
								this.selfIntroduction = res.data.selfIntroduction
								this.idPositive = res.data.idPositive
								this.identityFront = res.data.idPositive
								this.idBack = res.data.idBack
								this.identityBack = res.data.idBack
								this.iCardEgA = res.data.idPositive
								this.name = res.data.name
								this.hometown = res.data.hometown
								this.nowAdd = res.data.nowAdd
								this.career = res.data.career
								this.income = res.data.income
								this.idCard = res.data.idCard
								this.iCardStartTime = res.data.iCardStartTime
								this.iCardEndTime = res.data.iCardEndTime
							} else {
								this.$refs.uNotify.error(res.msg)
							}
						},
						fail: err => {
							console.log('请求失败！' + res)
						}
					})
				}
			},
			// 分享到好友或朋友圈
			onShareAppMessage(res) {
				return {
					title: '小羽佳-家姐联盟',
					path: '/pages-mine/franchise/enter/topUpMoney?id=' + uni.getStorageSync("memberId"),
					mpId: 'wx8342ef8b403dec4e'
				}
			},
			onShareTimeline(res) {
				return {
					title: '小羽佳-家姐联盟',
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			}
		},
		// 页面加载后
		mounted() {
			this.getFranchiseMsgById()
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/franchise.scss";

	page {
		height: auto;
		background-color: #ffffff;
		font-size: 32rpx;
	}

	.img {
		width: 140rpx;
		height: 140rpx;
		display: block;
		margin: 0 auto;
		padding-top: 30rpx;
	}

	.imgB {
		width: 70rpx;
		height: 70rpx;
		display: block;
		margin: 0 auto;
		padding-top: 30rpx;
		margin-left: 46rpx;
		padding-bottom: 1%;
	}

	.imgC {
		width: 70rpx;
		height: 70rpx;
		display: block;
		margin: 0 auto;
		padding-top: 30rpx;
		margin-top: -14%;
		margin-left: 202rpx;
	}

	.imgD {
		width: 700rpx;
		height: 500rpx;
		padding-top: 20rpx;
		padding-bottom: 20rpx;
	}

	.imgA {
		width: 450rpx;
		height: 300rpx;
		margin-top: -30%;
		padding-bottom: 20rpx;
	}

	.imgAA {
		width: 680rpx;
		height: 300rpx;
		padding-bottom: 20rpx;
	}

	.iconClass {
		margin-left: 65%;
		margin-top: 17%;
		height: 150rpx;
	}

	.steps {
		margin-top: 3%;
		padding-bottom: 20%;
	}

	.text-state {
		text-align: center;
		font-size: 27rpx;
		padding-bottom: 24rpx;
		margin-top: 2%;
	}

	.shadow {
		height: auto;
		width: 100%;
		margin-bottom: 30rpx;
		// 添加栏目底部阴影
		box-shadow: 0 4rpx 20rpx #dedede;
	}

	.companyMsgC {
		color: dimgrey;
		margin-left: 30rpx;

	}

	.text-popup {
		color: dimgrey;
		margin-left: 30rpx;
		line-height: 60rpx;
	}

	.authentication-type {
		color: dimgrey;
		margin-left: 30rpx;
		line-height: 70rpx;
	}

	.input-state {
		margin-left: 30rpx;
		margin-top: -2%;
	}

	.input-stateB {
		margin-left: 30rpx;
		margin-top: -2%;
		padding-bottom: 40rpx;
	}

	.text-stateB {
		margin-left: 30rpx;
		color: darkgrey;
		margin-top: -2%;
	}

	.text-stateD {
		color: darkgrey;
		margin-left: 25%;
		line-height: 100rpx;
	}

	.text-stateE {
		color: darkgrey;
		margin-left: 30rpx;
		line-height: 100rpx;
	}

	.upload-img {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;

	}

	.ulink {
		padding-bottom: 5%;
	}

	.text-stateC {
		margin-left: 30rpx;
		color: darkgrey;
		margin-top: -6%;
		padding-bottom: 3%;
	}

	// 小箭头
	.text-arrow {
		display: block;
		float: right;
		height: 100rpx;
		margin-top: -8%;
		font-size: 36rpx;
		padding-right: 40rpx;
		color: #88888C;
	}

	.upload {
		margin-left: 4%;
		width: 200rpx;
		height: 200rpx;

	}

	.uploadA {
		margin-left: 4%;
		width: 200rpx;
		height: 200rpx;
		padding-bottom: 15%;
	}

	.upload-imgB {
		width: 320rpx;
		height: 250rpx;
		border-radius: 20rpx;
	}

	.uploadB {
		margin-left: 53%;
		width: 200rpx;
		height: 200rpx;
		margin-top: -26%;
	}

	.link-state {
		margin-top: 10%;
	}

	.text-stateF {
		margin-left: 4%;
		color: dimgrey;
	}

	.text-stateG {
		margin-left: 3%;
		color: dimgrey;
	}

	.button-state {
		width: 400rpx;
		height: 100rpx;
		margin-top: -14%;
		margin-left: 43%;
	}

	.textA {
		text-align: center;
		display: block;
		margin-top: 3%;
	}

	.icon-state {
		margin-top: -5%;
		margin-left: 3%;
	}

	.text-up {
		color: red;
	}
</style>