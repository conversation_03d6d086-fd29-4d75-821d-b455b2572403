import {requestUtil,reqPost} from "../util/requestUtil.js"

/**
 * 修改用户信息
 * @param {Object} postParam
 */
export const modifyUserInfo = function(postParam) {
	requestUtil({
		url: 'b_modify_user_info',
		method: 'POST',
		header: { 'content-type': 'application/x-www-form-urlencoded' },
		data: postParam.data,
		success: postParam.onSuccess,
		fail: postParam.onFail,
		complete: postParam.onComplate
	})
}