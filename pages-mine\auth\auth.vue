<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 头部个人信息部分 -->
		<view class="head-img"><img :src="headImg !== '' ? headImg : blankImg" /></view>
		<view class="title">
			<view style="font-size: 40rpx;">
				<text v-if="!isAuthBegin">检测认证资格</text>
				<text v-if="isAuthBegin&&!isAuthPass&&!isAuthNotPass">请耐心等待后台审核结果！</text>
				<text v-if="isAuthPass">恭喜您，认证通过！</text>
				<text v-if="isAuthNotPass">认证不通过，再接再厉吧！</text>
			</view>
			<view style="font-size: 36rpx;font-weight: bold;">
				<text>{{memberName}}</text>
			</view>
			<view style="font-size: 36rpx;color: #909399;">
				<text>员工ID：{{baomuId}}</text>
			</view>
		</view>

		<!-- 认证项目 -->
		<view class="certificate" v-if="!isAuthBegin">
			<view class="certificate-head">
				<view class="head-title">
					<text>认证资格检测</text>
				</view>
				<view class="title-tips">
					<text :style="loadTime<loadLimit?'color:#F9AE3D;':'color:#000000;'">
						{{loadTime<6?"检测中":checkAuthResult()}}</text>
					<uni-icons v-show="loadTime>=loadLimit&&isAccess" type="checkbox-filled" size="18" color="#F9AE3D">
					</uni-icons>
					<uni-icons v-show="loadTime>=loadLimit&&!isAccess" type="clear" size="18" color="#ff4d4b">
					</uni-icons>
				</view>
			</view>

			<!-- 认证项目列表 -->
			<view class="certificate-item" v-for="(item,index) in authState" :key="index">
				<view class="item-title">
					<text>{{item.title}}</text>
				</view>
				<view class="item-tips">
					<text v-show="item.show&&index==0">{{item.count}}/80分&nbsp;</text>
					<text v-show="item.show&&index==1">{{item.count}}天&nbsp;</text>
					<text v-show="item.show&&index==2">{{item.count}}/60分钟&nbsp;</text>
					<text v-show="item.show&&index==3">{{item.count}}/1&nbsp;</text>
					<text v-show="!item.show" style="color:#F9AE3D;">检测中&nbsp;</text>
					<uni-icons v-show="item.show&&item.isAccess" type="checkbox-filled" size="18" color="#F9AE3D">
					</uni-icons>
					<uni-icons v-show="item.show&&!item.isAccess" type="clear" size="18" color="#ff4d4b"></uni-icons>
				</view>
			</view>

			<view class="btn-big">
				<button @click="tryAuth()"
					:style="isAccess?'background-color:#1e1848;':'background-color:#aaa;color:#fff'">开始认证</button>
			</view>
		</view>



		<view class="btn-big">
			<button @click="openAuthReport()" v-if="isAuthPass">查看认证等级权益</button>
		</view>

		<view class="btn-big">
			<button @click="restartAuth()" v-if="isAuthNotPass">重新去认证</button>
		</view>



	</view>
</template>

<script>
	export default {
		data() {
			return {
				memberId: null,
				baomuId: null,
				memberName: null,
				baomuDetail: [],
				employee: [],
				headImg: null,
				loadTime: 0,
				// 检测动画时间
				loadLimit: 6,
				// 认证资格状态
				isAccess: false,
				// 认证开始状态
				isAuthBegin: false,
				// 认证通过和不通过
				isAuthPass: false,
				isAuthNotPass: false,
				status: 'more',
				// 认证资格状态（0：简历分数 1：合同总时长 2：课程学习时长 3.比赛证书）
				authState: [{
					title: "简历分数",
					isAccess: true,
					count: 80,
					show: false
				}, {
					title: "合同总时长",
					isAccess: false,
					count: 125,
					show: false
				}, {
					title: "课程学习时长",
					isAccess: true,
					count: 70,
					show: false
				}, {
					title: "比赛证书",
					isAccess: false,
					count: 0,
					show: false
				}],
				baomuAuth: {
					baomuId: this.baomuId,
					authEmployeeId: null,
					authName: "技能认证",
					authContent: "认证该员工是否具备对应的职业资格，是后续是否可上架的判断依据之一",
					authType: 0
				}

			}
		},
		methods: {
			// 返回检测结构
			checkAuthResult() {
				if (this.isAccess) {
					return '通过'
				} else {
					return '不通过'
				}
			},
			// 查看认证报告
			openAuthReport() {
				uni.navigateTo({
					url: "/pages-mine/auth/auth-report?baomuId=" + this.baomuId
				})
			},
			// 尝试开始认证
			tryAuth() {
				if (this.isAccess) {
					this.addBaomuAuth()
				} else {
					this.$refs.uNotify.error("还不满足认证资格哦，继续努力吧！")
				}
			},
			// 检查认证记录结果
			checkAuthState() {
				if (this.authState[0].isAccess) {
					// 测试时加上（临时更改，将认证条件简化）
					this.isAccess = true
					if (this.authState[1].isAccess || this.authState[2].isAccess ||
						this.authState[3].isAccess) {
						this.isAccess = true
					}
				}
			},
			// 获取认证资格
			checkAuthQualification() {
				this.http({
					url: 'checkAuthQualification',
					path: this.baomuId,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.authState = res.data
							for (let item of this.authState) {
								this.$set(item, 'show', false)
							}
						}
					},
				});
			},
			// 重新去认证
			restartAuth() {
				this.$refs.uNotify.error("抱歉，7天之后才可以重新认证！")
			},
			// 添加保姆技能认证-待认证信息
			addBaomuAuth() {
				this.$set(this.baomuAuth, 'baomuId', this.baomuId)
				this.http({
					url: 'addBaomuAuth',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: this.baomuAuth,
					method: 'POST',
					success: res => {
						if (res.code == 0) {
							this.isAuthBegin = true
							this.$refs.uNotify.success("认证成功！请耐心等待后台审核！")
						} else {
							this.$refs.uNotify.error("认证失败！" + res.msg)
						}
					},
				});
			},
			// 获取保姆详细信息成功
			getBaomuDetail() {
				if (this.baomuId !== null) {
					this.http({
						url: 'getBaomuDetail',
						method: 'GET',
						hideLoading: true,
						path: this.baomuId,
						success: res => {
							if (res.code == 0) {
								let baomuDetail = res.data
								this.baomuDetail = baomuDetail
								this.employee = this.baomuDetail.employee
								// 获取员工信息
								this.memberName = this.employee.realName
								this.headImg = this.employee.headPortrait
								console.log('获取保姆详细信息成功-请求成功！')
							} else {
								console.log('获取保姆详细信息成功-请求失败！')
							}
						}
					});
				}
			},
			// 获取技能认证信息（判断是否已经进入等待认证流程）
			getBaomuAuth() {
				this.http({
					url: 'getBaomuAuthByBaomuId',
					path: this.baomuId,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							for (let item of res.data) {
								if (item.authType == 0) {
									this.isAuthBegin = true
									if (item.state == 1) {
										this.isAuthNotPass = true
									} else if (item.state == 2) {
										this.isAuthPass = true
									}
									return
								}

							}

						}
					},
				});
			},
			// 获取用户信息
			getMemberInfor() {
				let baomuId = uni.getStorageSync('baomuId')
				baomuId = baomuId == '' ? null : baomuId
				// 如果在上一个页面没有获取参数，则从缓存中取值（帮别人填的情况）
				if (this.baomuId == undefined || this.baomuId == null) {
					this.baomuId = baomuId
				}
				this.getBaomuDetail()
				this.getBaomuAuth()
				this.checkAuthQualification()
			},
			orginInfo() {
				this.getMemberInfor()
			}
		},
		watch: {
			loadTime: {
				handler(newValue, oldVal) {
					let count = [0, 1, 2, 3]
					for (let i in count) {
						if (this.loadTime - 2 == i) {
							this.authState[i].show = true
						}
					}
					if (this.loadTime >= this.loadLimit) {
						this.checkAuthState()
					}
				},
				deep: true
			},
		},
		onLoad(options) {
			this.baomuId = options.baomuId
			// 测试时加上
			// this.baomuId = 37492
		},
		mounted() {
			this.orginInfo()
			let time = setInterval(() => {
				this.loadTime += 1;
			}, 1000);
		}
	}
</script>

<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
	}

	.head-img {
		width: 100%;
		height: auto;

		img {
			margin: 200rpx auto 60rpx auto;
			display: block;
			width: 200rpx;
			height: 200rpx;
			border-radius: 50%;
		}
	}

	.title {
		width: 100%;
		height: auto;
		margin: 0 auto;
		text-align: center;

		text {
			line-height: 60rpx;
		}
	}

	// 大号按钮
	.btn-big {
		padding-bottom: 60rpx;

		button {
			margin: 80rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	.certificate {
		width: 80%;
		height: auto;
		font-size: 36rpx;
		margin: 40rpx auto;
	}

	.certificate-head {
		width: 100%;
		line-height: 100rpx;
		display: flex;
		flex-direction: row;
	}

	.head-title,
	.item-title {
		width: 50%;
		text-align: left;

		text {}
	}

	.title-tips,
	.item-tips {
		width: 50%;
		display: block;
		text-align: right;

		text {}
	}

	.certificate-item {
		width: 100%;
		line-height: 60rpx;
		color: #909399;
		display: flex;
		flex-direction: row;
	}

	.item-load {
		width: 30%;
	}
</style>