<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 须知提示弹窗 -->
		<u-popup :show="popupShow" mode="bottom" @close="popupShow = false">
			<view class="filter-title" @click="popupShow = false">
				<text style="font-weight: bold;">考试须知</text>
			</view>
			<view class="filter-content" style="height: 500rpx;">
				<view class="filter-content-text">
					<text v-for="(item,index) in attentionList" :key="index">{{item}}</text>
				</view>
			</view>
		</u-popup>

		<!-- 入口分享图片 -->
		<view class="img-view" v-if="showShareImg">
			<img class="w10 mg-at" :src="shareImg" alt="" mode="widthFix" />
		</view>

		<view v-if="!showShareImg">
			<view class="exam-tab">
				<view class="tab flac-col">
					<view class="tab-head">
						<text>{{(exam.examTitle)}}</text>
					</view>
					<view class="tab-head-smail flac-row">
						<uni-icons type="notification" size="24" style="margin-right: 20rpx;">
						</uni-icons>
						<text>考试限时：{{exam.examDuration||''}}分钟</text>
					</view>

					<view class="tab-head-smail flac-row">
						<uni-icons type="calendar" size="24" style="margin-right: 20rpx;">
						</uni-icons>
						<text>合格：{{exam.examPassScore||''}}分</text>
					</view>

					<view class="tab-head-smail flac-row">
						<uni-icons type="medal" size="24" style="margin-right: 20rpx;">
						</uni-icons>
						<text>满分：{{exam.examScore||''}}分</text>
					</view>
				</view>
			</view>

			<!-- 考试说明 -->
			<view class="exam-tab">
				<view class="tab flac-col">
					<view class="tab-head">
						<text>考试说明</text>
					</view>
					<view class="tab-head-smail">
						<text style="font-size: 32rpx;line-height: 50rpx;">{{exam.examRemark||'-'}}</text>
					</view>

					<view class="tab-head-smail" style="text-align: right;color: #ff4d4b;margin-top: 20rpx;"
						@click="popupShow = true">
						<text class="fb" style="font-size: 32rpx;line-height: 50rpx;">*阅读考试须知</text>
					</view>
					<u-gap height="10"></u-gap>
				</view>
			</view>

			<u-gap height="200"></u-gap>

			<view class="btn-bottom-fixed" @click="openExam()" v-if="examTimeState==1">
				<button v-if="!examRecordList">报名考试</button>
				<button v-else-if="!recordEnded">参加考试</button>
				<button v-else>查看结果</button>
			</view>
			<view class="btn-bottom-fixed" v-else>
				<button v-if="examTimeState==0"
					style="background-color:#909399">考试未开始{{leaveTime?'-'+formatLeaveTime(leaveTime):''}}</button>
				<button v-else-if="examTimeState==2" style="background-color:#909399">考试已结束</button>
				<button v-else style="background-color:#909399">暂不开放考试</button>
			</view>
		</view>

	</view>
</template>

<script>
	import {
		data
	} from '../../uni_modules/uview-ui/libs/mixin/mixin'
	export default {
		data() {
			return {
				// 可设置
				// 显示考试入口分享图
				showShareImg: false,
				// 是否校验员工
				checkEmployee: true,
				// 检查考试时间
				checkExamTime: true,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				popupShow: false,

				service: "https://biapi.xiaoyujia.com/",
				attentionList: [
					"1.开始考试后，不得中途退出，否则将视为放弃，并重新答题！",
					"2.考试时间到，不得继续答题，否则将自动交卷，请注意把握时间！",
					"3.诚信考试，多次切屏将视为违规，并自动交卷结束考试！"
				],
				headImg: '',
				shareImg: '',
				examEntryImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/QR_img/QR_examEntry.png',
				blankImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png',

				recordId: null,
				memberId: uni.getStorageSync("memberId") || 0,
				employeeId: uni.getStorageSync("employeeId") || null,
				examId: -1,
				exam: {},
				examRecordList: null,
				recordEnded: false,
				examTimeState: -1,
				time: 0,
				leaveTime: null,
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 打开考试
			openExam() {
				if (this.recordEnded) {
					uni.navigateTo({
						url: "/pages-mine/exam/exam-result?id=" + this.recordId
					})
					return
				}

				if (!this.employeeId && this.checkEmployee) {
					this.openCheck(1, "登录账号非员工账号，请检查",
						'可能导致成绩无法计入绩效！确定参加考试吗？（该手机号可能与入职时的不一致，可先进行考试，后务必在我的-设置-会员信息中修改真实姓名！未设置造成绩效录入失败者，后果自负！）')
					return
				}

				if (!this.recordId) {
					this.http({
						outsideUrl: 'https://api.xiaoyujia.com/exam/addExamRecordAndNoticeByExamId',
						method: 'POST',
						data: {
							memberId: this.memberId || null,
							employeeId: this.employeeId || null,
							examId: this.examId
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							this.$refs.uNotify.success("报名成功！点击参加考试即可开始答题！")
							this.listUnionExamRecord()
						}
					});
				} else {
					uni.navigateTo({
						url: "/pages-mine/exam/exam?id=" + this.recordId
					})
				}
			},
			// 获取考试列表
			listUnionExamRecord() {
				this.http({
					url: 'listUnionExamRecord',
					method: 'POST',
					data: {
						memberId: this.memberId || 0,
						examId: this.examId,
						orderBy: 'u.recordCreTime DESC'
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.examRecordList = res.data
							this.recordId = this.examRecordList[0].id
							for (let i = 0; i < this.examRecordList.length; i++) {
								if (this.examRecordList[i].recordState < 2) {
									this.recordId = this.examRecordList[i].id
									break
								} else if (this.examRecordList[i].recordState == 2 || this.examRecordList[i]
									.recordState == 4) {
									this.recordEnded = true
									this.recordId = this.examRecordList[i].id
									break
								}
							}
						} else {
							this.examRecordList = null
						}
					}
				});
			},
			// 获取考试详情
			selectUnionExamById(value) {
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/unionExam/selectUnionExamById',
					method: 'GET',
					path: this.examId,
					hideLoading: true,
					success: res => {
						if (res.status == 200) {
							this.exam = res.data
							if (value == 0) {
								this.getShareImg()
							}
							this.checkTime()
						}
					}
				})
				if (value == 0) {
					this.listUnionExamRecord()
				}
			},
			// 获取分享图片
			getShareImg() {
				if (!this.showShareImg) {
					return
				}

				let scene = "id/" + this.examId
				let param = {
					textList: [{
						"text": this.exam.examTitle,
						"fontSize": 50,
						"isCenter": true,
						"color": "0xfed472",
						"isBold": true,
						"x": 0,
						"y": 1020
					}],
					qrCodeStyle: {
						"width": 350,
						"height": 350,
						"x": 200,
						"y": 250
					},
					maxWidth: 600,
					img: this.examEntryImg,
					path: 'pages-mine/exam/exam-entry',
					scene: scene,
					source: "xyjacn",
					type: 1
				}
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/image/getWxShareImg',
					method: 'POST',
					data: param,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.status == 200) {
							this.shareImg = res.data
						}
					},
					fail: err => {
						this.$refs.uNotify.erro("获取分享图片-请求失败！" + err)
					}
				})
			},
			formatLeaveTime(time) {
				let h = parseInt(time / (60 * 60))
				let m = parseInt((time - (h * 60 * 60)) / 60)
				let s = parseInt((time - (h * 60 * 60) - (m * 60)))
				h = h < 10 ? '0' + h : h
				m = m < 10 ? '0' + m : m
				s = s < 10 ? '0' + s : s
				let leaveTime = h + ':' + m + ':' + s
				return leaveTime
			},
			formatDateValue(value) {
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				return value
			},
			// 检查考试开始时间
			checkTime() {
				let timeStarted = new Date(this.formatDateValue(this.exam.examStarted)).getTime()
				let timeEnded = new Date(this.formatDateValue(this.exam.examEnded)).getTime()
				let nowDate = new Date().getTime()
				if (timeStarted <= nowDate && nowDate <= timeEnded) {
					this.examTimeState = 1
				} else if (nowDate < timeStarted) {
					this.examTimeState = 0
					let time = parseInt((timeStarted - nowDate) / 1000)
					// 开启倒计时
					if (time < 259200) {
						this.leaveTime = time
					}
				} else if (nowDate > timeEnded) {
					this.examTimeState = 2
				}
			},
			// 登录状态检查
			checkLogin() {
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.error('您还未进行登录哦，先去登录吧！')
					let url = '/pages-mine/exam/exam-entry?id=' + this.examId
					uni.setStorageSync('redirectUrl', url)
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						});
					}, 2000);
					return false
				} else {
					this.selectUnionExamById(0)
					return true
				}
			},
			// 分享
			onShareAppMessage(res) {
				return {
					title: '家姐联盟-考试中心（' + this.exam.examTitle + '）',
					path: '/pages-mine/exam/exam-entry?id=' + this.examId,
					mpId: 'wx8342ef8b403dec4e'
				};
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					uni.navigateTo({
						url: "/pages-mine/exam/exam?id=" + this.recordId
					})
				} else if (this.checkType == 1) {
					this.checkEmployee = false
					this.openExam()
				}
			},
		},
		watch: {
			time: {
				handler(newValue, oldVal) {
					if (this.leaveTime && this.leaveTime >= 0) {
						this.leaveTime--
					}
				},
				deep: true
			},
			leaveTime: {
				handler(newValue, oldVal) {
					if (this.leaveTime == 0) {
						this.examTimeState = 1
					}
				},
				deep: true
			}
		},
		onShow() {
			this.selectUnionExamById(1)
		},
		mounted() {
			this.checkLogin()
			let time = setInterval(() => {
				this.time++
			}, 1000)
		},
		onLoad(options) {
			if (options.scene) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/')
					obj[arr[0]] = arr[1]
				}
				this.examId = obj.id || -1
			}
			this.examId = options.id ? parseInt(options.id) : this.examId
			this.showShareImg = options.showShareImg ? true : false
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/exam.scss";
</style>