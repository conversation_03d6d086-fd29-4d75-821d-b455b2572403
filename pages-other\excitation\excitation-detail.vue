<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 评论 -->
		<u-popup :show="showPopup" @close="showPopup = false" round="10">
			<view class="f18 fb text-c lh60">我的评论</view>
			<scroll-view scroll-y="true" class="scroll-Y">
				<view class="tab-inputbox-high" style="margin: 10rpx 0rpx 30rpx 30rpx;">
					<u--textarea class="multiline-input" confirmType="done" maxlength="200"
						v-model="excellentEmployeeComment.commentContent" placeholder="请填写您对该员工的评论（参与评论与打赏，一起进步吧！）"
						height="100" count></u--textarea>
				</view>
			</scroll-view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="showPopup = false">
						<text>取消</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="updateExcellentEmployeeComment()">
					<view class="filter-button-right">
						<text>提交</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 打赏 -->
		<u-popup :show="showPopup1" @close="showPopup1 = false" round="10">
			<view class="f18 fb text-c lh60">积分打赏</view>
			<scroll-view scroll-y="true" class="scroll-Y">
				<view class="flac-row-b f16">
					<view>当前余额：<text :style="rewardAuth?'text-decoration: line-through;':''">{{jiaBiAmount}}积分</text>
					</view>
					<uni-number-box v-model="rewardAmount" :min="0" :max="rewardAuth?rewardLimit:jiaBiAmount" />
				</view>
				<view class="f16 lh40" style="color: #ff4d4b;">
					{{rewardAuth?'tips：管理员无需消耗余额，上限'+rewardLimit+'积分':'参与打赏，共同进步吧！'}}
				</view>
			</scroll-view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="showPopup1 = false">
						<text>取消</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="rewardExcellentEmployee()">
					<view class="filter-button-right">
						<text>提交</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 标杆员工 -->
		<uni-transition mode-class="fade" :show="excellentEmployee!=null">
			<view class="main-bar f16" v-if="excellentEmployee">
				<view class="flac-col" style="padding: 0 20rpx;">
					<view class="flac-row">
						<view class="w2" @click="openImgPreview(formatHeadImg(excellentEmployee))">
							<u-avatar :src="formatHeadImg(excellentEmployee)" size="50" />
						</view>
						<view class="flac-col w8" style="padding: 10rpx 0;">
							<view class="main-text f18 w10" style="display: block; white-space: nowrap;">
								{{excellentEmployee.name|| '匿名用户'}}{{excellentEmployee.depart?'('+excellentEmployee.depart+')':''}}
							</view>
							<view style="display: block;">{{excellentEmployee.createDate}}</view>
						</view>
					</view>
					<view style="margin: 15rpx 0rpx;" v-if="formatCreatorName(excellentEmployee)">
						获得<text class="main-text"
							style="margin: 0 10rpx;">{{formatCreatorName(excellentEmployee)}}</text>的赞赏
					</view>
					<view style="margin: 10rpx 0rpx;">
						<view v-if="excellentEmployee.introduce">工作介绍：{{excellentEmployee.introduce}}</view>
						<view v-if="excellentEmployee.workDeeds">工作事迹：{{excellentEmployee.workDeeds}}</view>
					</view>
					<view v-if="excellentEmployee.dailyReviewId"
						style="color: #00aaff;text-decoration:underline;margin: 20rpx 0;">
						<text
							@click="goPage('/pages-other/excitation/review-detail?id='+excellentEmployee.dailyReviewId)">每日复盘详情
						</text>
					</view>
					<view class="w10" style="margin: 10rpx auto;">
						<u-album :maxCount="maxShowPhoto" rowCount="3" multipleSize="90" space="5.5"
							:urls="excellentEmployee.excellentEmployeeImgList" />
					</view>
				</view>
				<view class="flac-row-b" style="margin-top: 20rpx;padding: 20rpx 20rpx;background-color: #f4f4f5;"
					v-if="excellentEmployee.rewardTypeImg">
					<view class="flac-row w5">
						<view>
							<img :src="excellentEmployee.rewardTypeImg" alt="" style="width: 150rpx;height: 150rpx;" />
						</view>
						<view style="margin-left: 20rpx;">
							<text>{{excellentEmployee.rewardTypeName}}</text>
						</view>
					</view>
					<view style="margin-right: 20rpx;">
						{{excellentEmployee.rewardAmount}}
					</view>
				</view>
			</view>

			<!-- 操作栏 -->
			<view class="flac-row-b bar f16" v-if="excellentEmployee">
				<view class="flac-row" @click="like()">
					<uni-icons :type="excellentEmployee.liked==1?'hand-up-filled':'hand-up'" size="20"
						:color="excellentEmployee.liked==1?'#ff4d4b':'#dedede'"></uni-icons>
					<text style="margin-left: 10rpx;">{{likeList.length||'0'}}</text>
				</view>
				<view class="flac-row" @click="showPopup=true">
					<uni-icons type="chat" size="20"></uni-icons>
					<text style="margin-left: 10rpx;">评论</text>
				</view>
				<view class="flac-row" @click="showPopup1=true">
					<uni-icons type="medal" size="20"></uni-icons>
					<text style="margin-left: 10rpx;">打赏</text>
				</view>
				<view class="flac-row" style="margin-left: -30rpx;">
					<button plain="true" open-type="share" style="border: none;"><uni-icons type="redo"
							size="20"></uni-icons></button>
					<text style="margin-left: -10rpx;">分享</text>
				</view>
			</view>
		</uni-transition>

		<!-- 栏目 -->
		<view style="margin: 0rpx 50rpx">
			<u-tabs :list="menuList" @click="choiceMenu" :current="choiceIndex" lineWidth="40" lineHeight="4"
				lineColor="#1e1848" :activeStyle="{
					color: '#1e1848',
				  fontSize: '16px',
					fontWeight: 'bold',
				}" :inactiveStyle="{
					color: '#888',
					fontSize: '16px',
				  fontWeight: 'bold',
				}" itemStyle="height:100rpx">
			</u-tabs>
		</view>

		<!-- 点赞 -->
		<uni-transition mode-class="slide-left" :show="choiceIndex == 0">
			<view v-if="choiceIndex == 0">
				<view class="mg-at f16 lh25" style="margin: 20rpx 5%;">
					<view class="small-tab" v-for="(item,index) in likeList" :key="index">
						<view class="flac-row">
							<view class="w2">
								<u-avatar :src="formatHeadImg(item)" size="50" />
							</view>
							<view class="flac-col w7" style="padding: 10rpx 0;">
								<text style="display: block;">{{formatName(item)}}</text>
								<text style="display: block;">{{formatDate(item.createTime)}}</text>
							</view>
							<view class="w1">
								<uni-icons type="heart-filled" size="24" color="#ff4d4b"></uni-icons>
							</view>
						</view>
					</view>

					<u-empty v-if="likeList.length==0" text="暂无点赞" icon="http://cdn.uviewui.com/uview/empty/data.png" />
				</view>
			</view>
		</uni-transition>

		<!-- 评论 -->
		<uni-transition mode-class="slide-right" :show="choiceIndex == 1">
			<view v-if="choiceIndex == 1">
				<view class="mg-at f16 lh25" style="margin: 40rpx 5%;">
					<view class="comment-tab" v-for="(item,index) in commentList" :key="index"
						@longpress="deleteComment(index)">
						<view class="flac-row" style="padding: 0 20rpx;">
							<view class="w2">
								<u-avatar :src="formatHeadImg(item)" size="50" />
							</view>
							<view class="flac-col w4" style="padding: 10rpx 0;">
								<text style="display: block;">{{formatName(item)}}</text>
								<text style="display: block;">{{formatDate(item.createTime)}}</text>
							</view>
						</view>
						<view style="min-height: 100rpx;padding: 0 20rpx;">
							<text>{{item.commentContent}}</text>
						</view>
					</view>

					<u-empty v-if="commentList.length==0" text="暂无评论"
						icon="http://cdn.uviewui.com/uview/empty/data.png" />
					<view class="list-bottom" v-if="commentList.length!=0">
						<text>已显示全部内容</text>
					</view>
				</view>
			</view>
		</uni-transition>

		<!-- 打赏 -->
		<uni-transition mode-class="slide-right" :show="choiceIndex == 2">
			<view v-if="choiceIndex == 2">
				<view class="mg-at f16 lh25" style="margin: 40rpx 5%;">
					<view class="comment-tab" v-for="(item,index) in rewardList" :key="index">
						<view class="flac-row" style="padding: 0 20rpx;">
							<view class="w2">
								<u-avatar :src="formatHeadImg(item)" size="50" />
							</view>
							<view class="flac-col w65" style="padding: 10rpx 0;">
								<text style="display: block;">{{formatName(item)}}</text>
								<text style="display: block;">{{formatDate(item.createTime)}}</text>
							</view>
							<view class="w15" style="text-align: left;">
								<uni-icons type="medal-filled" size="24" color="#ff4d4b"></uni-icons>
								<text>{{item.rewardAmount||''}}</text>
							</view>
						</view>
					</view>

					<u-empty v-if="rewardList.length==0" text="暂无打赏"
						icon="http://cdn.uviewui.com/uview/empty/data.png" />
				</view>
			</view>
		</uni-transition>

		<!-- 悬浮按钮 -->
		<view style="position: fixed;right: 36rpx;bottom: 25%;" v-if="isMine">
			<img style="width: 80rpx;height: 80rpx;"
				src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/icon_edit.png"
				@click="goEdit" />
		</view>
		<view style="position: fixed;right: 36rpx;bottom: 19%;" v-if="shareType==1">
			<img style="width: 80rpx;height: 80rpx;"
				src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/icon_home.png"
				@click="goPage('./index')" />
		</view>

		<u-gap height="400"></u-gap>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 打赏权限
				rewardAuth: false,
				// 最大显示图片数
				maxShowPhoto: 3,
				// 打赏上限
				rewardLimit: 10000,
				// 打赏管理员会员id
				rewardMemberId: 298434,
				// 页面类型（0：普通 1：可返回主页）
				shareType: 0,
				// 是否自己打赏的
				isMine: false,

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				rewardAmount: 0,
				jiaBiAmount: 0,
				choiceIndex: 0,
				blankHeadImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				employeeNo: uni.getStorageSync("employeeNo") || null,
				excellentEmployeeId: 0,
				excellentEmployee: null,
				menuList: [{
					name: '点赞'
				}, {
					name: '评论'
				}, {
					name: '打赏'
				}],

				likeList: [],
				commentList: [],
				rewardList: [],
				excellentEmployeeComment: {
					excellentEmployeeId: 0,
					memberId: uni.getStorageSync("memberId") || 0,
					employeeId: uni.getStorageSync("employeeId") || null,
					commentContent: '',
					commentScore: 0,
				},
				excellentList: [],
				showPopup: false,
				showPopup1: false,
				total: 0,
				pageCount: 0,
				searchCondition: {
					orderBy: "t.createTime ASC",
					commentState: 1,
					current: 1,
					size: 10
				},
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 选择菜单
			choiceMenu(e) {
				this.choiceIndex = e.index
			},
			// 跳转页面
			goPage(url) {
				uni.navigateTo({
					url: url
				})
			},
			goEdit() {
				let url = '/pages-other/excitation/excitation?id=' + this.excellentEmployeeId
				uni.navigateTo({
					url: url
				})
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 格式化时间
			formatDate(value) {
				if (value == null) {
					return "-"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '-' + MM + '-' + d
			},
			formatName(val) {
				return val.name || val.employeeName || val.memberName || '匿名用户'
			},
			formatCreatorName(val) {
				if (val.selfReference && val.selfReference == val.name) {
					return '自己'
				}
				let result = val.reference || val.selfReference || val.creatorName || ''
				let depart = val.creatorDepart ? '(' + val.creatorDepart + ')' : ''
				result += result ? depart : ''
				return result
			},
			formatHeadImg(val) {
				return val.employeeHeadImg || val.memberHeadImg || this.blankHeadImg
			},
			checkLogin() {
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.warning("您还未进行登录哦，请先登录吧！")
					uni.setStorageSync('redirectUrl', '/pages-other/excitation/excitation-detail?id=' + this
						.excellentEmployeeId)
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						})
					}, 2000)
					return false
				} else {
					return true
				}
			},
			// 删除评论
			deleteComment(index) {
				let memberId = this.commentList[index].memberId || 0
				let id = this.commentList[index].id
				if (memberId != this.memberId) {
					return
				}

				uni.showModal({
					title: '确定删除该评论吗？',
					content: '删除后不可恢复！',
					success: res => {
						if (res.confirm) {
							this.http({
								url: 'deleteExcellentEmployeeCommentById',
								method: 'GET',
								path: id,
								success: res => {
									if (res.code == 0) {
										this.$refs.uNotify.success('评论删除成功！')
									} else {
										this.$refs.uNotify.error(res.msg)
									}
								},
							})
						}
					}
				});
			},
			// 获取优秀员工信息
			getExcellentEmployee() {
				this.http({
					url: 'getExcellentEmployee',
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						id: this.excellentEmployeeId || 0,
						memberIdSearch: this.memberId || 0
					},
					success: res => {
						if (res.code == 0) {
							this.excellentEmployee = res.data
							if (this.excellentEmployee.creator == this.employeeNo) {
								this.isMine = true
							}
						}
					},
				})
			},
			// 获取点赞列表
			listExcellentEmployeeLike() {
				this.http({
					url: 'listExcellentEmployeeLike',
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						excellentEmployeeId: this.excellentEmployeeId,
						likeType: 0,
						likeState: 1,
						orderBy: 't.createTime ASC'
					},
					success: res => {
						if (res.code == 0) {
							this.likeList = res.data || []
						} else {
							this.likeList = []
						}
					},
				})
			},
			// 获取优秀员工评论列表
			pageExcellentEmployeeComment() {
				this.$set(this.searchCondition, "excellentEmployeeId", this.excellentEmployeeId)
				this.http({
					url: 'pageExcellentEmployeeComment',
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: this.searchCondition,
					success: res => {
						if (res.code == 0) {
							this.total = res.data.total
							this.pageCount = res.data.pages
							this.commentList = this.commentList.concat(res
								.data.records)
						} else {

						}
					},
				})
			},
			// 获取打赏列表
			listJiaBiReward() {
				this.http({
					url: 'listJiaBiReward',
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						excellentEmployeeId: this.excellentEmployeeId,
						orderBy: 't.createTime ASC'
					},
					success: res => {
						if (res.code == 0) {
							this.rewardList = res.data || []
						}
					},
				})
			},
			// 点赞
			like() {
				if (!this.checkLogin()) {
					return
				}
				let liked = this.excellentEmployee.liked
				this.http({
					url: 'updateExcellentEmployeeLike',
					method: 'POST',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						excellentEmployeeId: this.excellentEmployeeId || 0,
						memberId: this.memberId || 0,
						employeeId: this.employeeId || null,
					},
					success: res => {
						if (res.code == 0) {
							if (liked == 0) {
								this.$refs.uNotify.success("点赞成功！")
							}
							this.excellentEmployee.liked = liked == 1 ? 0 : 1
							this.listExcellentEmployeeLike()
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
				})
			},
			// 评论
			updateExcellentEmployeeComment() {
				if (!this.checkLogin()) {
					return
				}

				if (!this.excellentEmployeeComment.commentContent) {
					return this.$refs.uNotify.warning("请填写评论内容！")
				}
				this.$set(this.excellentEmployeeComment, "excellentEmployeeId", this.excellentEmployeeId)
				this.http({
					url: 'updateExcellentEmployeeComment',
					method: 'POST',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: this.excellentEmployeeComment,
					success: res => {
						if (res.code == 0) {
							this.showPopup = false
							this.$refs.uNotify.success("评论成功！")
							this.searchCondition.current = 1
							this.commentList = []
							this.excellentEmployeeComment.commentContent = ''
							this.pageExcellentEmployeeComment()
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
				})
			},
			// 打赏
			rewardExcellentEmployee() {
				if (!this.excellentEmployee || !this.checkLogin()) {
					return
				}
				if (this.rewardAmount == 0) {
					return this.$refs.uNotify.warning("请输入打赏积分数量！")
				}
				this.http({
					url: 'rewardExcellentEmployee',
					method: 'POST',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						memberId: this.rewardAuth ? this.rewardMemberId : this.memberId,
						employeeId: this.employeeId || null,
						excellentEmployeeId: this.excellentEmployeeId,
						rewardAmount: this.rewardAmount
					},
					success: res => {
						if (res.code == 0) {
							this.rewardAmount = 0
							this.$refs.uNotify.success("打赏成功！")
							this.getjiaBiAmount()
							this.showPopup1 = false
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
				})
			},
			// 获取佳币数量
			getjiaBiAmount() {
				this.http({
					url: 'getJiaBi',
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.memberId || 0
					},
					success: res => {
						if (res.code == 0) {
							this.jiaBiAmount = res.data.jiaBiAmount
						}
					},
				})
			},
			// 校验打赏权限
			checkRewardAuth() {
				this.http({
					url: 'checkRewardAuth',
					method: 'POST',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					hideLoading: true,
					data: {
						roleId: uni.getStorageSync('roleId') || 0,
						employeeId: uni.getStorageSync('employeeId') || null,
					},
					success: res => {
						if (res.code == 0) {
							this.rewardAuth = true
						}
					},
				})
			},
			// 分享到好友或朋友圈
			onShareAppMessage(res) {
				let img = this.excellentEmployee.rewardTypeImg
				let reward = this.excellentEmployee.rewardTypeName ? '【' + (this.excellentEmployee.rewardTypeName || '') +
					'】的' : ''
				let title = '恭喜您获得了' + reward + '赞赏，快来看看吧！'
				return {
					title: title,
					path: '/pages-other/excitation/excitation-detail?id=' + this.excellentEmployeeId,
					mpId: 'wx8342ef8b403dec4e',
					imageUrl: img
				}
			},
		},
		onReachBottom() {
			this.searchCondition.current++
			this.pageExcellentEmployeeComment()
		},
		onLoad(options) {
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				this.excellentEmployeeId = obj.t || this.excellentEmployeeId
			}
			this.excellentEmployeeId = options.id || this.excellentEmployeeId
			this.shareType = options.shareType || 0
			this.getExcellentEmployee()
			this.listExcellentEmployeeLike()
			this.pageExcellentEmployeeComment()
			this.listJiaBiReward()
			this.getjiaBiAmount()
			this.checkRewardAuth()
		},
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";
	@import "@/pages-mine/common/css/resume-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}

	.main-text {
		color: #1e1848;
		font-weight: bold;
	}

	.main-bar {
		width: 94%;
		height: auto;
		padding: 40rpx 3%;
		margin: 20rpx 0;
	}

	.bar {
		width: 86%;
		height: 80rpx;
		border: 1rpx #dedede solid;
		padding: 10rpx 7%;
		margin: 20rpx 0;
	}

	.small-tab {
		width: 100%;
		height: 140rpx;
		border-bottom: 2rpx #dedede solid;
		margin: 20rpx 0;
	}

	.comment-tab {
		width: 100%;
		height: auto;
		border-bottom: 2rpx #dedede solid;
		margin: 20rpx 0;
	}

	.list-bottom {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;

		text {
			display: block;
			text-align: center;
			font-weight: 100;
			color: #909399;
		}
	}

	.scroll-Y {
		display: block;
		width: 90%;
		height: 850rpx;
		margin: 0rpx 5%;
	}

	.active {
		border: 10rpx #19be6b solid;
		width: 220rpx;
		height: 300rpx;
		// margin: 10rpx 10rpx;
	}
</style>