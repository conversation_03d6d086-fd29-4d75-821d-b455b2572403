<template>
  <view class="payment-list-container">
    <!-- 头部搜索区域 -->
    <view class="search-header">
      <view class="search-box">
        <view class="search-input-wrapper">
          <input
            v-model="searchKeyword"
            placeholder="请填写订单号、合同编号"
            class="search-input"
            @input="handleSearch"
          />
          <view class="search-icon">🔍</view>
          <view v-if="searchKeyword" class="clear-icon" @click="clearSearch">✕</view>
        </view>
      </view>

      <!-- 状态筛选 -->
      <view class="status-filter">
        <view
          v-for="(status, index) in statusOptions"
          :key="index"
          class="status-item"
          :class="{ active: selectedStatus === status.value }"
          @click="handleStatusChange(status.value)"
        >
          <text>{{ status.label }}</text>
        </view>
      </view>
    </view>

    <!-- 卡片列表 -->
    <view class="card-list">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-wrapper">
        <text class="loading-text">加载中...</text>
      </view>

      <view
        v-for="item in filteredList"
        :key="item.id"
        class="payment-card"
      >
        <view class="card-header">
          <view class="contract-info">
            <view class="contract-row">
              <text class="label-text">合同编号</text>
              <text class="contract-number" @click="copyText(item.contractNumber, '合同编号')">{{ item.contractNumber }}</text>
            </view>
            <view class="status-tag" :class="item.status=='待确定'?'status-warning'
															:item.status=='已返还'?'status-info'
															:item.status=='已奖励'?'status-success':'status-info'">
              <text class="status-text">{{ item.status }}</text>
            </view>
			<text class="value amount" v-if="item.remark">说明：{{ item.remark }}</text>
          </view>
        </view>

        <view class="card-content">
          <view class="info-row">
            <view class="info-item">
              <view class="info-header">
                <text class="info-icon">📋</text>
                <text class="label">订单编号</text>
              </view>
              <text class="value clickable" @click="copyText(item.orderNumber, '订单编号')">{{ item.orderNumber }}</text>
            </view>
            <view class="info-item">
              <view class="info-header">
                <text class="info-icon">💰</text>
                <text class="label">订单金额</text>
              </view>
              <text class="value amount">¥{{ item.orderAmount }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item">
              <view class="info-header">
                <text class="info-icon">💸</text>
                <text class="label">扣款金额</text>
              </view>
              <text class="value deduction">¥{{ item.deductionAmount }}</text>
            </view>
            <view class="info-item">
              <view class="info-header">
                <text class="info-icon">👩‍💼</text>
                <text class="label">当前阿姨</text>
              </view>
              <text class="value">{{ item.currentAuntie }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item">
              <view class="info-header">
                <text class="info-icon">🏪</text>
                <text class="label">阿姨归属门店</text>
              </view>
              <text class="value">{{ item.auntieStore }}</text>
            </view>
            <view class="info-item">
              <view class="info-header">
                <text class="info-icon">📅</text>
                <text class="label">上户天数</text>
              </view>
              <text class="value days">{{ item.workingDays }}天</text>
            </view>
          </view>

          <!-- 奖励对象信息 - 仅在已奖励状态时显示 -->
          <view v-if="item.status === '已奖励'" class="info-row">
            <view class="info-item reward-item">
              <view class="info-header">
                <text class="info-icon">🎁</text>
                <text class="label">奖励对象</text>
              </view>
              <text class="value reward">{{ item.rewardTarget + '【' + item.rewardEmployeeNo + '】' }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="filteredList.length === 0 && !loading" class="empty-state">
        <text class="empty-text">暂无数据</text>
      </view>
    </view>

    <!-- 分页 -->
    <view class="pagination-wrapper" v-if="total > 0">
      <view class="pagination-info">
        <text>共 {{ total }} 条</text>
      </view>
      <view class="pagination-controls">
        <button
          class="page-btn"
          :disabled="currentPage <= 1"
          @click="handlePrevPage"
        >
          上一页
        </button>
        <text class="page-info">{{ currentPage }}/{{ totalPages }}</text>
        <button
          class="page-btn"
          :disabled="currentPage >= totalPages"
          @click="handleNextPage"
        >
          下一页
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      searchKeyword: '',
      selectedStatus: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      hasMore: true,

      // 状态选项
      statusOptions: [
        { label: '全部', value: '' },
        { label: '待确定', value: '待确定' },
        { label: '已返还', value: '已返还' },
        { label: '已奖励', value: '已奖励' }
      ],

      // 真实数据
      paymentList: []
    }
  },

  computed: {
    // 过滤后的列表（现在直接返回paymentList，因为过滤在API层面完成）
    filteredList() {
      return this.paymentList
    },

    // 总页数
    totalPages() {
      return Math.ceil(this.total / this.pageSize)
    }
  },

  onLoad() {
    this.loadData()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.refreshData()
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 1000)
  },

  onReachBottom() {
    // 上拉加载更多
    this.loadMoreData()
  },

  methods: {
    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        '待确定': 'status-warning',
        '已返还': 'status-info',
        '已奖励': 'status-success'
      }
      return statusMap[status] || 'status-info'
    },

    // 清空搜索
    clearSearch() {
      this.searchKeyword = ''
      this.handleSearch()
    },

    // 搜索处理
    handleSearch() {
      this.currentPage = 1
      this.hasMore = true
      this.paymentList = []
      this.loadData()
    },

    // 状态筛选处理
    handleStatusChange(status) {
      this.selectedStatus = status
      this.currentPage = 1
      this.hasMore = true
      this.paymentList = []
      this.loadData()
    },

    // 上一页
    handlePrevPage() {
      if (this.currentPage > 1) {
        this.currentPage--
        this.paymentList = []
        this.loadData()
      }
    },

    // 下一页
    handleNextPage() {
      if (this.currentPage < this.totalPages && this.hasMore) {
        this.currentPage++
        this.paymentList = []
        this.loadData()
      }
    },

    // 复制文本
    copyText(text, type) {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: `${type}已复制`,
            icon: 'success',
            duration: 1500
          })
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          })
        }
      })
    },

    // 加载数据
    async loadData() {
      this.loading = true
      try {
        // 构建请求参数
        const requestData = {
          current: this.currentPage,
          size: this.pageSize,
		  storeId: uni.getStorageSync('storeId'),
		  roleId: uni.getStorageSync('roleId')	  
        }

        // 添加搜索关键词
        if (this.searchKeyword) {
          requestData.search = this.searchKeyword
        }

        // 添加状态筛选
        if (this.selectedStatus) {
          requestData.state = this.selectedStatus=="待确定"?0:this.selectedStatus=="已返还"?1:this.selectedStatus=="已奖励"?2:""
        }

        this.http({
          // outsideUrl: 'http://localhost:15012/getRecruitmentMoneyData',
          url: 'getRecruitmentMoneyData',
          method: 'GET',
          data: requestData,
          success: res => {
            this.loading = false
            if (res.code === 0) {
              // 格式化数据
              const formattedData = this.formatPaymentData(res.data.records || [])

              if (this.currentPage === 1) {
                this.paymentList = formattedData
              } else {
                this.paymentList.push(...formattedData)
              }

              this.total = res.data.total || 0

              // 判断是否还有更多数据
              this.hasMore = this.currentPage < res.data.pages
            } else {
              uni.showToast({
                title: res.msg || '获取数据失败',
                icon: 'none'
              })
            }
          },
          fail: error => {
            this.loading = false
            console.error('获取数据失败:', error)
            uni.showToast({
              title: '网络错误，请重试',
              icon: 'none'
            })
          }
        })
      } catch (error) {
        this.loading = false
        console.error('加载数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    // 格式化支付数据
    formatPaymentData(records) {
      return records.map(record => {
        return {
          id: record.id,
          contractNumber: record.contractNo || '',
          remark: record.remark || '',
          orderNumber: record.billNo || '',
          status: this.mapStatus(record.status),
		  rewardEmployeeNo: record.rewardEmployeeNo || null,
          orderAmount: this.formatAmount(record.amount || 0),
          deductionAmount: this.formatAmount(record.deductAmount || 0),
          currentAuntie: record.nowEmployee || '',
          auntieStore: record.storeName || '',
          workingDays: record.upDays || 0,
          rewardTarget: record.rewardEmployeeName || null,
          createTime: record.createTime,
          deductTime: record.deductTime,
          changeTime: record.changeTime
        }
      })
    },

    // 映射状态
    mapStatus(status) {
      // 根据接口返回的status字段映射到前端显示的状态
      const statusMap = {
        null: '待确定',
        0: '待确定',
        1: '已返还',
        2: '已奖励'
      }
      return statusMap[status] || '待确定'
    },

    // 格式化金额
    formatAmount(amount) {
      if (!amount && amount !== 0) return '0.00'
      return parseFloat(amount).toFixed(2)
    },

    // 刷新数据
    refreshData() {
      this.currentPage = 1
      this.hasMore = true
      this.paymentList = []
      this.loadData()
    },

    // 加载更多数据
    loadMoreData() {
      if (this.loading || !this.hasMore) return

      this.currentPage++
      this.loadData()
    }
  }
}
</script>

<style scoped>
.payment-list-container {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.search-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 40rpx;
  border-radius: 24rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.search-box {
  margin-bottom: 30rpx;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20rpx;
  padding: 0 80rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.search-input {
  flex: 1;
  height: 88rpx;
  border: none;
  background: transparent;
  font-size: 28rpx;
  padding: 0 60rpx 0 20rpx;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.search-icon {
  position: absolute;
  left: 24rpx;
  font-size: 32rpx;
  color: #667eea;
}

.clear-icon {
  position: absolute;
  right: 24rpx;
  font-size: 28rpx;
  color: #999;
  padding: 10rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.clear-icon:active {
  background: rgba(0, 0, 0, 0.1);
}

.status-filter {
  display: flex;
  align-items: center;
  gap: 20rpx;
  overflow-x: auto;
  padding: 10rpx 0;
}

.status-item {
  padding: 20rpx 36rpx;
  border-radius: 50rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2rpx solid transparent;
  white-space: nowrap;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.status-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.status-item text {
  font-size: 28rpx;
  font-weight: 500;
}

.card-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.loading-wrapper {
  text-align: center;
  padding: 120rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.payment-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.payment-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.payment-card:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid rgba(102, 126, 234, 0.1);
}

.contract-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.contract-row {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.label-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 400;
}

.contract-number {
  font-weight: 600;
  font-size: 32rpx;
  color: #667eea;
  padding: 8rpx 16rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.contract-number:active {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(0.98);
}

.status-tag {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.status-warning {
  background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
  color: #d63031;
}

.status-info {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
}

.status-success {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  color: white;
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  gap: 40rpx;
}

.info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  padding: 20rpx;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 16rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}

.info-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.info-icon {
  font-size: 28rpx;
}

.label {
  color: #666;
  font-size: 24rpx;
  font-weight: 500;
}

.value {
  color: #333;
  font-size: 30rpx;
  font-weight: 600;
  margin-top: 4rpx;
}

.value.clickable {
  color: #667eea;
  padding: 8rpx 16rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.value.clickable:active {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(0.98);
}

.value.amount {
  color: #e17055;
  font-weight: 700;
}

.value.deduction {
  color: #fdcb6e;
  font-weight: 700;
}

.value.days {
  color: #74b9ff;
  font-weight: 700;
}

.value.reward {
  color: #00b894;
  font-weight: 700;
}

.reward-item {
  background: linear-gradient(135deg, rgba(0, 184, 148, 0.1) 0%, rgba(0, 160, 133, 0.1) 100%);
  border: 1rpx solid rgba(0, 184, 148, 0.2);
}

.empty-state {
  text-align: center;
  padding: 120rpx 0;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  margin: 40rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  font-weight: 500;
}

.pagination-wrapper {
  margin-top: 40rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 40rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-info text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.page-btn {
  padding: 20rpx 36rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.page-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.page-btn[disabled] {
  background: linear-gradient(135deg, #ddd 0%, #ccc 100%);
  color: #999;
  box-shadow: none;
}

.page-info {
  font-size: 24rpx;
  color: #333;
  margin: 0 20rpx;
  font-weight: 600;
}

/* 移动端适配 */
@media (max-width: 750rpx) {
  .payment-list-container {
    padding: 20rpx;
  }

  .search-header {
    padding: 30rpx;
  }

  .payment-card {
    padding: 30rpx;
  }

  .info-row {
    flex-direction: column;
    gap: 20rpx;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }

  .contract-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
    width: 100%;
  }

  .pagination-wrapper {
    flex-direction: column;
    gap: 20rpx;
  }

  .status-filter {
    gap: 16rpx;
  }

  .status-item {
    padding: 16rpx 28rpx;
  }

  .info-item {
    padding: 16rpx;
  }
}

/* 添加一些动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.payment-card {
  animation: fadeIn 0.5s ease-out;
}

.loading-wrapper {
  animation: fadeIn 0.3s ease-out;
}
</style>
