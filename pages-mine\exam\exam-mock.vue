<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 选题弹窗 -->
		<u-popup :show="popupShow" mode="bottom" @close="popupShow = false">
			<view class="filter-title" @click="popupShow = false">
				<uni-icons type="map" size="20" color="#909399"></uni-icons>
				<text>{{choiceQuestionIndex+1}}/{{questionAmount}}</text>
			</view>
			<view class="filter-content">
				<scroll-view :scroll-top="scrollTop1" scroll-y class="filter-scroll-Y">
					<view style="display: flex;flex-wrap: wrap;">
						<view class="tab-checkbox" v-for="(item,index) in examQuestion" :key="index"
							@click="choiceTab(index)">
							<view class="checkbox" :style="[formatStyle(0,index)]">
								<text v-model="index">{{index+1}}</text>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</u-popup>

		<!-- 结果弹窗 -->
		<u-popup :show="popupShow1" mode="bottom" @close="popupShow1 = false">
			<view class="fb f18 text-c lh50">
				模拟考试统计
			</view>
			<view class="filter-content f16 lh30 text-c" style="height: 400rpx;" @click="popupShow1 = false">
				<view>本次总计答题：{{totalNum}}道</view>
				<view>答对：{{rightNum}}道</view>
				<view>答错：{{totalNum-rightNum}}道</view>
				<view>正确率：{{totalNum?(rightNum/totalNum*100).toFixed(2):0}}%</view>
				<view style="color: #ff4d4b;">*可返回继续答题或查看错题哦</view>
			</view>
		</u-popup>

		<view v-if="flag==0">
			<view v-for="(item,index) in examQuestion" :key="index" v-show="choiceQuestionIndex==index">
				<view class="exam-tab">
					<!-- 倒计时 -->
					<view class="countdown">
						<uni-countdown color="#FFFFFF" :background-color="remainTime<=remindTime?'#ff4d4b':'#00B26A'"
							border-color="#00B26A" :day="0" :hour="0" :minute="0" :second="remainTime" :show-day="false"
							@timeup="examEnd()" style="float: right;">
						</uni-countdown>
					</view>
					<!-- 题目 -->
					<view class="tab" style="width: 100%;">
						<view class="tab-head-tag">
							<text @click="!item.answerRecord?isShow=!isShow:''"
								:style="[formatStyle(2,index)]">{{questionTypeList[item.questionType].text}}</text>
							<text>{{formatQuestion(index)}}</text>
							<text v-if="isShow">{{item.unionAnswerContent}}</text>
						</view>
					</view>

					<!-- 图片 -->
					<view class="tab" style="width: 100%;" v-if="showQuestionSource(0,index)">
						<view class="question-img" @click="openImgPreview(item.questionImg)">
							<img :src="item.questionImg" alt="" mode="widthFix" />
						</view>
					</view>

					<!-- 视频 -->
					<view class="tab" style="width: 100%;" v-if="showQuestionSource(1,index)">
						<view class="question-video">
							<video :controls="true" :enable-progress-gesture="true" :initial-time="0"
								:show-center-play-btn="true" :src="item.questionVideo" custom-cache="false" />
						</view>
					</view>
				</view>

				<view class="exam-tab" :style="[formatStyle(1,0)]">
					<!-- 单选/判断选项 -->
					<view class="tab" style="width: 100%;" v-if="item.questionType==0 || item.questionType==2">
						<view class="tab-choice" v-for="(item1,index1) in item.questionContent.choice" :key="index1"
							@click="clickChoice(index,index1)"
							:style="item.isCheck==index1?'background-color: #f4f4f5':''">
							<uni-icons type="circle" size="18" color="#909399" v-if="item.isCheck!=index1">
							</uni-icons>
							<uni-icons type="circle-filled" size="18" color="#909399" v-if="item.isCheck==index1">
							</uni-icons>
							<view class="choice-text" :style="item1.imgUrl?'display:flex;':''">
								<text>{{item1.flag}}. {{item1.content}}</text>
								<img :src="item1.imgUrl" mode="widthFix" v-if="item1.imgUrl" />
							</view>
						</view>
					</view>

					<!-- 多选选项 -->
					<view class="tab" style="width: 100%;" v-if="item.questionType==1">
						<view class="tab-choice" v-for="(item1,index1) in item.questionContent.choice" :key="index1"
							@click="clickChoice(index,index1)" :style="item1.isCheck==1?'background-color: #f4f4f5':''">
							<uni-icons type="circle" size="18" color="#909399" v-if="item1.isCheck==0">
							</uni-icons>
							<uni-icons type="circle-filled" size="18" color="#909399" v-if="item1.isCheck==1">
							</uni-icons>
							<view class="choice-text" :style="item1.imgUrl?'display:flex;':''">
								<text>{{item1.flag}}. {{item1.content}}</text>
								<img :src="item1.imgUrl" mode="widthFix" v-if="item1.imgUrl" />
							</view>
						</view>
					</view>

					<!-- 填空题输入框 -->
					<view class="tab" style="width: 100%;" v-if="item.questionType==3">
						<view class="tab-inputbox-high" v-for="(it,index1) in choiceCountList" :key="index1"
							v-if="index1<item.questionContent.choiceCount">
							<text>填空项{{index1+1}}</text>
							<u--input v-model="item.questionContent.answerContent[index1]" @input="inputAnswer(0,index)"
								placeholder="请输入填空项" border="bottom" clearable v-if="!item.answerRecord"
								customStyle="width: 400rpx;display: inline-block;margin-left: 20rpx;">
								填空项{{index}}
							</u--input>
							<text v-if="item.answerRecord">
								{{checkStr(item.questionContent.answerContent[index1])}}
							</text>
						</view>
					</view>

					<!-- 问答题输入框 -->
					<view class="tab" style="width: 100%;" v-if="item.questionType==4">
						<view class="tab-inputbox-high">
							<u--textarea class="multiline-input" confirmType="done" maxlength="200"
								v-model="item.answerContent" @input="inputAnswer(1,index)"
								:placeholder="questionTypeList[4].tips" height="150" count v-if="!item.answerRecord">
							</u--textarea>
							<text v-if="item.answerRecord">
								回答项：{{item.answerContent}}
							</text>
						</view>
					</view>
				</view>


				<!-- 答案解析 -->
				<view class="exam-tab" v-if="item.answerRecord" style="height: 1200rpx;">
					<view class="tab-text">
						<text>你的回答：</text>
						<text style="font-weight: 100;">{{formatAnswer(item.answerRecord.answerContent)}}</text>
						<uni-icons type="closeempty" size="18" color="#ff4d4b" v-if="item.answerRecord.isRight==0">
						</uni-icons>
						<uni-icons type="checkmarkempty" size="18" color="#19be6b" v-if="item.answerRecord.isRight==1">
						</uni-icons>
					</view>
					<view class="tab-text">
						<text>正确答案：</text><text style="font-weight: 100;">{{formatExpect(index)}}</text>
					</view>
					<view class="tab-text" v-if="false">
						<text>本题得分：</text><text style="font-weight: 100;">{{item.answerRecord.answerScore||0}}分</text>
					</view>
					<view class="tab-text">
						<text>答案解析</text>
					</view>
					<view class="tab-text">
						<text style="font-weight: 100;">{{checkStr(item.unionAnswerContent.analysis)}}</text>
					</view>
				</view>
			</view>

			<!-- 底栏切换 -->
			<view class="bottom-tab">
				<view @click="changeTab(0)">
					<uni-icons type="arrow-left" size="20" color="#909399" v-if="choiceQuestionIndex!=0"></uni-icons>
					<text v-if="choiceQuestionIndex!=0">上一题</text>
				</view>
				<view @click="popupShow = true">
					<uni-icons type="map" size="20" color="#909399"></uni-icons>
					<text>{{choiceQuestionIndex+1}}/{{questionAmount}}</text>
				</view>
				<view @click="changeTab(1)" v-if="choiceQuestionIndex+1!=questionAmount">
					<text>下一题</text>
					<uni-icons type="arrow-right" size="20" color="#909399"></uni-icons>
				</view>
				<view @click="changeTab(2)" v-if="choiceQuestionIndex+1==questionAmount">
					<text>结束</text>
					<uni-icons type="arrow-right" size="20" color="#909399"></uni-icons>
				</view>
			</view>
		</view>


		<view class="f16" v-if="flag==1">
			<img :src="postImg" mode="widthFix" style="width: 100%;" />

			<view class="fb f20 text-c lh60">
				请选择模拟考试题库
			</view>
			<view class="lib-tab flac-col lh30" v-for="(item,index) in libList" :key="index"
				:class="choiceIndex==index?'active':''" @click="choiceIndex =index">
				<view class="fb f18">
					{{item.name}}
				</view>
				<view class="">
					题库介绍：{{item.content}}
				</view>
			</view>
			<view class="lh40 text-c" v-if="libList.length">
				<view v-if="searchCondition.current>=pageCount">已显示全部内容</view>
				<view v-else @click="searchCondition.current++;pageUnionQuestionLib()">下滑查看更多...</view>
			</view>
			<u-empty v-if="!libList.length" text="暂无题库" icon="http://cdn.uviewui.com/uview/empty/data.png" />

			<u-gap height="200"></u-gap>

			<view class="btn-bottom-fixed">
				<button @click="start">开始模拟考试</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 参数设置部分
				// 0：模拟考试中 1：选择题库
				flag: 1,
				// 考试用时
				recordDuration: 0,
				// 提醒时间-考试即将结束
				remindTime: 60,
				// 是否查看完所有问题
				isViewAll: false,
				// 模拟考试时间（分钟）
				examDuration: 30,
				// 题目数量
				questionCount: 50,
				// baseUrl: 'https://api.xiaoyujia.com/',
				baseUrl: 'http://localhost:8080/',

				choiceIndex: 0,
				isFinished: false,
				isShow: false,
				popupShow: false,
				popupShow1: false,
				scrollTop: 0,
				scrollTop1: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				searchCondition: {
					state: 1,
					allowMock: 1,
					current: 1,
					size: 10
				},

				choiceQuestionIndex: 0,
				questionAmount: 0,
				notCheckAmount: 0,
				memberId: uni.getStorageInfoSync('memberId') || null,
				postImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam-center_1.png',
				recordId: 0,
				examId: 1,

				remainTime: 0,
				recordState: 0,
				postDate: [],
				libId: 0,
				exam: {},
				getQuestionData: {},
				examQuestion: [],
				choiceCountList: [0, 1, 2, 3, 4, 5],
				questionTypeList: [{
					text: "单选题",
					value: 0,
					tips: "只能选择一个选项",
					style: {
						backgroundColor: '#F9AE3D'
					}
				}, {
					text: "多选题",
					value: 1,
					tips: "可以选择多个选项",
					style: {
						backgroundColor: '#19be6b'
					}
				}, {
					text: "判断题",
					value: 2,
					tips: "只能选择一个选项",
					style: {
						backgroundColor: '#909399'
					}
				}, {
					text: "填空题",
					value: 3,
					tips: "将空格处的答案补充完整",
					style: {
						backgroundColor: '#2979ff'
					}
				}, {
					text: "问答题",
					value: 4,
					tips: "开放答案，将由人工阅卷",
					style: {
						backgroundColor: '#ff4d4b'
					}
				}],
				rightNum: 0,
				totalNum: 0,
				pageCount: 0,
				libList: []
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 考试结束
			examEnd() {
				this.$refs.uNotify.error("考试时间到！")
			},
			// 选择题目
			choiceTab(index) {
				this.choiceQuestionIndex = index
				this.popupShow = false
			},
			// 切换题目（上一题/下一题）
			changeTab(index) {
				let answerRecord = this.examQuestion[this.choiceQuestionIndex].answerRecord || null
				let isCheck = this.examQuestion[this.choiceQuestionIndex].isCheck
				if (index == 0) {
					if (this.choiceQuestionIndex == 0) {
						// this.$refs.uNotify.error("没有上一题了！")
						return
					}
					if (answerRecord || isCheck == -1) {
						this.choiceQuestionIndex--
					}
					// 没出则加载答案
					else {
						this.getAnalysisByQuestion(this.choiceQuestionIndex)
					}
				} else if (index == 1) {
					// 若题目已出答案，则跳到下一题
					if (answerRecord || isCheck == -1) {
						this.choiceQuestionIndex++
					}
					// 没出则加载答案
					else {
						this.getAnalysisByQuestion(this.choiceQuestionIndex)
					}

					// 查看完所有答案之后进行提示
					// if (this.choiceQuestionIndex + 1 == this.questionAmount && this.flag == 1 && !this.isViewAll) {
					// 	this.$refs.uNotify.warning("已经是最后一题了！")
					// }
				} else if (index == 2) {
					if (!answerRecord && isCheck == 1) {
						this.getAnalysisByQuestion(this.choiceQuestionIndex)
					}

					if (this.choiceQuestionIndex == this.examQuestion.length - 1) {
						this.submitExam()
						return
					}
				}
			},
			// 选中选项
			clickChoice(index, index1) {
				let question = this.examQuestion[index]
				let questionType = question.questionType
				let choice = question.questionContent.choice
				// 查看答案解析中，不可选择
				if (question.answerRecord) {
					return
				}

				// 单选/判断
				if (questionType == 0 || questionType == 2) {
					this.examQuestion[index].isCheck = index1
				}
				// 多选
				else if (questionType == 1) {
					if (choice[index1].isCheck == 0) {
						this.examQuestion[index].questionContent.choice[index1].isCheck = 1
						this.examQuestion[index].questionContent.choiceCount++
					} else {
						this.examQuestion[index].questionContent.choice[index1].isCheck = 0
						this.examQuestion[index].questionContent.choiceCount--
					}

					// 清空所有多选项
					if (this.examQuestion[index].questionContent.choiceCount != 0) {
						this.examQuestion[index].isCheck = 1
					} else {
						this.examQuestion[index].isCheck = -1
					}
				}
				// 填空题
				else if (questionType == 3) {

				}
				// 问答题
				else if (questionType == 4) {

				}
			},
			// 格式化题目
			formatQuestion(index) {
				let question = this.examQuestion[index]
				let title = question.questionTitle
				let remark = question.questionRemark
				let result = title
				if (remark != null && remark != "") {
					result += "（" + remark + "）"
				}
				return result
			},
			// 格式化回答
			formatAnswer(answer) {
				return answer.replace(/\|/g, "、")
			},
			// 格式化答案
			formatExpect(index) {
				let result = ""
				let expect = this.examQuestion[index].unionAnswerContent.expect
				let keyword = this.examQuestion[index].unionAnswerContent.keyword

				if (keyword != null && keyword != "") {
					result = keyword + "（答案包含关键词即可得分）"
				} else {
					result = expect.replace(/\|/g, "、")
				}
				return result
			},
			// 格式化字符串
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无"
				} else {
					return str
				}
			},
			// 显示试题资源（图片或视频）
			showQuestionSource(value, index) {
				let result = false
				let questionImg = this.examQuestion[index].questionImg
				let questionVideo = this.examQuestion[index].questionVideo
				if (value == 0) {
					if (this.checkStr(questionImg) != '暂无' && this.checkStr(questionVideo) == '暂无') {
						result = true
					}
				} else if (value == 1) {
					if (this.checkStr(questionVideo) != '暂无') {
						result = true
					}
				}
				return result
			},
			// 打开图片预览
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 输入答案
			inputAnswer(value, index) {
				// 填空题
				if (value == 0) {
					this.examQuestion[index].isCheck = 1
				}
				// 问答题
				else if (value == 1) {
					let answerContent = this.examQuestion[index].answerContent
					this.examQuestion[index].isCheck = answerContent != "" ? 1 : -1
				}
			},
			// 检查未答的题目
			getNotCheck() {
				let count = 0
				let result = true
				for (let item of this.examQuestion) {
					if (item.isCheck == -1) {
						count++
						result = false
					}
				}
				this.notCheckAmount = count
				return result
			},
			// 提交试卷前检查
			submitExam() {
				if (!this.getNotCheck()) {
					uni.showModal({
						title: '是否结束考试模拟？',
						content: "当前还有" + this.notCheckAmount + "题未回答完整",
						success: res => {
							if (res.confirm) {
								this.popupShow1 = true
							} else {
								this.$refs.uNotify.warning('可以继续答题或者查看错题哦！')
							}
						}
					});
				} else {
					this.$refs.uNotify.warning("当前已经是最后一题了！")
				}
			},
			// 处理交卷答案
			dealPost(item) {
				// 拼接格式化各类试题的答案
				let questionId = item.id
				let answerContent = ""
				let questionType = item.questionType
				let isCheck = item.isCheck
				let answer = {}

				// 未选择，则直接跳过不提交该题
				if (isCheck == -1) {
					return null
				}

				// 单选/判断题
				if (questionType == 0 || questionType == 2) {
					let choice = item.questionContent.choice
					answerContent = choice[isCheck].flag
				}
				// 多选题
				else if (questionType == 1) {
					let choice = item.questionContent.choice
					for (let it of choice) {
						if (it.isCheck == 1) {
							if (answerContent != "") {
								answerContent += "|"
							}
							answerContent += it.flag
						}
					}
				}
				// 填空题
				else if (questionType == 3) {
					let content = item.questionContent.answerContent
					let choiceCount = item.questionContent.choiceCount || 0
					for (let i = 0; i < choiceCount; i++) {

						answerContent += content[i]
						if (i != choiceCount - 1) {
							answerContent += "|"
						}

					}
				}
				// 问答题
				else if (questionType == 4) {
					answerContent = item.answerContent
				}

				this.$set(answer, "questionId", questionId)
				this.$set(answer, "answerContent", answerContent)
				return answer
			},
			// 获取考试试题解析
			getAnalysisByQuestion(index) {
				let data = this.examQuestion[index]
				let answer = this.dealPost(data) || null
				if (!answer) {
					this.$refs.uNotify.warning('请检查试题是否填写正确！')
				}
				this.$set(data, 'unionAnswerRecord', answer)
				this.http({
					outsideUrl: this.baseUrl + 'exam/getAnalysisByQuestion',
					method: 'POST',
					hideLoading: true,
					data: data,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$set(this.examQuestion[index], 'answerRecord', res.data.answerRecord)
							this.$set(this.examQuestion[index], 'unionAnswerContent', res.data
								.unionAnswerContent)
							this.totalNum++
							if (this.examQuestion[index].answerRecord.isRight == 1) {
								this.rightNum++
							}
						}
					}
				});
			},
			// 获取题库列表
			pageUnionQuestionLib() {
				if (this.flag != 1) {
					return
				}
				this.http({
					outsideUrl: this.baseUrl + 'exam/pageUnionQuestionLib',
					method: 'POST',
					hideLoading: true,
					data: this.searchCondition,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.libList = this.libList.concat(res.data.records)
							this.pageCount = res.data.pages
						} else if (this.searchCondition.current > 1) {
							this.$refs.uNotify.warning('暂无更多内容了哦')
						}
					}
				});
			},
			// 获取考题
			listMockExamQuestion() {
				if (this.flag != 0) {
					return
				}
				this.http({
					outsideUrl: this.baseUrl + 'exam/listMockExamQuestion',
					method: 'POST',
					data: {
						questionLibId: this.libId,
						questionType: null,
						questionCount: this.questionCount
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.examQuestion = res.data
							this.questionAmount = this.examQuestion.length
						} else {
							this.$refs.uNotify.error("考题获取失败！请返回后重试！" + res.msg)
						}
					}
				});
			},
			// 开始模拟考
			start() {
				this.flag = 0
				this.libId = this.libList.length ? this.libList[this.choiceIndex].id : this.libId
				setInterval(() => {
					if (this.examDuration * 60 - this.recordDuration > 0) {
						this.recordDuration += 1
					}
				}, 1000);
				this.listMockExamQuestion()
			}
		},
		computed: {
			// 格式化样式
			formatStyle() {
				return (value, index) => {
					let style = ""
					let recordState = this.recordState
					// 题目选框样式
					if (value == 0) {
						let isCheck = this.examQuestion[index].isCheck
						if (isCheck != -1) {
							style = {
								backgroundColor: '#19be6b',
								color: '#fff'
							}
							if (this.choiceQuestionIndex == index) {
								style = {
									color: '#19be6b',
									border: '#19be6b 4rpx dashed',
									backgroundColor: '#fff'
								}
							} else {

							}

							// 根据答题正确和错误显示不同样式
							if (this.examQuestion[index].answerRecord) {
								let isRight = this.examQuestion[index].answerRecord.isRight
								if (isRight == 0) {
									if (this.choiceQuestionIndex == index) {
										style = {
											color: '#ff4d4b',
											border: '#ff4d4b 4rpx dashed',
											backgroundColor: '#fff'
										}
									} else {
										style = {
											color: '#fff',
											backgroundColor: '#ff4d4b'
										}
									}
								} else if (isRight == 1) {
									if (this.choiceQuestionIndex == index) {
										style = {
											color: '#19be6b',
											border: '#19be6b 4rpx dashed',
											backgroundColor: '#fff'
										}
									} else {
										style = {
											color: '#fff',
											backgroundColor: '#19be6b'
										}
									}
								}
							}
						} else {
							if (this.choiceQuestionIndex == index) {
								style = {
									backgroundColor: '#fff',
									border: '#1e1848 4rpx dashed',
									color: '#000'
								}
							} else {

							}
						}
					}
					// 选项区域样式
					else if (value == 1) {
						// 若正在考试，不显示答案解析
						if (!this.examQuestion[index].answerRecord) {
							style = {
								// boxShadow: '0 0rpx 0rpx #dedede',
								height: '1200rpx',
							}
						} else if (this.examQuestion[index].answerRecord) {

						}
					}
					// 题目类型标签颜色
					else if (value == 2) {
						let type = this.examQuestion[index].questionType
						style = this.questionTypeList[type].style
					}
					return style
				}
			},
		},
		onReachBottom() {
			this.searchCondition.current++
			this.pageUnionQuestionLib()
		},
		watch: {
			remainTime: {
				handler(newValue, oldVal) {
					if (this.flag != 0) {
						return
					}

					// 自动交卷
					if (this.remainTime == 0) {
						return this.$refs.uNotify.warning('模拟时间已到！可选择继续进行答题！')
					}

				},
				deep: true
			},
		},
		onLoad(options) {
			this.flag = options.flag || 1
			this.libId = options.libId || 0
			if (options.libId) {
				this.libId = options.libId || 0
				this.flag = 0
			}
			this.listMockExamQuestion()
			this.remainTime = parseInt(this.examDuration * 60 - this.recordDuration)
			if (this.flag == 0) {
				this.start()
			} else {
				this.pageUnionQuestionLib()
			}
		},
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/exam.scss";

	.countdown {
		width: 90%;
		line-height: 100rpx;
		height: 100rpx;
		margin: 0 5%;
	}

	.tag-small {
		position: absolute;
		width: 100%;

		text {
			position: absolute;
			padding: 0 20rpx;
			left: 40rpx;
			top: 130rpx;

			height: 60rpx;
			line-height: 60rpx;
			width: auto;

			font-size: 28rpx;
			border-radius: 30rpx;
			color: #F9AE3D;
			background-color: rgba(254, 251, 251, 0.8);
		}
	}

	.lib-tab {
		padding: 40rpx 40rpx;
		border-bottom: 2rpx solid #dedede;
	}

	.active {
		border: #1e1848 4rpx solid;
	}
</style>