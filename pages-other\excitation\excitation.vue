<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 选择器 -->
		<u-popup :show="showPickerMine" @close="showPickerMine=false">
			<uni-search-bar class="w85 mg-at" placeholder="搜索" bgColor="#f6f8fa" :radius="100"
				v-model="searchPickerMineText" cancelButton="none" @input="searchPickerMine">
			</uni-search-bar>
			<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
				<u-radio-group iconPlacement="left" v-model="pickerIndex" placement="column" borderBottom
					@change="changePickerMine">
					<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, index) in pickerMineList" :key="index"
						:label="item.realName+'（'+item.employeeNo+'）'" :name="index" v-if="item.show" />
				</u-radio-group>
			</scroll-view>
		</u-popup>

		<view class="w9 mg-at">
			<u--form :model="dom" labelPosition="top" labelWidth="330" :labelStyle="labelStyle">
				<u-form-item label="表彰员工" required>
					<view class="flac-col">
						<u-search bgColor="#fff" :clearabled="true" :showAction="true" :animation="true"
							v-model="searchText" placeholder="通过姓名/工号进行搜索" @search="search" @custom="search"
							@clear="searchText='';" />
						<view class="f16 lh40" style="color: #ff4d4b;">
							点击搜索按钮后，记得勾选列表项哦
						</view>
					</view>
				</u-form-item>
				<u-form-item label="表彰类型" required>
					<scroll-view :scroll-top="0" scroll-x="true" style="width: 670rpx;">
						<view class="w10 mg-at flac-row f16">
							<view class="flac-col-c" v-for="(item,index) in rewardTypeList" :key="index"
								style="margin: 20rpx 20rpx;" @click="choiceType(index)">
								<img :src="item.typeImg" style="width: 160rpx;height: 160rpx;"
									:class="choiceIndex==index?'active':''" />
								<text style="margin: 20rpx 0;">{{item.typeName}}</text>
							</view>
						</view>
					</scroll-view>
				</u-form-item>
				<u-form-item label="打赏积分" prop="rewardAmount" required>
					<view class="flac-col">
						<u-input placeholder="发布表彰后自动赠送积分" v-model="dom.rewardAmount" type="number"
							:disabled="pageType==1" />
						<view v-if="pageType==0">
							<view style="margin-top: 20rpx;">
								当前余额：<text :style="rewardAuth?'text-decoration:line-through;':''">{{jiaBiAmount}}积分
								</text>
							</view>
							<view class="f16 lh40" style="color: #ff4d4b;">
								{{rewardAuth?'tips：管理员无需消耗余额，上限'+rewardLimit+'积分':'参与打赏，共同进步吧！'}}
							</view>
						</view>
					</view>
				</u-form-item>
				<u-form-item label="打赏人" prop="reference">
					<u-input placeholder="若未填写则默认为当前发布表彰者" v-model="dom.reference" />
				</u-form-item>
				<u-form-item label="工作内容" prop="introduce">
					<u-textarea placeholder="请填写表彰人工作内容" v-model="dom.introduce" />
				</u-form-item>
				<u-form-item label="工作事迹" prop="workDeeds">
					<u-textarea placeholder="请填写表彰人工作事迹" v-model="dom.workDeeds" />
				</u-form-item>
				<u-form-item label="图片展示">
				</u-form-item>
			</u--form>
			<shmily-drag-image v-model="imgList" keyName="imgUrl" class="w10 mg-at" :addImage="uploadImg"
				:delImage="delImage" @delDragImg="delDragImg"></shmily-drag-image>
		</view>

		<view class="btn-bottom-fixed" @click="pageType==0?save():update()">
			<button style="background-color:#1e1848;">
				{{pageType==0?'发布表彰':'保存修改'}}
			</button>
		</view>
		<u-gap height="400"></u-gap>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 最大显示图片数
				maxShowPhoto: 3,
				// 打赏权限
				rewardAuth: false,
				// 打赏上限
				rewardLimit: 10000,
				// 打赏管理员会员id
				rewardMemberId: 298434,
				// 页面类型（0：发布 1：修改）
				pageType: 0,

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				labelStyle: {
					'font-weight': 'bold',
					'font-size': '32rpx',
					'letter-spacing': '5rpx',
				},
				dom: {
					memberId: null,
					employeeId: null,
					name: '',
					depart: '',
					introduce: '',
					workDeeds: '',
					headPortrait: '',
					reference: '',
					selfReference: '',
					rewardType: '',
					rewardAmount: '',
					creator: uni.getStorageSync("employeeNo") || null,
				},
				searchText: '',
				name: "",
				pickerIndex: 0,
				choicePickerMineValue: 0,
				pickerMineName: '',
				searchPickerMineText: '',
				showPickerMine: false,
				pickerMineList: [],

				showPopup: false,
				blankHeadImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				defaultPost: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/vote_share.png",
				memberId: uni.getStorageSync("memberId") || 0,
				employeeId: uni.getStorageSync("employeeId") || null,
				choiceIndex: 0,
				choiceImgIndex: 0,
				employeeList: [],
				rewardTypeList: [],
				imgList: [],
				excellentEmployeeId: null,
				jiaBiAmount: 0,
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 格式化时间
			formatDate(value) {
				if (value == null) {
					return "-"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '-' + MM + '-' + d
			},
			formatName(val) {
				return val.name || val.employeeName || val.memberName || '匿名用户'
			},
			formatHeadImg(val) {
				return val.employeeHeadImg || val.memberHeadImg || this.blankHeadImg
			},
			// 选择奖励类型
			choiceType(index) {
				if (this.pageType == 0) {
					this.choiceIndex = index
					this.dom.rewardAmount = this.rewardTypeList[index].defaultReward || 1
				}
			},
			// 搜索
			search() {
				if (this.pageType == 0) {
					this.listExcitationEmployee()
				} else {
					return this.$refs.uNotify.warning('该表彰已发布，无法修改表彰员工，只能修复表彰内容！')
				}
			},
			// 打开选择器
			openPickerMine(value) {
				if (value == 0) {
					this.pickerMineName = "realName"
					this.pickerMineList = this.employeeList
				} else if (value == 1) {

				}

				this.pickerMineList.forEach(item => {
					this.$set(item, 'show', true)
				})
				this.searchPickerMine(this.searchPickerMineText)
				this.choicePickerMineValue = value
				this.showPickerMine = true
			},
			// 选择器选择
			changePickerMine(e) {
				let value = this.choicePickerMineValue
				let index = e
				if (value == 0) {
					let item = this.employeeList[index]
					this.dom.employeeId = item.id
					this.dom.memberId = item.memberId
					this.dom.name = item.realName
					this.dom.headPortrait = item.headPortrait
					this.dom.depart = item.departName
					this.searchText = item.realName
				}
				this.showPickerMine = false
				this.popupShow = true
			},
			// 选择器搜索
			searchPickerMine(e) {
				if (e) {
					this.pickerMineList.forEach(item => {
						if (item[this.pickerMineName].includes(e)) {
							this.$set(item, 'show', true)
						} else {
							this.$set(item, 'show', false)
						}
					})
				} else {
					this.pickerMineList.forEach(item => {
						this.$set(item, 'show', true)
					})
				}
			},
			checkLogin() {
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.warning("您还未进行登录哦，请先登录吧！")
					uni.setStorageSync('redirectUrl', '/pages-other/excitation/excitation')
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						})
					}, 2000)
					return false
				} else {
					return true
				}
			},
			// 上传图片
			uploadImg() {
				let url = "https://api.xiaoyujia.com/system/imageUpload"
				uni.chooseImage({
					count: 9,
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						for (let i = 0; i < tempFilePaths.length; i++) {
							let item = tempFilePaths[i]
							uni.uploadFile({
								url: url,
								filePath: item,
								name: 'file',
								formData: {
									route: 'userPhotos'
								},
								dataType: 'json',
								success: res => {
									let result = JSON.parse(res.data)
									let imgUrl = result.data
									let data = {
										'id': 0,
										'imgUrl': imgUrl
									}
									this.imgList.push(data)
								}
							});
						}
					}
				});
			},
			delDragImg(index) {
				this.choiceImgIndex = index
			},
			// 删除个人照片
			delImage(done) {
				uni.showModal({
					content: '确定删除该照片吗，删除后不可恢复！?',
					success: res => {
						if (res.confirm) {
							this.delete(done)
						}
					}
				})
			},
			// 删除照片-旧方法
			delete(done) {
				let id = this.imgList[this.choiceImgIndex].id
				if (id == 0) {
					this.$delete(this.imgList, this.choiceImgIndex)
					this.$refs.uNotify.success("照片删除成功！")
					return
				}

				this.http({
					url: 'deleteExcellentEmployeeImg',
					method: 'GET',
					path: id,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("照片删除成功！")
							done()
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			//输入校验
			checkInput() {
				if (!this.dom.name) {
					this.$refs.uNotify.warning('请选择当前需要表彰的员工！')
					return false
				}
				if (!this.dom.memberId) {
					this.$refs.uNotify.warning('该员工未登录过家姐联盟，暂时无法进行表彰！请确保已通过搜索框搜索到员工并进行勾选！')
					return false
				}
				if (!this.dom.rewardAmount) {
					this.$refs.uNotify.warning('打赏积分未填写！')
					return false
				}
				if (!this.dom.introduce && !this.dom.workDeeds) {
					this.$refs.uNotify.warning('工作介绍和工作事迹至少填写一个！')
					return false
				}

				if (!this.rewardAuth && this.dom.rewardAmount > this.jiaBiAmount) {
					this.$refs.uNotify.warning('积分余额不足，请重新填写打赏积分！')
					return false
				}
				return true
			},
			// 添加优秀员工
			save() {
				if (!this.checkInput()) {
					return
				}
				this.dom.rewardType = this.rewardTypeList[this.choiceIndex].id
				uni.showModal({
					title: '确定进行表彰吗？',
					content: '表彰后帖子将设为公开状态，可进行今日标杆投票，同时打赏将立即到账！',
					success: (res) => {
						if (res.confirm) {
							this.http({
								url: 'insertExcellentEmployee',
								method: 'POST',
								header: {
									'content-type': 'application/json;charset=UTF-8'
								},
								data: this.dom,
								success: res => {
									if (res.code == 0) {
										this.$refs.uNotify.success('表彰成功！')
										this.pageType = 1
										this.dom = res.data
										this.excellentEmployeeId = this.dom.id
										this.rewardExcellentEmployee()
										this.sendExcitationMsg(this.excellentEmployeeId)
										this.updateExcellentEmployeeImgList()
									} else {
										this.$refs.uNotify.error(res.msg)
									}
								},
							})
						}
					}
				});
			},
			// 更新
			update() {
				if (!this.checkInput()) {
					return
				}
				this.http({
					url: 'updateExcellentEmployee',
					method: 'POST',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: this.dom,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('修改成功！')
							this.updateExcellentEmployeeImgList()
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
				})
			},
			// 更新图片
			updateExcellentEmployeeImgList() {
				if (!this.imgList.length) {
					return
				}
				this.imgList.forEach(item => {
					this.$set(item, 'excellentEmployeeId', this.excellentEmployeeId)
				})
				this.http({
					url: 'updateExcellentEmployeeImgList',
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: this.imgList,
					success: res => {
						if (res.code == 0) {
							this.listExcellentEmployeeImgById()
						}
					},
				})
			},
			//获取图片列表
			listExcellentEmployeeImgById() {
				this.http({
					url: 'listExcellentEmployeeImgById',
					method: 'GET',
					hideLoading: true,
					path: this.dom.id,
					success: res => {
						if (res.code == 0) {
							this.imgList = res.data || []
						} else {
							this.imgList = []
						}
					},
				})
			},
			// 奖励类型列表
			listExcellentEmployeeRewardType() {
				this.http({
					url: 'listExcellentEmployeeRewardType',
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.rewardTypeList = res.data
							this.choiceType(0)
							this.getExcellentEmployee()
						}
					},
				})
			},
			// 获取员工列表
			listExcitationEmployee() {
				if (!this.searchText) {
					return this.$refs.uNotify.error('请输入关键词进行查询！')
				}
				this.http({
					url: 'listExcitationEmployee',
					method: 'POST',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						search: this.searchText,
						// employeeType: 20,
						state: 1
					},
					success: res => {
						if (res.code == 0) {
							this.employeeList = res.data
							this.openPickerMine(0)
							this.changePickerMine(0)
							this.showPickerMine = true
						} else {
							this.employeeList = []
							this.$refs.uNotify.warning('搜索不到相关员工，请重试！')
						}
					},
				})
			},
			// 获取优秀员工信息
			getExcellentEmployee() {
				if (!this.excellentEmployeeId) {
					return
				}
				this.http({
					url: 'getExcellentEmployee',
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						id: this.excellentEmployeeId || 0
					},
					success: res => {
						if (res.code == 0) {
							this.dom = res.data
							this.searchText = this.dom.name
							this.pageType = 1
							this.listExcellentEmployeeImgById()
							for (let i = 0; i < this.rewardTypeList.length; i++) {
								let id = this.rewardTypeList[i].id
								if (id == this.dom.rewardType) {
									this.choiceIndex = i
									break
								}
							}
						}
					},
				})
			},
			// 打赏
			rewardExcellentEmployee() {
				if (!this.checkLogin()) {
					return
				}
				this.http({
					url: 'rewardExcellentEmployee',
					method: 'POST',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						memberId: this.rewardAuth ? this.rewardMemberId : this.memberId,
						employeeId: this.employeeId || null,
						excellentEmployeeId: this.dom.id,
						rewardAmount: this.dom.rewardAmount
					},
					success: res => {
						if (res.code == 0) {
							this.getjiaBiAmount()
						}
					},
				})
			},
			// 激励宝企微消息推送
			sendExcitationMsg(id) {
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/content/sendExcitationMsg',
					method: 'GET',
					hideLoading: true,
					path: id,
					success: res => {},
				})
			},
			// 获取佳币数量
			getjiaBiAmount() {
				this.http({
					url: 'getJiaBi',
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.memberId || 0
					},
					success: res => {
						if (res.code == 0) {
							this.jiaBiAmount = res.data.jiaBiAmount
						}
					},
				})
			},
			// 校验打赏权限
			checkRewardAuth() {
				this.http({
					url: 'checkRewardAuth',
					method: 'POST',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					hideLoading: true,
					data: {
						roleId: uni.getStorageSync('roleId') || 0,
						employeeId: uni.getStorageSync('employeeId') || null,
					},
					success: res => {
						if (res.code == 0) {
							this.rewardAuth = true
						}
					},
				})
			},
			// 分享到好友或朋友圈
			onShareAppMessage(res) {
				let img = this.defaultPost
				return {
					title: '标杆员工评选，请投出您宝贵的一票吧！',
					path: '/pages-other/excitation/excitation-vote',
					mpId: 'wx8342ef8b403dec4e',
					imageUrl: img
				}
			},
		},
		onLoad(options) {
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				this.excellentEmployeeId = obj.t || this.excellentEmployeeId
			}
			this.excellentEmployeeId = options.id || this.excellentEmployeeId
			this.listExcellentEmployeeRewardType()
			this.checkRewardAuth()
			this.getjiaBiAmount()
		},
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";
	@import "@/pages-mine/common/css/resume-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}

	.active {
		border: 10rpx #19be6b solid;
		width: 220rpx;
		height: 300rpx;
		// margin: 10rpx 10rpx;
	}
</style>