<template>
	<view class="page">
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 选择器 -->
		<u-popup :show="showPickerMine" @close="showPickerMine=false">
			<uni-search-bar class="w85 mg-at" placeholder="搜索" bgColor="#f6f8fa" :radius="100"
				v-model="searchPickerMineText" cancelButton="none" @input="searchPickerMine">
			</uni-search-bar>
			<scroll-view :scroll-top="0" scroll-y="true" style="height: 1000rpx;">
				<u-radio-group iconPlacement="left" v-model="pickerIndex" placement="column" borderBottom
					@change="changePickerMine">
					<u-radio :customStyle="{margin: '30rpx 20rpx'}" v-for="(item, index) in pickerMineList" :key="index"
						:label="item[pickerMineName]" :name="index" v-if="item.show" />
				</u-radio-group>
			</scroll-view>
		</u-popup>

		<!-- 操作确认弹窗-加备注 -->
		<uni-popup ref="popupCheckDetail" type="dialog">
			<uni-popup-dialog type="success" cancelText="取消" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()">
				<view class="popupCheck-inputbox">
					<text>{{checkText}}</text>
					<view class="tab-inputbox">
						<view class="tab-input">
							<input class="single-input" type="text" v-model="trialStaff.workRemark"
								placeholder="请输入移除/删除/下架原因" />
						</view>
					</view>
				</view>

			</uni-popup-dialog>
		</uni-popup>

		<u-popup :show="popupShow" mode="right" @close="popupShow = false">
			<view style="width: 600rpx;">
				<view class="f18 fb text-c lh50">筛选</view>
				<view class="filter-tab">
					<u-search :clearabled="true" :showAction="false" margin="20rpx 20rpx"
						v-model="searchCondition.search" placeholder="员工姓名、手机号、工号等"></u-search>

					<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y-high">
						<view class="tab-title">
							<text>所属门店</text>
						</view>
						<view class="tab-picker" @click="openPickerMine(0)">
							<text class="picker-text" v-if="storeName == ''">点击选择门店</text>
							<text class="picker-text" v-if="storeName !== ''">{{ storeName }}</text>
							<uni-icons class="picker-text-arrow" type="forward" size="18"></uni-icons>
						</view>

						<u-gap height="120"></u-gap>
					</scroll-view>
				</view>

				<view class="filter-button">
					<view style="width: 31%;height: 120rpx;">
						<view class="filter-button-left" @click="reset()">
							<text>重置</text>
						</view>
					</view>
					<view style="width: 51%;height: 120rpx;" @click="search()">
						<view class="filter-button-right">
							<text>确定</text>
						</view>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 备注弹窗 -->
		<u-popup :show="popupShowRemark" :round="10" mode="bottom" @close="popupShowRemark=false" :closeable="true">
			<view class="" v-if="list1.length">
				<view class="f18 fb text-c lh50">更改员工备注</view>
				<view style="width: 100%;;height: 600rpx;">
					<view class="tab-inputbox-high" style="margin: 10rpx 40rpx 30rpx 40rpx;">
						<u--textarea class="multiline-input" confirmType="done" maxlength="200" v-model="remark"
							placeholder="员工备注" height="100" count :disabled="!isAuth"></u--textarea>
					</view>
					<view class="tab-title">
						<text style="font-size: 32rpx;text-align: right;font-weight: 100;color: #909399;">
							* 仅管理员和店长可编辑</text>
					</view>
				</view>
				<view class="filter-button">
					<view style="width: 40%;height: 120rpx;">
						<view class="filter-button-left" @click="remark=''">
							<text>清空备注</text>
						</view>
					</view>
					<view style="width: 60%;height: 120rpx;" @click="updateEmployee(0)">
						<view class="filter-button-right">
							<text>保存</text>
						</view>
					</view>
				</view>
			</view>
		</u-popup>

		<u-popup :show="popupShowInfo" :round="10" mode="bottom" @close="popupShowInfo=false" :closeable="true">
			<view class="" v-if="list1.length">
				<view class="f18 fb text-c lh50">更改员工信息</view>

				<scroll-view :scroll-top="scrollTop" scroll-y style="height: 800rpx;">
					<view class="filter-tab">
						<view class="tab-title" style="margin-top: 20rpx;">
							<text>员工姓名</text>
						</view>
						<view class="tab-inputbox">
							<view class="tab-input"><input class="single-input" type="text"
									v-model="list1[choiceItemIndex].realName" placeholder="填写员工姓名" /></view>
						</view>

						<view class="tab-title" style="margin-top: 20rpx;">
							<text>员工状态</text>
						</view>
						<view class="tab-checkbox" style="width: 300rpx;margin-left: 40rpx;"
							v-for="(item,index) in stateList" :key="index" v-if="index==1||index==2">
							<view class="checkbox" :class="{activeBox: index==choiceStateIndex}">
								<text @click="choiceStateIndex=index">{{item.text}}</text>
							</view>
						</view>

						<view style="width: 86%;padding: 20rpx 7% 200rpx 7%">
							<text style="display: block;font-size: 32rpx;color: #909399;">
								状态更改后立即生效，下架员工无法使用门店管理功能</text>
							<text style="display: block;font-size: 32rpx;color: #ff4d4b;">
								* 仅管理员和店长可以更改员工信息！</text>
						</view>
					</view>
				</scroll-view>
				<view class="filter-button">
					<view style="width: 40%;height: 120rpx;">
						<view class="filter-button-left" @click="popupShowInfo=false">
							<text>取消</text>
						</view>
					</view>
					<view style="width: 60%;height: 120rpx;" @click="updateEmployee(1)">
						<view class="filter-button-right">
							<text>保存</text>
						</view>
					</view>
				</view>
			</view>
		</u-popup>

		<u-popup :show="popupShowAdd1" :round="10" mode="bottom" @close="popupShowAdd1=false" :closeable="true">
			<view class="f18 fb text-c lh50">添加员工</view>

			<scroll-view :scroll-top="scrollTop" scroll-y style="height: 1300rpx;">
				<view class="filter-tab">
					<view class="tab-title">
						<text>手机号</text>
						<text style="color: #ff4d4b;">*</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="number" v-model="employeeNew.phone"
								placeholder="填写员工手机号" /></view>
					</view>

					<view class="tab-title">
						<text>姓名</text>
						<text style="color: #ff4d4b;">*</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="text" v-model="employeeNew.realName"
								placeholder="填写员工姓名/上传身份证识别" /></view>
					</view>

					<view class="tab-title">
						<text>身份证号</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="text" v-model="employeeNew.idCard"
								placeholder="填写员工身份证号码/上传身份证识别" /></view>
					</view>

					<view class="tab-title">
						<text>工号</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="text" v-model="employeeNew.no"
								placeholder="由系统自动生成" :disabled="true" /></view>
					</view>

					<view class="tab-title" style="margin-top: 20rpx;">
						<text>员工类型</text>
					</view>
					<view class="tab-checkbox" style="width: 300rpx;margin-left: 40rpx;"
						v-for="(item,index) in typeList" :key="index">
						<view class="checkbox" :class="{activeBox: index==choiceTypeIndex}">
							<text v-model="item.value" @click="choiceTypeIndex=index">{{item.text}}</text>
						</view>
					</view>

					<view style="width: 86%;padding: 20rpx 7% 200rpx 7%">
						<text style="display: block;font-size: 32rpx;color: #909399;">
							添加后将为对应账号开通门店员工权限，并使用门店运营相关功能</text>
						<text style="display: block;font-size: 32rpx;color: #ff4d4b;">
							* 最多只能同时存在5名【上架】员工！</text>
					</view>
				</view>
			</scroll-view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="employeeNew.realName='';employeeNew.phone=''">
						<text>清空输入</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="createStoreNewStaff()">
					<view class="filter-button-right">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>

		<u-popup :show="popupShowAdd" :round="10" mode="bottom" @close="popupShowAdd=false" :closeable="true">
			<view class="f18 fb text-c lh50">添加员工</view>

			<scroll-view :scroll-top="scrollTop" scroll-y style="height: 1300rpx;">
				<view class="filter-tab">
					<view class="tab-title">
						<text>手机号</text>
						<text style="color: #ff4d4b;">*</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="number" v-model="employeeNew.phone"
								placeholder="填写员工手机号" /></view>
					</view>

					<view class="tab-title">
						<text>姓名</text>
						<text style="color: #ff4d4b;">*</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="text" v-model="employeeNew.realName"
								placeholder="填写员工姓名/上传身份证识别" /></view>
					</view>

					<view class="tab-title">
						<text>身份证号</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input"><input class="single-input" type="text" v-model="employeeNew.idCard"
								placeholder="填写员工身份证号码/上传身份证识别" /></view>
					</view>

					<view class="tab-title">
						<text>身份证上传</text>
					</view>
					<view class="flac-row">
						<view class="w5">
							<img :src="identityFront"
								style="display:block;width:280rpx;height: 160rpx;margin: 0 auto;border-radius: 10rpx;"
								@click="uploadImg(0)">
						</view>
						<view class="w5">
							<img :src="identityBack"
								style="display:block;width: 280rpx;height: 160rpx;margin: 0 auto;border-radius: 10rpx;"
								@click="uploadImg(1)">
						</view>
					</view>

					<view style="width: 86%;padding: 20rpx 7% 200rpx 7%">
						<text style="display: block;font-size: 32rpx;color: #909399;">
							自助添加的员工将入驻到您的门店，并计入开发！（记得后续帮Ta完善资料哦）</text>
						<text style="display: block;font-size: 32rpx;color: #ff4d4b;">
							* 请在 15 日内完成资料完善，否则将清除对应的开发权益</text>
					</view>
				</view>
			</scroll-view>

			<view class="filter-button">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="employeeNew.realName='';employeeNew.phone=''">
						<text>清空输入</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="checkEmployeeByIdCard()">
					<view class="filter-button-right">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>


		<u-popup :show="popupShowLog" mode="bottom" @close="popupShowLog = false">
			<view class="filter-title">
				<text>员工日志</text>
			</view>

			<scroll-view :scroll-top="scrollTop" scroll-y class="filter-scroll-Y">
				<view class="log-list" v-for="(item,index) of logList" :key="index">
					<view style="display: flex;width: 100%;height: auto;line-height: 60rpx;">
						<uni-icons type="smallcircle-filled" style="margin-right: 20rpx;" size="12" color="#19be6b">
						</uni-icons>
						<text style="font-weight: bold;">{{item.operationName||'-'}}</text>
					</view>
					<text>时间：{{item.creatDate}}</text>
					<text>操作人：{{item.operatorName||'系统'}}</text>
				</view>

				<u-empty v-if="logList.length==0" text="暂无日志" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</scroll-view>
		</u-popup>

		<!-- 导航栏 -->
		<u-sticky bgColor="#fff">
			<view class="w9 mg-at">
				<u-tabs :list="tabsList" :current="current" :scrollable="false" lineWidth="100" lineHeight="2"
					lineColor="#4a456b" :activeStyle="{fontSize:'36rpx',color: '#4a456b',fontWeight: 'bold',}"
					:inactiveStyle="{fontSize:'36rpx',color: '#666',}" itemStyle="width:50%;margin: 20rpx auto"
					@click="changePage" />
			</view>
		</u-sticky>

		<!-- 服务人员 -->
		<view class="" v-if="current == 0">
			<view class="lh20" style="padding: 20rpx 40rpx;background-color: #f4f4f5;">
				服务员工包括门店标准单员工（保洁、搬家、清洗等），主要面向客户提供服务
			</view>
			<view class="w10 bac1e1848" style="padding: 20rpx 0;">
				<u--input placeholder="请输入员工姓名/手机号进行搜索" prefixIcon="search" color="#eee"
					prefixIconStyle="font-size: 22px;color: #999" border="none" customStyle="width:90%;margin:auto"
					v-model="searchCondition.search" @confirm="search">
					<template slot="suffix">
						<u-button text="搜索" type="info" @click="search" size="mini"></u-button>
					</template>
				</u--input>
			</view>

			<!-- 头部菜单-一级 -->
			<view class="tab-menu" style="margin: 0 40rpx;">
				<u-sticky>
					<u-tabs :list="menuList" @click="choiceMenu" :current="current1" lineWidth="22" lineHeight="8"
						:lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
				        color: '#1e1848',
				        fontWeight: 'bold',
				        transform: 'scale(1.2)'
				    }" :inactiveStyle="{
				        color: '#333',
				        transform: 'scale(1.05)'
				    }" itemStyle="padding-left: 15px; padding-right: 15px; height: 45px;">
					</u-tabs>
				</u-sticky>
			</view>

			<!-- 头部菜单-二级 -->
			<view class="w95 flac-row-b" style="margin: 10rpx 0;">
				<view class="f16 w6" style="margin: 20rpx 0;padding: 0 5rpx;">
					<view style="display: inline-block;padding: 0 40rpx;" v-for="(choiceList, index) in choiceList"
						:key="index" v-if="choiceList.show" @click="choiceTab(index)">
						<text :class="{activeChoice: choiceIndex == index}"
							class="choice-title">{{choiceList.choiceTitle}}</text>
					</view>
				</view>
				<view class="flac-row" @click="popupShow=true">
					<u-icon name="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-shaixuan.png"
						size="15"></u-icon>
					<text class="f16 t-indent">筛选</text>
				</view>
			</view>

			<uni-swipe-action>
				<uni-swipe-action-item v-for="(item, index) in list" :key="index"
					:right-options="choiceIndex==0?options.option:options.option1" @click="clickOption"
					@change="choiceItemIndex = index">
					<view class="baomu-tab" style="border-bottom: #f4f4f5 4rpx solid;">
						<view class="tab-left">
							<view class="tab-left-img">
								<img :src="item.headPortrait||blankImg" @click="openImgReview(index)">
							</view>
							<view class="tab-left-btn" v-if="choiceIndex==0">
								<u-tag text="员工上架" @click="openTab(3,index)" color="#FFFFFF" bgColor="#1e1848" plain
									shape="circle" size="mini" borderColor="#1e1848"
									v-if="item.examinationState==1"></u-tag>
							</view>
							<view class="tab-left-btn" v-if="choiceIndex==0">
								<u-tag text="上架催审" @click="urgeListing(index)" color="#FFFFFF" bgColor="#1e1848" plain
									shape="circle" size="mini" borderColor="#1e1848" v-if="item.state!=1"></u-tag>
							</view>
						</view>
						<view class="tab-right">
							<view>
								<view style="width: 100%;height: 100rpx;line-height: 100rpx;display: flex;">
									<text style="font-weight: bold;font-size: 40rpx;">{{item.name}}</text>
									<text v-if="item.post">（{{item.post}}）</text>
									<view class="small-tag" v-if="(item.staffLevel||0)>0">
										<img :src="authIcon" alt="">
										<text>{{levelList[item.staffLevel].text}}</text>
									</view>
									<img :src="item.employeeType==10?employeeTypeList[1].img:employeeTypeList[2].img"
										alt="" style="width: 80rpx;height: 80rpx;margin: 10rpx 10rpx;" v-else />
									<uni-icons type="calendar" style="margin: 10rpx 0 0 10rpx;" size="20"
										color="#1e1848" @click="openLog(index)" />
								</view>
								<view class="tab-info" @click="openTab(0,index)">
									<text>{{formatStr(0,2,item.hometown)}}人</text>
									<text v-if="item.sex!='隐藏'">|&nbsp;{{item.sex}}</text>
									<text v-if="item.age!=null&&item.age!=1">|&nbsp;{{item.age}}岁</text>
									<text>|&nbsp;{{item.workYear!==null&&item.workYear!=="0"?"工作"+item.workYear+"年" :"无经验"}}</text>
									<text v-if="item.education">|&nbsp;{{item.education}}</text>
									<text v-if="item.married">|&nbsp;{{item.married}}</text>
								</view>

								<view @click="openTab(0,index)">
									<view class="tab-text">
										<text>工号：<text class="c0">{{item.no||'-'}}</text></text>
									</view>
									<view class="tab-text">
										<text>入驻：<text class="c0">{{item.creatDate}}</text></text>
									</view>
									<view class="tab-text" v-if="item.storeName!=null">
										<text>门店：{{item.storeName}}</text>
									</view>
									<view class="tab-text" v-if="item.recruitChannel">
										<text>渠道：<text class="c0">{{item.recruitChannel||'-'}}</text></text>
									</view>
									<view class="tab-text" v-if="choiceIndex==1&&item.workState">
										<text>求职状态：<text class="c0">{{item.workState}}</text></text>
									</view>
									<view class="tab-text" v-if="item.introducerName!=null">
										<text>推荐人：<text
												class="c0">{{checkPeople(item.introducerNo,item.introducerName)}}</text></text>
									</view>

									<!-- 								<view class="tab-text" v-if="item.workingProperty!=0">
										<text>工作性质：<text
												class="c0">{{workingPropertyList[item.workingProperty].showText}}</text></text>
									</view> -->
									<view class="tab-text" v-if="item.salary&&item.salary!=0">
										<text>期望薪资：<text class="c0">{{item.salary}}</text></text>
									</view>
									<view class="tab-text" v-if="item.departName">
										<text>部门：<text class="c0">{{item.departName}}</text></text>
									</view>
									<view class="tab-text" v-if="item.areaName">
										<text>区域：<text class="c0">{{item.areaName}}</text></text>
									</view>


									<view class="tab-text">
										<text>流程：<text
												class="c0">{{processIdList[item.processId].showText}}</text></text>
									</view>

									<view class="tab-text" v-if="choiceIndex==1">
										<text>鉴定人：<text
												class="c0">{{checkPeople(item.updaterNo,item.updaterName)}}</text></text>
									</view>
								</view>
							</view>


							<view style="display: flex; flex-direction: row;width: 100%;height: 100rpx;" v-if="!canPut">
								<view style="width: 50%;height: 60rpx;" @click="openTab(1,index)">
									<view class="button-left">
										<text>鉴定</text>
									</view>
								</view>
								<view style="width: 50%;height: 60rpx;" @click="openTab(2,index)">
									<view class="button-right">
										<text>流程</text>
									</view>
								</view>
							</view>


						</view>
					</view>


				</uni-swipe-action-item>
			</uni-swipe-action>

			<view class="lh40 text-c f16" v-if="list.length">
				<view v-if="searchCondition.current>=pageCount">已显示全部内容</view>
				<view v-else @click="searchCondition.current++;pageStaff()">下滑查看更多...</view>
			</view>
			<u-empty v-if="!list.length" text="暂无内容" icon="http://cdn.uviewui.com/uview/empty/data.png" />
		</view>

		<!-- 全职员工 -->
		<view v-if="current == 1">
			<view class="lh20" style="padding: 20rpx 40rpx;background-color: #f4f4f5;">
				全职员工包括在全职工作的店长、合伙人、经纪人等门店员工，不面向客户提供服务
			</view>
			<view class="w10 bac1e1848" style="padding: 20rpx 0;">
				<u--input placeholder="请输入员工姓名/手机号进行搜索" prefixIcon="search" color="#eee"
					prefixIconStyle="font-size: 22px;color: #999" border="none" customStyle="width:90%;margin:auto"
					v-model="searchCondition1.search" @confirm="search">
					<template slot="suffix">
						<u-button text="搜索" type="info" @click="search" size="mini"></u-button>
					</template>
				</u--input>
			</view>
			<view class="tab f16 flac-col lh30" v-for="(item,index) of list1" :key="index">
				<view class="flac-row">
					<view class="f18 w65 flac-row"
						@click="choiceItemIndex=index;choiceStateIndex=item.state||1;popupShowInfo=isAuth?true:false">
						<text class="fb"> {{item.realName}}</text>
						<text class="f16" v-if="item.roleName">（{{item.roleName}}）</text>
						<view class="tag"
							:style="item.state==1?'color:#1e1848;border: #1e1848 2rpx solid;':'color:#909399;border: #909399 2rpx solid;'">
							{{stateList[item.state].text}}
						</view>
					</view>
					<view class="w35 tx-r" style="color: #1e1848;" @click="callPhone(item.id)">
						<uni-icons type="phone-filled" style="margin-right: 5rpx;" size="20" color="#1e1848">
						</uni-icons>
						<text>{{item.phone}}</text>
					</view>
				</view>
				<view class="flac-col" @click="openTab(0,index)">
					<view>
						<text class="fb">工号：</text>{{item.no||'-'}}
					</view>
					<view class="flac-row w65" v-if="roleId==66||roleId==1||roleId==95">
						<text class="fb">接收线索：</text>
						<u-switch class="w2" activeColor="#7ee257"  asyncChange  
						:activeValue="1" :inactiveValue="0" v-model="item.receiveStoreNeed"
						 @change="(value) => switchChange(value, item)"></u-switch>
					</view>
					<view v-if="isAuth">
						<text class="fb">备注：</text>{{item.remark||'暂无备注'}}
						<uni-icons type="compose" style="display: inline-block;margin-left: 5rpx;" size="18"
							color="#909399"
							@click="remark=item.remark||'';popupShowRemark = true;choiceItemIndex=index">
						</uni-icons>
					</view>
					<view style="color: #909399;">
						最后登录时间：{{item.lastLogin||'-'}}
					</view>
				</view>
			</view>
			<view class="lh40 text-c f16" v-if="list1.length">
				<view v-if="searchCondition1.current>=pageCount">已显示全部内容</view>
				<view v-else @click="searchCondition1.current++;pageEmployeeDto()">下滑查看更多...</view>
			</view>
			<u-empty v-if="!list1.length" text="暂无内容" icon="http://cdn.uviewui.com/uview/empty/data.png" />
		</view>

		<u-gap height="80"></u-gap>

		<view class="btn-fixed" @click="addEmployee">
			添加
			员工
		</view>
	</view>
</template>

<script>
	import {
		pathToBase64,
		base64ToPath
	} from '@/pages-mine/common/js/image-tools/index.js'
	export default {
		data() {
			return {
				// 可设置
				isAuth: true,
				// 请求地址
				baseUrl: 'http://localhost:8080/',
				// baseUrl: 'http://**************:9999/',

				// 可设置
				// 推荐人提醒-订阅消息模板
				templateId: "ZEuR4v5y6Bpt-G4Bxx5YHo5beBSGo1XTOSqvgpv1DsI",
				// 是否可以直接上架
				canPut: false,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},

				roleId: uni.getStorageSync('roleId') || 0,
				storeId: uni.getStorageSync('storeId') || 0,
				current: 0,
				current1: 0,
				choiceItemIndex: 0,
				choiceStateIndex: 1,
				choiceTypeIndex: 0,
				popupShow: false,
				popupShowRemark: false,
				popupShowAdd: false,
				popupShowAdd1: false,
				popupShowInfo: false,
				popupShowLog: false,
				remark: '',
				tabsList: [{
					name: '服务员工'
				}, {
					name: '全职员工'
				}],
				stateList: [{
						index: 0,
						text: '待完善',
						value: null
					}, {
						index: 1,
						text: '上架',
						value: 1
					},
					{
						index: 2,
						text: '下架',
						value: 2
					},
					{
						index: 3,
						text: '离职',
						value: 3
					},
					{
						index: 4,
						text: '删除',
						value: 4
					}
				],
				pageCount: 0,
				pageCount1: 0,
				total: 0,
				total1: 0,

				searchCondition: {
					search: '',
					isAppraisal: 0,
					isBaomu: 0,
					employeeType: 10,
					creTimeRange: 0,
					introducer: uni.getStorageSync("employeeNo") || 0,
					orderBy: "creatDate DESC,entryTime DESC",
					current: 1,
					size: 10
				},
				searchCondition1: {
					storeId: null,
					search: '',
					employeeType: 20,
					storeId: uni.getStorageSync('storeId') || 0,
					orderBy: "t.id ASC",
					current: 1,
					size: 10
				},
				list: [],
				list1: [],
				identityFront: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663226047582identity-front.png",
				identityBack: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663224975448identity-back.png",
				identityFrontOld: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663226047582identity-front.png",
				identityBackOld: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663224975448identity-back.png",
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/hr/default.jpg",
				authIcon: "https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/auth_icon.png",
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				employeeNew: {
					realName: '',
					phone: '',
					idCard: '',
					storeId: uni.getStorageSync("storeId") || 2,
					introducer: uni.getStorageSync("employeeNo") || '',
					operator: uni.getStorageSync("employeeName") || uni.getStorageSync("memberName") || "",
					idCardTime: null,
					birthTime: null,
					hometown: '',
					zodiac: '',
					nation: '',
					sex: '',
				},
				employeeNewOld: {
					realName: '',
					phone: '',
					idCard: '',
					storeId: uni.getStorageSync("storeId") || 2,
					introducer: uni.getStorageSync("employeeNo") || '',
					operator: uni.getStorageSync("employeeName") || uni.getStorageSync("memberName") || "",
					idCardTime: null,
					birthTime: null,
					hometown: '',
					zodiac: '',
					nation: '',
					sex: '',
				},
				idcard1: [{
					content: "",
					certificate: {
						title: "身份证正面",
						employeeId: null,
						certificateType: 1,
						certificateImg: null,
						validity: null
					}
				}],
				idcard2: [{
					content: "",
					certificate: {
						title: "身份证反面",
						employeeId: null,
						certificateType: 2,
						certificateImg: null,
						validity: null
					}
				}],
				typeList: [{
						value: 42,
						text: '经纪人',
					},
					{
						value: 77,
						text: '合伙人',
					},
				],

				// 标准员工部分
				storeName: "",
				pickerIndex: 0,
				choicePickerMineValue: 0,
				pickerMineName: '',
				searchPickerMineText: '',
				showPickerMine: false,
				pickerMineList: [],

				choiceIndex: 0,
				options: {
					option: [{
							text: '移除',
							style: {
								backgroundColor: '#1e1848',
							}
						},
						{
							text: '删除',
							style: {
								backgroundColor: '#909399',
							}
						},
						{
							text: '流转',
							style: {
								backgroundColor: '#19be6b',
							}
						}
					],
					option1: [{
							text: '下架',
							style: {
								backgroundColor: '#1e1848',
							}
						},
						{
							text: '删除',
							style: {
								backgroundColor: '#909399',
							}
						},
						{
							text: '流转',
							style: {
								backgroundColor: '#19be6b',
							}
						}
					],
				},
				menuList: [{
					index: 0,
					name: '全部',
					value: null
				}],
				storeList: [],
				choiceList: [{
					choiceTitle: "待鉴定",
					value: 2,
					show: true
				}, {
					choiceTitle: "已上架",
					value: 3,
					show: true
				}],
				workingPropertyList: [{
					index: 0,
					text: '',
					showText: "不限",
					value: null,
				}, {
					index: 1,
					text: '',
					showText: "全职",
					value: 1,
				}, {
					index: 2,
					text: '',
					showText: "兼职",
					value: 2,
				}, {
					index: 3,
					text: '',
					showText: "行政",
					value: 0,
				}],
				employeeTypeList: [{
					index: 0,
					text: '',
					showText: "不限",
					value: null,
					img: ""
				}, {
					index: 1,
					text: '',
					showText: "一线",
					value: 10,
					img: "https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/hr/frontline.jpg"
				}, {
					index: 2,
					text: '',
					showText: "行政",
					value: 20,
					img: "https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/hr/administration.jpg"
				}],
				processIdList: [{
					index: 0,
					text: '',
					showText: "不限",
					value: null
				}, {
					index: 1,
					text: '',
					showText: "资料",
					value: 1
				}, {
					index: 2,
					text: '',
					showText: "面试",
					value: 2
				}, {
					index: 3,
					text: '',
					showText: "培训",
					value: 3
				}, {
					index: 4,
					text: '',
					showText: "保险",
					value: 4
				}, {
					index: 5,
					text: '',
					showText: "跟单",
					value: 5
				}, {
					index: 6,
					text: '',
					showText: "考试",
					value: 6
				}, {
					index: 7,
					text: '',
					showText: "入职",
					value: 7
				}, {
					index: 8,
					text: '',
					showText: "上架",
					value: 8
				}],
				levelList: [{
					value: null,
					text: "不限",
				}, {
					value: 1,
					text: "一星",
				}, {
					value: 2,
					text: "二星",
				}, {
					value: 3,
					text: "三星",
				}, {
					value: 4,
					text: "四星",
				}, {
					value: 5,
					text: "五星",
				}],
				logList: [],
				trialStaff: {
					id: null,
					workRemark: ""
				},
			}
		},
		methods: {
			async switchChange(e,item) {
					uni.showModal({
						content: e ? '确定要打开吗' : '确定要关闭吗',
						success: (res) => {
							if (res.confirm) {
								this.http({
									outsideUrl: 'https://api.xiaoyujia.com/employee/updateEmployee',
									header: {
										'content-type': 'application/json;charset=UTF-8'
									},
									method: 'POST',
									hideLoading: true,
									data: {
										id: item.id,
										receiveStoreNeed: e?1:0
									},
									success: res => {
										if (res.code == 0) {
											this.$refs.uNotify.success('更新成功！')
											this.list1 = []
											this.searchCondition1.current = 1
											this.pageEmployeeDto()
								
										} else {
											this.$refs.uNotify.error('更新失败！' + res.msg)
										}
									}
								});
							}
						}
					})
			},
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			changePage(e) {
				this.current = e.index
				this.search()
			},
			// 选择菜单（一级）
			choiceMenu(e) {
				this.current1 = e.index
				this.search()
			},
			// 选择菜单（二级）
			choiceTab(index) {
				this.choiceIndex = index
				this.search()
			},
			// 字符串截取
			formatStr(index, index1, str) {
				if (str == null) {
					return
				}
				let result = str.substring(index, index1)
				return result
			},
			// 格式化时间
			formatDate(value) {
				if (value == null) {
					return "暂无时间"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '.' + MM + '.' + d
			},
			checkPeople(no, name) {
				if (no == null || name == null) {
					return "暂无"
				} else {
					return name + "（" + no + "）"
				}
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			openTab(value, index) {
				if (this.current == 0) {
					this.choiceItemIndex = index
					let id = this.list[index].trialId
					if (value == 0) {
						uni.navigateTo({
							url: '/pages-other/hr/staff-resume?id=' + id + "&isAdmin=true"
						})
					} else if (value == 1) {
						uni.navigateTo({
							url: '/pages-other/hr/staff-appraisal?id=' + id
						})
					} else if (value == 2) {
						uni.navigateTo({
							url: '/pages-other/hr/staff-process?id=' + id + "&isAdmin=true"
						})
					} else if (value == 3) {
						uni.navigateTo({
							url: '/pages-other/hr/staff-grounding?id=' + id
						})
					}
				} else if (this.current == 1) {

				}
			},
			// 打开日志
			openLog(index) {
				this.logList = []
				this.popupShowLog = true
				let id = this.list[index].trialId
				this.http({
					url: 'listTrialStaffLog',
					method: 'POST',
					hideLoading: true,
					data: {
						trialId: id
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.logList = res.data
						} else {
							this.logList = []
						}
					}
				})
			},
			// 重置
			reset() {
				if (this.current == 0) {
					this.searchCondition.search = ''
					this.searchCondition.storeId = null
					this.storeName = ''
				} else {
					this.searchCondition1.search = ''
				}
			},
			search() {
				if (this.current == 0) {
					this.list = []
					this.searchCondition.current = 1
					this.searchCondition.post = this.menuList[this.current1].value
					this.searchCondition.isAppraisal = this.choiceIndex == 0 ? 0 : 1
					this.pageStaff()
				} else {
					this.list1 = []
					this.searchCondition1.current = 1
					this.pageEmployeeDto()
				}
			},
			// 添加员工
			addEmployee() {
				if (this.current == 0) {
					this.employeeNew = this.employeeNewOld
					this.popupShowAdd = true
				} else if (this.current == 1) {
					if (!this.isAuth) {
						return this.$refs.uNotify.warning('暂无权限添加员工！')
					}
					this.getStoreNewStaff()
				}
			},
			// 打电话
			callPhone(id) {
				this.http({
					url: 'callPrivatePhone',
					data: {
						employeeId: id
					},
					method: 'GET',
					hideLoading: true,
					success: (res) => {
						if (res.code == 0) {
							let that = this
							uni.makePhoneCall({
								phoneNumber: res.data,
								success: res => {

								},
								fail: res => {}
							})
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}
				})
			},
			// 更新员工
			updateEmployee(value) {
				if (!this.isAuth) {
					return this.$refs.uNotify.warning('暂无权限！')
				}
				if (value == 0 && !this.remark) {
					return this.$refs.uNotify.warning('请填写备注！')
				}
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/employee/updateEmployee',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: {
						id: this.list1[this.choiceItemIndex].id,
						realName: this.list1[this.choiceItemIndex].realName,
						remark: this.remark,
						state: value == 1 ? this.choiceStateIndex : null,
					},
					success: res => {
						if (res.code == 0) {
							this.popupShowRemark = false
							this.popupShowInfo = false
							this.$refs.uNotify.success('更新成功！')
							if (value == 0) {
								this.list1[this.choiceItemIndex].remark = this.remark
								this.remark = ''
							} else if (value == 1) {
								this.list1[this.choiceItemIndex].state = this.choiceStateIndex
							}

						} else {
							this.$refs.uNotify.error('更新失败！' + res.msg)
						}
					}
				});
			},
			uploadImg(value) {
				let url = "https://api.xiaoyujia.com/system/imageUpload"
				uni.chooseImage({
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						// 将图片上传至后台
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {
								route: 'idCard'
							},
							dataType: 'json',
							success: res => {
								let result = JSON.parse(res.data)
								if (value == 0) {
									this.idcard1[0].certificate.certificateImg = result.data
								} else if (value == 1) {
									this.idcard2[0].certificate.certificateImg = result.data
								}
							}
						})
						// #ifdef  H5
						pathToBase64(tempFilePaths)
							.then(base64 => {
								if (value == 0) {
									this.identityFront = tempFilePaths[0]
									this.idcard1[0].content = base64
									this.vidcardNext()
								} else if (value == 1) {
									this.identityBack = tempFilePaths[0]
									this.idcard2[0].content = base64
									this.vidcard1Next()
								}
							})
							.catch(error => {
								console.error("转化失败！")
							})
						// #endif
						// #ifdef  MP-WEIXIN
						const base64 = this.urlTobase64(tempFilePaths[0])
						if (value == 0) {
							this.identityFront = tempFilePaths[0]
							this.idcard1[0].content = base64
							this.vidcardNext()
						} else if (value == 1) {
							this.identityBack = tempFilePaths[0]
							this.idcard2[0].content = base64
							this.vidcard1Next()
						}
						// #endif
					}
				});
			},
			urlTobase64(url) {
				const imgData = uni.getFileSystemManager().readFileSync(url, 'base64')
				const base64 = 'data:image/jpeg;base64,' + imgData
				return base64
			},
			// 识别身份证正面
			vidcardNext() {
				let url = "https://api.xiaoyujia.com/acn/imgsToIdInfo"
				console.log("读取图片后进行请求！")
				// 请求：识别身份证
				let data = {
					file: this.idcard1[0].content,
					cardTpe: 0
				}
				uni.request({
					url: url,
					method: 'POST',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: JSON.stringify(data),
					success: res => {
						let status = res.data.image_status
						if (status === 'normal') {
							this.$refs.uNotify.success("身份证正面上传成功！")
							this.employeeNew.realName = res.data.words_result.姓名.words
							this.employeeNew.idCard = res.data.words_result.公民身份号码.words
							this.employeeNew.birthTime = this.getMyTime(res.data.words_result.出生.words)
							this.employeeNew.hometown = res.data.words_result.住址.words
							this.employeeNew.zodiac = this.getshengxiao(res.data.words_result.出生.words
								.substring(
									0, 4))
							this.employeeNew.nation = res.data.words_result.民族.words
							this.employeeNew.sex = this.getSex(res.data.words_result.性别.words)
						} else if (status === 'unknown' && res.data.idcard_number_type === 0) {
							this.employeeNew.realName = res.data.words_result.姓名.words
							this.employeeNew.idCard = res.data.words_result.公民身份号码.words
							this.employeeNew.birthTime = this.getMyTime(res.data.words_result.出生.words)
							this.employeeNew.hometown = res.data.words_result.住址.words
							this.employeeNew.zodiac = this.getshengxiao(res.data.words_result.出生.words
								.substring(
									0, 4))
							this.employeeNew.nation = res.data.words_result.民族.words
							this.employeeNew.sex = this.getSex(res.data.words_result.性别.words)
							this.$refs.uNotify.error("因第三方接口不识别，请自行输入身份证号码！")
						} else {
							let msg = '身份证认证失败，请重新上传';
							if (status != null) {
								if (status === 'reversed_side') {
									msg = '身份证应上传照片面'
								}
								if (status === 'non_idcard') {
									msg = '上传的图片中不包含身份证'
								}
								if (status === 'blurred') {
									msg = '身份证模糊'
								}
								if (status === 'other_type_card') {
									msg = '其他类型证照'
								}
								if (status === 'over_exposure') {
									msg = '身份证关键字段反光或过曝'
								}
								if (status === 'over_dark') {
									msg = '身份证欠曝（亮度过低）'
								}
							}
							this.idcard1[0].content = ""
							this.$refs.uNotify.error(msg)
						}
					},
					fail: err => {
						console.log('兑换奖品-请求失败！' + res.data.code)
					}
				})
			},
			// 识别身份证背面
			vidcard1Next() {
				let url = "https://api.xiaoyujia.com/acn/imgsToIdInfo"
				// 请求：识别身份证
				let data = {
					file: this.idcard2[0].content,
					cardTpe: 1
				}
				uni.request({
					url: url,
					method: 'POST',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: JSON.stringify(data),
					success: res => {
						let status = res.data.image_status;
						if (status === 'normal') {
							this.$refs.uNotify.success("身份证背面上传成功！")
							this.employeeNew.idCardTime = this.getMyTime(res.data.words_result.失效日期.words)
						} else {
							this.idcard2[0].content = ""
							this.$refs.uNotify.error("身份证背面认证失败，请检查有效期限后请重新上传")
						}
					}
				})
			},
			// 更新证件图片
			addCertificate(value, employeeId) {
				let idCardTime = this.employeeNew.idCardTime
				if (employeeId == 0) {
					return
				}

				this.$set(this.idcard1[0].certificate, 'employeeId', employeeId)
				this.$set(this.idcard2[0].certificate, 'employeeId', employeeId)
				this.idcard1[0].certificate.validity = this.getMyTime2(idCardTime)
				this.idcard2[0].certificate.validity = this.getMyTime2(idCardTime)
				let data = value == 0 ? this.idcard1[0].certificate : this.idcard2[0].certificate
				if (!data.certificateImg) {
					return
				}
				this.http({
					url: 'addCertificate',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: data,
					success: res => {
						if (res.code == 0) {
							if (value == 0) {
								this.idcard1[0].certificate.certificateImg = ""
							} else {
								this.idcard2[0].certificate.certificateImg = ""
							}
						}
					}
				})
			},
			getshengxiao(yyyy) {
				var arr = ['猴', '鸡', '狗', '猪', '鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊']
				return /^\d{4}$/.test(yyyy) ? arr[yyyy % 12] : null
			},
			getMyTime(time) {
				let dateTime = time.substring(0, 4) + "-" + time.substring(4, 6) + "-" + time.substring(6, 8)
				if (time.includes("长期")) {
					dateTime = "长期有效"
				}
				return dateTime
			},
			getMyTime2(time) {
				if (!time) {
					return ''
				}
				if (time.includes("长期")) {
					return "3000/01/01"
				} else {
					let dateTime = time.substring(0, 4) + "/" + time.substring(5, 7) + "/" + time.substring(8, 10)
					return dateTime
				}
			},
			getSex(sex) {
				let result = 2
				if (sex == "男") {
					result = 1
				} else if (sex == "女") {
					result = 2
				}
				return result
			},

			// 标准员工相关
			// 打开选择器
			openPickerMine(value) {
				if (value == 0) {
					this.pickerMineName = "storeName"
					this.pickerMineList = this.storeList
				} else if (value == 1) {

				}

				this.pickerMineList.forEach(item => {
					this.$set(item, 'show', true)
				})
				this.searchPickerMine(this.searchPickerMineText)
				this.choicePickerMineValue = value
				this.popupShow = false
				this.showPickerMine = true
			},
			// 选择器选择
			changePickerMine(e) {
				let value = this.choicePickerMineValue
				let index = e
				if (value == 0) {
					this.searchCondition.storeId = this.storeList[index].id
					this.storeName = this.storeList[index].storeName
				}
				this.showPickerMine = false
				this.popupShow = true
			},
			// 选择器搜索
			searchPickerMine(e) {
				if (e) {
					this.pickerMineList.forEach(item => {
						if (item[this.pickerMineName].includes(e)) {
							this.$set(item, 'show', true)
						} else {
							this.$set(item, 'show', false)
						}
					})
				} else {
					this.pickerMineList.forEach(item => {
						this.$set(item, 'show', true)
					})
				}
			},
			// 催审
			urgeListing(index) {
				uni.showModal({
					title: '员工上架催审',
					content: '是否催审？将会给门店鉴定师发送消息！',
					success: res => {
						if (res.confirm) {
							let item = this.list[index]
							this.http({
								outsideUrl: "https://api.xiaoyujia.com/acn/urgeListing",
								method: 'POST',
								header: {
									'content-type': "application/json;charset=UTF-8"
								},
								data: {
									employeeId: item.employeeId,
									trialId: item.id,
									type: 1,
									storeId: item.storeId,
									operator: uni.getStorageSync('employeeName') || uni.getStorageSync(
										'memberName') || (
										'会员：' +
										uni.getStorageSync('account')),
								},
								success: res => {
									if (res.code == 0) {
										this.$refs.uNotify.success('催审成功！请等待鉴定师操作！')
										this.$set(this.list[index], 'hideUrge', 1)
									} else {
										this.$refs.uNotify.error(res.msg)
									}
								}
							})
						}
					}
				});
			},
			// 点击选项
			clickOption(e) {
				let choiceOption = e.index
				// 判断点击的按钮是什么
				if (this.choiceIndex == 0) {
					if (choiceOption == 0) {
						this.$refs.uNotify.error("移除后在鉴定列表将不可见！")
						this.openCheckDetail(5, "移除员工", "确定从鉴定列表中移除吗？")
					} else if (choiceOption == 1) {
						this.$refs.uNotify.error("删除后员工将无法鉴定和上架，请谨慎操作！")
						this.openCheckDetail(4, "删除员工", "确定删除该员工吗？")
					} else if (choiceOption == 2) {
						this.openCheck(8, "流转员工", "流转的员工必须是保姆/月嫂/育儿嫂等工种，流转后可在【保姆】列表查看，确定流转该员工吗？")
					}
				} else if (this.choiceIndex == 1) {
					if (choiceOption == 0) {
						this.openCheckDetail(3, "下架员工", "确定下架该员工吗？")
					} else if (choiceOption == 1) {
						this.openCheckDetail(4, "删除员工", "确定删除该员工吗？")
					} else if (choiceOption == 2) {
						this.openCheck(8, "流转员工", "流转的员工必须是保姆/月嫂等工种，确定流转该员工吗？")
					}
				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 打开确认框
			openCheckDetail(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheckDetail.open()
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：xxx确认
				if (this.checkType == 0) {
					uni.navigateTo({
						url: "/pages-mine/resume/resume?baomuId=" + this.list[this.choiceItemIndex].baomuId
					})
				} else if (this.checkType == 1) {
					uni.navigateTo({
						url: "/pages-other/employee/workSkill?baomuId=" + this.list[this.choiceItemIndex].id
					})
				} else if (this.checkType == 2) {
					// 上架员工
					// this.employee = this.list[this.choiceItemIndex]
					// this.employee.state = 1
					// this.employee.putTime = Number(new Date())
					// this.updateBaomuState()
				} else if (this.checkType == 3) {
					let workRemark = this.trialStaff.workRemark
					if (workRemark == "") {
						this.$refs.uNotify.error("请先补充下架原因！")
						return
					}
					// 下架员工
					this.trialStaff.state = 2
					this.trialStaff.workRemark = "下架原因：" + workRemark + "，操作人：" + uni.getStorageSync("employeeName")
					this.updateStaffState()
				} else if (this.checkType == 4) {
					let workRemark = this.trialStaff.workRemark
					if (workRemark == "") {
						this.$refs.uNotify.error("请先补充删除原因！")
						return
					}
					// 删除员工
					this.trialStaff.state = 4
					this.trialStaff.workRemark = "删除原因：" + workRemark + "，操作人：" + uni.getStorageSync("employeeName")
					this.updateStaffState()
				} else if (this.checkType == 5) {
					let workRemark = this.trialStaff.workRemark
					if (workRemark == "") {
						this.$refs.uNotify.error("请先补充移除原因！")
						return
					}
					// 移除员工
					this.trialStaff.staffLevel = -1
					this.trialStaff.workRemark = "移除原因：" + workRemark + "，操作人：" + uni.getStorageSync("employeeName")
					this.updateTrialStaff()
				} else if (this.checkType == 6) {

				} else if (this.checkType == 7) {

				} else if (this.checkType == 8) {
					this.staffToEmployee()
				}
			},
			// 员工流转
			staffToEmployee() {
				let data = {
					trialId: this.list[this.choiceItemIndex].trialId,
					employeeId: this.list[this.choiceItemIndex].employeeId || 0,
					operatorId: uni.getStorageSync("employeeId") || 0,
					operatorName: uni.getStorageSync("employeeName") || ""
				}
				this.http({
					url: 'staffToEmployee',
					method: 'POST',
					hideLoading: true,
					data: data,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							this.$delete(this.list, this.choiceItemIndex)
							this.$refs.uNotify.success("员工流转成功！请至【保姆】栏目查看！")
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				});
			},
			// 更新员工状态
			updateStaffState() {
				this.$set(this.trialStaff, "trialId", this.list[this.choiceItemIndex].trialId)
				this.$set(this.trialStaff, "operatorId", uni.getStorageSync("employeeId") || 0)
				this.http({
					url: 'updateStaffState',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: this.trialStaff,
					success: res => {
						if (res.code == 0) {
							this.$delete(this.list, this.choiceItemIndex)
							this.$refs.uNotify.success("已更新员工状态！")
						}
					}
				});
			},
			// 获取工种字典内容
			getWorkType() {
				this.http({
					url: "getWorkType",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							// 格式化一级菜单
							let workTypeList = res.data
							for (let i = 0; i < workTypeList.length; i++) {
								let str = workTypeList[i].typeName.substring(0, 2)
								let data = {
									index: i + 1,
									name: str,
									value: str
								}
								this.menuList.push(data)
							}
						}
					}
				})
			},
			// 获取门店列表
			getStoreList() {
				this.http({
					url: 'getAllStoreList',
					data: {},
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.storeList = res.data
						}
					}
				});
			},

			// 校验员工
			checkEmployeeByIdCard() {
				if (!this.employeeNew.idCard) {
					this.createNewEmployee()
				} else {
					this.http({
						url: 'checkEmployeeByIdCard',
						method: 'POST',
						data: {
							idcard: this.employeeNew.idCard
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								this.popupShowNew = false
								this.$refs.uNotify.warning("该身份证已录入过系统！无法重复添加员工！")
							} else {
								this.createNewEmployee()
							}
						},
					})
				}
			},
			checkEmployee() {
				const phoneReg = /^1[3456789]\d{9}$/
				const idCardReg =
					/(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)/
				if (!this.employeeNew.realName) {
					this.$refs.uNotify.error("请输入员工姓名！")
					return false
				}
				if (!phoneReg.test(this.employeeNew.phone || '')) {
					this.$refs.uNotify.error("请输入正确手机号！")
					return false
				}
				if (!idCardReg.test(this.employeeNew.idCard || '')) {
					this.$refs.uNotify.error("请输入正确的身份证号！")
					return false
				}
				return true
			},
			// 创建新员工
			createNewEmployee() {
				if (!this.checkEmployee()) {
					return
				} else {
					let idCardTime = this.employeeNew.idCardTime
					if ((idCardTime || '').includes("长期")) {
						this.employeeNew.idCardTime = "3000-01-01" + " 00:00:00.000"
					} else if (idCardTime && idCardTime.length < 12) {
						this.employeeNew.idCardTime = idCardTime + " 00:00:00.000"
					}

					this.$set(this.employeeNew, 'roleId', null)
					this.http({
						url: 'createNewEmployee',
						data: this.employeeNew,
						method: 'POST',
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code == 0) {
								this.$refs.uNotify.success("添加成功！")
								let employeeId = res.data || 0
								this.addCertificate(0, employeeId)
								this.addCertificate(1, employeeId)
								let timer = setTimeout(() => {
									this.employeeNew = this.employeeNewOld
									this.identityFront = this.identityFrontOld
									this.identityBack = this.identityBackOld
								}, 600)

								// 流转
								let id = res.data || null
								this.http({
									url: 'employeeToStaff',
									method: 'POST',
									hideLoading: true,
									data: {
										id: id,
										realName: "邀请入驻->流转"
									},
									header: {
										'content-type': 'application/json;charset=UTF-8'
									},
									success: res => {}
								});
							} else {
								this.$refs.uNotify.error(res.msg)
							}
						}
					});
				}
			},
			// 获取门店新全职员工
			getStoreNewStaff() {
				this.http({
					outsideUrl: this.baseUrl + "acn/getStoreNewStaff",
					method: 'GET',
					hideLoading: true,
					path: this.storeId,
					success: res => {
						if (res.code == 0) {
							this.employeeNew = res.data
							this.popupShowAdd1 = true
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			// 创建全职员工
			createStoreNewStaff() {
				this.$set(this.employeeNew, 'idCardString', this.employeeNew.idCard)
				if (!this.checkEmployee()) {
					return
				}
				if (!this.employeeNew.no) {
					return this.$refs.uNotify.warning("添加失败！工号未生成！")
				}
				this.$set(this.employeeNew, 'roleId', this.typeList[this.choiceTypeIndex].value)
				this.http({
					outsideUrl: this.baseUrl + "acn/createStoreNewStaff",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: this.employeeNew,
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("添加成功！")
							this.popupShowAdd1 = false
							this.search()
							let timer = setTimeout(() => {
								this.employeeNew = this.employeeNewOld
							}, 600)
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})

			},
			// 获取一线员工列表
			pageStaff() {
				this.http({
					url: 'getTrialStaffPage',
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: this.searchCondition,
					success: res => {
						if (res.code == 0) {
							this.list = this.list.concat(res.data.records)
							this.total = res.data.total
							this.pageCount = res.data.pages
							this.tabsList[0].name = '服务员工' + '（' + this.total + '）'
						} else if (this.searchCondition.current > 1) {
							this.$refs.uNotify.warning('暂无更多内容了哦')
						}
						this.popupShow = false
					}
				})
			},
			// 获取支持员工列表
			pageEmployeeDto() {
				this.http({
					outsideUrl: this.baseUrl + "acn/pageEmployeeDto",
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: this.searchCondition1,
					success: res => {
						if (res.code == 0) {
							this.list1 = this.list1.concat(res.data.records)
							this.pageCount1 = res.data.pages
							this.total1 = res.data.total
							this.tabsList[1].name = '全职员工' + '（' + this.total1 + '）'
						} else if (this.searchCondition1.current > 1) {
							this.$refs.uNotify.warning('暂无更多内容了哦')
						}
					}
				})
			},
		},
		onReachBottom() {
			if (this.current == 0) {
				this.searchCondition.current++
				this.pageStaff()
			} else {
				this.searchCondition1.current++
				this.pageEmployeeDto()
			}
		},
		onLoad(option) {
			if (option.index) {
				this.current = option.index
			}
			if (this.roleId != 95 && this.roleId != 1) {
				this.isAuth = false
			}
			this.search()
			this.getWorkType()
			this.getStoreList()
		},
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";

	.tab {
		padding: 20rpx 40rpx;
		height: auto;
		border-bottom: #f4f4f5 4rpx solid;
	}

	.tag {
		font-size: 28rpx;
		font-weight: bold;
		border: #1e1848 2rpx solid;
		width: 60rpx;
		height: 40rpx;
		line-height: 40rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	// 筛选组件按钮
	.filter-button {
		position: fixed;
		z-index: 999;
		bottom: 0rpx;
		width: 100%;
		height: 120rpx;
		background-color: #ffffff;
		box-shadow: 0 4rpx 20rpx #dedede;
		padding: 0;
		display: flex;
		flex-direction: row;
	}

	.filter-button-left,
	.filter-button-right {
		width: 86%;
		height: 80rpx;
		// background-color: red;
		border-radius: 40rpx;
		margin: 20rpx 7%;
		text-align: center;
		line-height: 80rpx;

		text {
			height: 80rpx;
			font-size: 36rpx;
		}

		button {
			height: 80rpx;
			font-size: 36rpx;
		}
	}

	.filter-button-left {
		border: #dedede 1px solid;
		// color: #909399;
		background-color: #f9f9f9;
	}

	.filter-button-right {
		color: #fff;
		background-color: #1e1848;
	}

	.tab-inputbox-high {
		display: block;
		width: 90%;
		margin: 30rpx 40rpx;
	}

	// 多行输入框
	.multiline-input {
		padding: 20rpx 20rpx;
		width: 100%;
		height: 300rpx;
		line-height: 100rpx;
		border-radius: 20rpx;
		color: #000000;
		font-size: 36rpx;
	}

	.btn-fixed {
		font-size: 26rpx;
		text-align: center;
		position: fixed;
		right: 30rpx;
		bottom: 18%;
		color: #fdd472;
		line-height: 30rpx;
		background-color: #1e1848;
		border-radius: 50%;
		width: 80rpx;
		height: 60rpx;
		padding: 20rpx 10rpx;
	}

	/deep/ .u-transition,
	.u-back-top {
		z-index: 999 !important;
	}

	// 保姆列表栏目
	.baomu-tab {
		width: 100%;
		height: auto;
		box-shadow: 0 4rpx 20rpx #dedede;
		padding: 40rpx 0;
		display: flex;
		flex-direction: row;
	}

	// 栏目左边
	.tab-left {
		float: left;
		width: 25%;
		height: 100%;
	}

	.tab-left-img {
		img:nth-child(1) {
			display: block;
			width: 170rpx;
			height: 240rpx;
			border-radius: 15rpx;
			margin: 20rpx 0 0 12%;
		}

		img:nth-child(2) {
			display: block;
			width: 170rpx;
			height: 60rpx;
			margin: -60rpx auto 0rpx 22rpx;
		}
	}

	.tab-left-btn {
		padding: 20rpx 10rpx;
		margin: 0 15%;
		width: 70%;
	}

	// 栏目右边
	.tab-right {
		float: right;
		display: flex;
		flex-direction: column;
		width: 69%;
		height: auto;
		padding: 0 3%;
	}

	.tab-name {
		width: 100%;
		height: 80rpx;
		font-size: 36rpx;
		font-weight: bold;
		line-height: 80rpx;
	}

	.tab-info {
		text {
			font-size: 36rpx;
			line-height: 60rpx;
			padding: 0 5rpx;
		}
	}

	.tab-text {
		margin-left: 5rpx;
		height: auto;
		width: 90%;
		color: #909399;

		text {
			font-size: 32rpx;
			line-height: 60rpx;
		}
	}

	.skills-content {
		width: 100%;
		height: auto;
	}

	.tab-inputbox {
		display: block;
		margin: 20rpx auto;
		width: 95%;
		height: 100rpx;
		background-color: #f9f9f9;
		border-radius: 20rpx;
		box-shadow: 2rpx 2rpx 10rpx #dedede;
	}

	// 弹窗输入区域
	.popupCheck-inputbox {
		display: flex;
		flex-direction: column;
		width: 100%;
		line-height: 60rpx;

		text {
			display: block;
			margin: -10rpx;
			text-align: center;
			font-size: 32rpx;
		}
	}

	// 单行输入框
	.single-input {
		display: block;
		float: left;
		width: 80%;
		height: 80rpx;
		line-height: 80rpx;
		padding-left: 30rpx;
		font-size: 32rpx;
		text-align: left;
		margin: 10rpx 0 0 20rpx;
		border-style: hidden;
	}

	.my-tag {
		float: left;
		padding: 0;
		width: auto;
		height: auto;
		line-height: 50rpx;
		font-size: 32rpx;
		border-radius: 10rpx;
		color: #909399;
		background-color: #f4f4f5;
		margin: 0 20rpx 10rpx 0rpx;
		padding: 0 10rpx;
	}

	.button-left,
	.button-right {
		width: 70%;
		height: 60rpx;
		border-radius: 40rpx;
		margin: 20rpx 7%;
		text-align: center;
		color: #f6cc70;
		background-color: #1e1848;

		text {
			height: 60rpx;
			line-height: 60rpx;
			font-size: 32rpx;
		}
	}

	.img-delete {
		position: absolute;
		margin: -230rpx 0 0 140rpx;
	}

	.log-list {
		width: 100%;
		height: auto;
		padding: 20rpx 40rpx;
		font-size: 36rpx;
		line-height: 60rpx;

		text {
			display: block;
		}
	}

	.small-tag {
		display: flex;
		width: 120rpx;
		height: 45rpx;
		line-height: 45rpx;
		color: #fff;
		background-color: #f6cc70;
		border-radius: 10rpx;
		margin: 30rpx 0;

		img {
			display: block;
			width: 30rpx;
			height: 30rpx;
			margin: 7rpx 0 0 10rpx;
		}

		text {
			display: block;
			width: 60%;
			text-align: center;
		}
	}
</style>