 	/*
		找工作栏目--通用样式
	*/
   
    .tab {
		overflow: auto;
		height: auto !important;
		padding-bottom: 20rpx;
    }
	
	// 栏目头部
	.tab-head,
	.tab-title {
		width: 100%;
		height: 60rpx;
		margin: 0 0 10rpx 40rpx;
		padding-left: 0rpx !important;

		text {
			display: bolck;
			width: 100%;
			height: 60rpx;
			font-size: 40rpx;
			font-weight: bold;
			line-height: 60rpx;
		}
	}

	.tab-title {
		text {
			font-size: 36rpx;
		}
	}
	
	// 栏目内容
	.tab-content {
		display: block;
		width: 92%;
		height: auto;
		padding: 0 40rpx;
	}
	
	/*
		栏目单行对象
	*/
   
	.tab-item {
		width: 100%;
		height: auto;
		line-height: 60rpx;
		display: flex;
	}

	// 对象标题
	.item-title,
	.item-title-short {
		width: auto;
		height: 60rpx;
		line-height: 60rpx;

		text {
			font-size: 32rpx;
			color: #909399;
		}
	}

	.item-title-short {
		width: 20%;
	}

	// 对象文本
	.item-text {
		width: 70%;
		height: auto;

		text {
			font-size: 32rpx;
		}
	}

	// 对象价格文本
	.item-price {
		margin-left: 20rpx;
		color: #f9ae3d;
		font-weight: bold;
		font-size: 36rpx;
	}

	// 自定义标签
	.my-tag {
		float: left;
		padding: 0;
		width: auto;
		height: 50rpx;
		line-height: 50rpx;
		font-size: 32rpx;
		border-radius: 10rpx;
		color: #909399;
		background-color: #f4f4f5;
		margin: 0 20rpx 10rpx 0rpx;
		padding: 0 10rpx;
	}
	
	// 气泡弹框
	.tips-box-fixed {
		position: fixed;
		/* #ifdef H5 || APP-PLUS */
		top: 5.4%;
		/* #endif */
		/* #ifdef MP-WEIXIN */
		top: 25rpx;
		/* #endif */
		z-index: 999;
		right: 14%;
	
		// margin: 10rpx 13% -10rpx 13%;
		width: 72%;
		height: 60rpx;
		line-height: 60rpx;
		text-align: center;
		color: #fff;
		font-size: 28rpx;
		background-color: rgba(30, 24, 72, 0.8);
		border-radius: 30rpx;
	}
	
	
