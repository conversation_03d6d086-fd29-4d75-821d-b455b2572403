<template>
	<view class="w9 mg-at" style="padding-bottom: 100rpx;" v-if="showMain">
		<u-sticky bgColor="#fff">
			<view class="f22 fb lh60 flac-row-b">
				{{title}}
				<u-icon name="list" size="30" @click="hiddenMask = true"></u-icon>
			</view>
			<uni-breadcrumb>
				<uni-breadcrumb-item v-for="(route,i) in routes" :key="i" :to="route.to">
					{{route.name}}
				</uni-breadcrumb-item>
			</uni-breadcrumb>
		</u-sticky>

		<!-- 目录 -->
		<view class="mask" v-show="hiddenMask">
			<view class="mask-wrap bacf">
				<view class="f20 fb lh50 flac-row-b">
					目录
					<u-icon name="close-circle" size="25" @click="hiddenMask = false"></u-icon>
				</view>
				<view class="lh35" v-for="(item,i) in catalogList" :key="i" @click="GoTitle(i)">
					<u-collapse :border="false" accordion>
						<u-collapse-item :title="item.bigTitle" :label="item.date" icon="tags-fill">
							<view class="f16 lh40 t-indent2" v-for="(item2,i2) in item.textList" :key="i2">{{item2}}
							</view>
						</u-collapse-item>
					</u-collapse>
				</view>
			</view>
		</view>

		<view v-if="list.length!=0" v-html="htmlContent" class="w95 mg-at"></view>
	</view>
</template>
<script>
	export default {
		comments: {},
		data() {
			return {
				showMain: false,
				flag: '',
				title: '',
				choiceIndex: 0,
				htmlContent: "",
				hiddenMask: false,
				routes: [{
						to: "/pages/index/index",
						name: "首页",
					},
					{
						to: "",
						name: "授权登录",
					},
					{
						to: "",
						name: "首次登录",
					}
				],
				catalogList: [{
					bigTitle: '家姐联盟更新日志',
					date: '',
					textList: ['微信授权登录', 'xxxx']
				}],
				list: [],
				// 日志内容测试
				list1: [{
						"id": 270,
						"courseId": 11,
						"courseNum": 1,
						"title": "1.0.49版本更新介绍",
						"courseImgUrl": null,
						"videoUrl": null,
						"courseContent": "|h1一、微信客服持续升级\n|icon-https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1675662002751video.png|h2接入场景|鉴定列表提醒按钮可显示提醒状态，重新提醒时弹窗进行提示|https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1675416054495企业微信截图_16754160043372.png|h2这里是介绍视频|这里是介绍文字||http://vi.xiaoyujia.com/sv/621400a2-183453f60e9/621400a2-183453f60e9.mp4",
						"createTime": "2023-02-03 17:09:42",
						"courseDuration": null,
						"courseRemark": "版本更新总体简介\n",
						"courseTitle": "1.0.49版本更新介绍",
						"courseKeyWords": "更新日志测试",
						"search": null,
						"orderBy": null,
						"current": null,
						"size": null
					},
					{
						"id": 271,
						"courseId": 11,
						"courseNum": 1,
						"title": "1.0.50版本更新介绍",
						"courseImgUrl": null,
						"videoUrl": null,
						"courseContent": "|h1一、微信客服持续升级二\n|icon-https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1675662002751video.png|h2接入场景|xxxxx这里是介绍文本|https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1675673693641企业微信截图_16756736812101.png",
						"createTime": "2023-02-06 17:09:42",
						"courseDuration": null,
						"courseRemark": "版本更新总体简介\n",
						"courseTitle": "1.0.50版本更新介绍",
						"courseKeyWords": "更新日志测试",
						"search": null,
						"orderBy": null,
						"current": null,
						"size": null
					}
				],
			}
		},
		methods: {
			// 切换版本
			changeVersion(index) {
				this.choiceIndex = index
				this.hiddenMask = false
			},
			// 获取日志列表
			getContentList() {
				let data = {
					id: 11,
					search: "",
					courseIdList: null,
					orderBy: "c.courseId,c.courseNum,c.CreateTime ASC",
					current: 1,
					size: 10
				}
				this.http({
					method: 'POST',
					url: "getCourseById",
					data: data,
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					dataType: "json",
					success: res => {
						if (res.code == 0) {
							this.list = res.data
							this.checkVersion()
							this.formatCatalogList()
							// this.formatCourseContent()
						}
					}
				})
			},
			// 格式化日志内容
			formatCourseContent() {
				let title = this.list[this.choiceIndex].title
				let content = this.list[this.choiceIndex].courseContent

				if (content == null || content == "") {
					result = "暂无详情"
					return result
				}
				// 大标题-样式
				let titleBegin =
					"<span style='font-size: 22px;line-height: 50px; display: block;'>"
				let titleEnd = "</span>"

				// 一级标题-样式
				let titleBegin1 =
					"<span style='font-size: 20px;line-height: 40px; display: block;'>"
				let titleEnd1 = "</span>"

				//  二级标题-样式
				let titleBegin2 = "<span style='font-size: 18px;line-height: 50px;'>"
				let titleEnd2 = "</span>"

				//正文-样式
				let textBegin =
					"<span style='font-size: 16px;line-height: 25px;display: block; text-indent: 2rem;'>"
				let textEnd = "</span>"

				// icon图标-样式
				let iconBegin = "<img alt='' style='width:25px; height: 25px; margin: 10px 5px 5px 0;float: left' src='"
				let iconEnd = "'>"

				// 图片-样式
				let imgBegin = "<img alt='图片' style='display: block; width: 90%; height: auto; margin: 10px auto;' src='"
				let imgEnd = "'>"

				// 视频-样式
				let videoBegin =
					"<video type='video/mp4' autoplay='true' style='display: block; width: 90%; height: auto; margin: 10px auto;' src='"
				let videoEnd = "'></video>"

				let result = titleBegin + title + titleEnd
				let data = content.split('|')
				for (let i in data) {
					// 识别并格式化
					if ((data[i]).includes("icon-")) {
						result = result + iconBegin + data[i].replace("icon-", "") + iconEnd
					} else if ((data[i]).includes(".png") || (data[i]).includes(".jpg") ||
						(data[i]).includes(".jpeg") || (data[i]).includes(".gif")) {
						result = result + imgBegin + data[i] + imgEnd
					} else if ((data[i]).includes(".mp4")) {
						result = result + videoBegin + data[i] + videoEnd
					} else if ((data[i]).includes("h1")) {
						result = result + titleBegin1 + data[i].replace("h1", "") + titleEnd1
					} else if ((data[i]).includes("h2")) {
						result = result + titleBegin2 + data[i].replace("h2", "") + titleEnd2
					} else {
						result = result + textBegin + data[i] + textEnd
					}
				}

				this.htmlContent = result
			},
			// 格式化目录
			formatCatalogList() {

			},
			checkVersion() {
				let url = this.list.courseDetailUrl
				// url = "https://docs.qq.com/doc/DR25Ra01DQUxhU1Br"
				if (this.flag != "") {
					let param = {
						url: url
					}
					let data = JSON.stringify(param)
					uni.navigateTo({
						url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
					})
				} else {
					this.showMain = true
				}
			},
		},
		onLoad(options) {
			let flag = options.flag || ""
			// 测试时加上
			// this.flag = "web"
		},
		mounted() {
			this.getContentList()
			// 判断是否需要跳转

		}
	}
</script>

<style lang="scss" scoped>
	.img {
		width: 100%;
		height: 100vh;
		margin: 50rpx auto;
	}

	.mask {
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		width: 100%;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.25);
	}

	.mask-wrap {
		position: fixed;
		top: 0;
		left: 0;
		z-index: 9999;
		width: 60%;
		height: 100vh;
		padding: 50rpx;
	}

	.content-view {
		padding: 50rpx;
	}

	/deep/ .u-cell__title-text {
		font-size: 34rpx;
		font-weight: 700;
		line-height: 50rpx;
	}

	/deep/.uni-breadcrumb-item {
		line-height: 80rpx !important;
		font-size: 36rpx !important;
	}
</style>
