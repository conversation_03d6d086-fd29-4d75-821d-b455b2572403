<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 栏目 -->
		<view style="margin: 0rpx 50rpx">
			<u-tabs :list="menuList" @click="choiceMenu" :current="choiceIndex" lineWidth="40" lineHeight="4"
				lineColor="#1e1848" :activeStyle="{
					color: '#1e1848',
				  fontSize: '16px',
					fontWeight: 'bold',
				}" :inactiveStyle="{
					color: '#888',
					fontSize: '16px',
				  fontWeight: 'bold',
				}" itemStyle="height:100rpx">
			</u-tabs>
		</view>

		<!-- 收到 -->
		<uni-transition mode-class="slide-left" :show="choiceIndex == 0">
			<view v-if="choiceIndex == 0">
				<view class="mg-at f16 lh25" style="margin: 40rpx 5%;">
					<view class="comment-tab" v-for="(item,index) in rewardList" :key="index">
						<view class="flac-row" style="padding: 0 20rpx;">
							<view class="w2" @click="openImgPreview(formatHeadImg(item))">
								<u-avatar :src="formatHeadImg(item)" size="50" />
							</view>
							<view class="flac-col w65" style="padding: 10rpx 0;">
								<text style="display: block;">{{formatName(item)}}</text>
								<text style="display: block;">{{formatDate(item.createTime)}}</text>
							</view>
							<view class="w15" style="text-align: left;">
								<uni-icons type="medal-filled" size="24" color="#ff4d4b"></uni-icons>
								<text>{{item.rewardAmount||''}}</text>
							</view>
						</view>
					</view>

					<u-empty v-if="rewardList.length==0" text="暂未收到打赏"
						icon="http://cdn.uviewui.com/uview/empty/data.png" />
				</view>
			</view>
		</uni-transition>

		<!-- 送出 -->
		<uni-transition mode-class="slide-right" :show="choiceIndex == 1">
			<view v-if="choiceIndex == 1">
				<view class="mg-at f16 lh25" style="margin: 40rpx 5%;">
					<view class="comment-tab" v-for="(item,index) in rewardList1" :key="index">
						<view class="flac-row" style="padding: 0 20rpx;">
							<view class="w2" @click="openImgPreview(formatHeadImg(item))">
								<u-avatar :src="formatHeadImg(item)" size="50" />
							</view>
							<view class="flac-col w65" style="padding: 10rpx 0;">
								<text style="display: block;">{{formatName(item)}}</text>
								<text style="display: block;">{{formatDate(item.createDate)}}</text>
							</view>
							<view class="w15" style="text-align: left;">
								<uni-icons type="medal-filled" size="24" color="#ff4d4b"></uni-icons>
								<text>{{item.rewardAmount||''}}</text>
							</view>
						</view>
					</view>

					<u-empty v-if="rewardList1.length==0" text="暂未送出打赏"
						icon="http://cdn.uviewui.com/uview/empty/data.png" />
				</view>
			</view>
		</uni-transition>

		<u-gap height="400"></u-gap>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 页面类型（0：收到打赏 1：送出打赏）
				shareType: 0,

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				menuList: [{
					name: '收到'
				}, {
					name: '送出'
				}],
				choiceIndex: 0,
				blankHeadImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				total: 0,
				rewardList: [],
				rewardList1: [],
				searchCondition: {
					orderBy: "t.createTime ASC",
					commentState: 1,
					current: 1,
					size: 10
				},
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 选择菜单
			choiceMenu(e) {
				this.choiceIndex = e.index
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 格式化时间
			formatDate(value) {
				if (value == null) {
					return "-"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '-' + MM + '-' + d
			},
			formatName(val) {
				return val.name || val.employeeName || val.memberName || '匿名用户'
			},
			formatCreatorName(val) {
				let result = val.reference || val.selfReference || val.creatorName || ''
				let depart = val.creatorDepart ? '(' + val.creatorDepart + ')' : ''
				result += result ? depart : ''
				return result
			},
			formatHeadImg(val) {
				return val.employeeHeadImg || val.memberHeadImg || this.blankHeadImg
			},
			// 获取打赏列表
			listJiaBiReward(value) {
				this.http({
					url: 'listJiaBiReward',
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						receiverId: value == 0 ? this.memberId : null,
						rewarderId: value == 1 ? this.memberId : null,
						orderBy: 't.createTime ASC'
					},
					success: res => {
						if (res.code == 0) {
							if (value == 0) {
								this.rewardList = res.data || []
							} else {
								this.rewardList1 = res.data || []
							}
						}
					},
				})
			},
			// 获取优秀员工
			excellentEmployeeList() {
				this.http({
					url: 'excellentEmployeeList',
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						creator: uni.getStorageSync('employeeNo') || 'xx',
						orderBy: 't.id ASC'
					},
					success: res => {
						if (res.code == 0) {
							this.rewardList1 = res.data || []
						}
					},
				})
			},
		},
		onLoad(options) {
			if (options.choiceIndex) {
				this.choiceIndex = parseInt(options.choiceIndex) || 0
			}
			this.listJiaBiReward(0)
			// this.listJiaBiReward(1)
			this.excellentEmployeeList()
		},
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";
	@import "@/pages-mine/common/css/resume-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}

	.main-text {
		color: #1e1848;
		font-weight: bold;
	}

	.main-bar {
		width: 94%;
		height: auto;
		padding: 40rpx 3%;
		margin: 20rpx 0;
	}

	.bar {
		width: 86%;
		height: 80rpx;
		border: 1rpx #dedede solid;
		padding: 10rpx 7%;
		margin: 20rpx 0;
	}

	.small-tab {
		width: 100%;
		height: 140rpx;
		border-bottom: 2rpx #dedede solid;
		margin: 20rpx 0;
	}

	.comment-tab {
		width: 100%;
		height: auto;
		border-bottom: 2rpx #dedede solid;
		margin: 20rpx 0;
	}

	.list-bottom {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;

		text {
			display: block;
			text-align: center;
			font-weight: 100;
			color: #909399;
		}
	}

	.scroll-Y {
		display: block;
		width: 90%;
		height: 850rpx;
		margin: 0rpx 5%;
	}

	.active {
		border: 10rpx #19be6b solid;
		width: 220rpx;
		height: 300rpx;
		// margin: 10rpx 10rpx;
	}
</style>