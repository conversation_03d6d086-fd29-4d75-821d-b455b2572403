<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<img class="rank-img" :src="rankResultImg" mode="widthFix" />

		<!-- 考试情况提示文本 -->
		<view class="rank-text" style="top: 180rpx;color: #1e1848;">
			<text style="font-size: 44rpx;">{{examRankMine.recordPassed?resultTipsList[1]:resultTipsList[0]}}</text>
		</view>

		<!-- 分数 -->
		<view class="rank-text" style="top: 310rpx;">
			<text style="font-weight: 400;font-size: 100rpx;">{{examRankMine.recordScore}}</text>
		</view>

		<!-- 文本标题 -->
		<view class="tag-big" style="top: 500rpx;">
			<!-- <text>{{memberName}} 的成绩单</text> -->
			<button>{{showRealName?employeeName:memberName}} 的成绩单</button>
		</view>

		<!-- 考试标题 -->
		<view class="rank-text" style="top: 550rpx;">
			<text style="color: #000;">{{examRankMine.examTitle}}</text>
		</view>

		<!-- 试卷提示 -->
		<view class="rank-text" style="top: 830rpx;">
			<text style="font-size: 24rpx;">{{resultTipsBottom}}</text>
		</view>

		<!-- 排行榜标题 -->
		<view class="head-img">
			<img :src="rankTitle" mode="widthFix" />
			<text style="width:0;height: 0;"></text>
			<text style="font-size: 28rpx;color:#909399;display: block;text-align: center;">{{rankTips}}</text>
		</view>

		<u-gap height="20"></u-gap>

		<!-- 排行榜-自己 -->
		<view class="list" style="background-color: #f4f4f5;" v-if="JSON.stringify(examRankMine)!='{}'">
			<listItem class="list-item" :item="examRankMine" :showRealName="showRealName" />
		</view>

		<!-- 排行榜 -->
		<view class="list">
			<listItem class="list-item" v-for="(item,index) in examRank" :key="index" :item="item"
				:showRealName="showRealName" />
		</view>

		<view class="btn-hollow">
			<button @click="openDetail(1)">查看完整榜单</button>
		</view>

		<u-gap height="100"></u-gap>

		<view class="btn-bottom-fixed">
			<button @click="openDetail(0)">查看答案</button>
		</view>
	</view>
</template>

<script>
	import listItem from "@/pages-mine/common/components/list-item.vue";

	export default {
		components: {
			listItem
		},
		data() {
			return {
				// 可设置
				// 显示真名
				showRealName: false,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				service: "https://biapi.xiaoyujia.com/",
				rankIcon: [
					"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_icon1.png",
					"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_icon2.png",
					"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_icon3.png"
				],
				rankResultImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank-result.png",
				rankTitle: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_title.png",
				resultTipsList: ["很遗憾您未通过考试", "恭喜您已通过考试"],
				rankTips: "排行榜每30分钟刷新",
				resultTipsBottom: "注：部分问答题分数由人工阅卷最终决定",
				loadMore: 0,
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				searchCondition: {
					examId: null,
					memberId: null,
					recordState: 2,
					orderBy: "recordScore DESC",
					current: 1,
					size: 10
				},
				examId: 0,
				recordId: 0,
				memberId: null,
				memberName: uni.getStorageSync("memberName") || "我",
				employeeName: uni.getStorageSync("employeeName") || "我",
				examRankIndex: -1,
				examRank: [],
				examRankMine: {},
				recordCourseId: null,
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 查看详情
			openDetail(index) {
				switch (index) {
					case 0:
						uni.navigateTo({
							url: "/pages-mine/exam/exam?id=" + this.recordId + "&flag=1"
						})
						break
					case 1:
						uni.navigateTo({
							url: "/pages-mine/exam/exam-rank?id=" + this.examId + "&examTitle=" + this.examRankMine
								.examTitle
						})
						break
				}
			},
			// 获取考试排行榜
			getExamRank(value) {
				if (value == 0) {
					this.searchCondition.size = 10
				} else if (value == 1) {
					this.searchCondition.size = 1000
				}
				this.http({
					url: 'getUnionExamRank',
					method: 'POST',
					hideLoading: true,
					data: this.searchCondition,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							if (value == 0) {
								this.examRank = this.examRank.concat(res.data.records)
								if (this.searchCondition.current == 1) {
									this.showRealName = res.data.records[0].showRealName == 1 ? true : false
								}
								this.formatRecord()
							} else if (value == 1) {
								this.$set(this.examRankMine, "index", res.data.records[0].rankNum - 1)
							}
						} else {
							console.log('获取考试排行榜-返回错误！' + res.msg)
						}
					}
				});
			},
			// 获取考试记录
			listUnionExamRecord() {
				this.http({
					url: 'listUnionExamRecord',
					method: 'POST',
					hideLoading: true,
					data: {
						id: this.recordId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.examRankMine = res.data[0]
							this.examId = this.examRankMine.examId
							// 格式化显示的分数
							let score = this.examRankMine.recordScore + ""
							if (!score.includes(".")) {
								score += ".0"
							}
							this.examRankMine.recordScore = score

							this.searchCondition.examId = this.examId
							this.searchCondition.memberId = this.memberId

							this.getExamRank(1)
							this.getExamRank(0)

							// 未通过考试切绑定课程，弹出学习提示
							if (this.examRankMine.recordCourseId && this.examRankMine.recordPassed == 0) {
								this.recordCourseId = this.examRankMine.recordCourseId
								this.openCheck(0, '很遗憾，您未通过考试！', '学习相关课程增加通过率吧！')
							}
						}
					}
				});
			},
			// 格式化成绩
			formatRecord() {
				for (let i = 0; i < this.examRank.length; i++) {
					let item = this.examRank[i]
					this.$set(item, "index", i)
				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					uni.navigateTo({
						url: '/pages-mine/studyCenter/course-detail?id=' + this.recordCourseId
					})
				}
			},
		},
		onLoad(options) {
			this.memberId = uni.getStorageSync("memberId")
			this.memberName = uni.getStorageSync("memberName")
			this.recordId = options.id || -1
			this.listUnionExamRecord()
		},
		watch: {
			loadMore: {
				handler(newValue, oldVal) {
					// this.searchCondition.current++
					// this.getExamRank()
				},
				deep: true
			}
		},
		onReachBottom() {
			this.loadMore++
		},
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/exam.scss";

	.rank-img {
		display: block;
		width: 750rpx;
		height: 1000rpx;
		padding: 0;
	}

	.rank-text {
		position: absolute;
		width: 100%;

		text {
			display: block;
			text-align: center;
			font-size: 32rpx;
			color: #1e1848;
		}
	}

	.tag-big {
		position: absolute;
		left: 50%;
		transform: translate(-50%, -50%);

		button {
			display: block;
			height: 70rpx;
			line-height: 70rpx;
			padding: 0 20rpx;
			width: auto;
			font-size: 32rpx;
			border-radius: 30rpx;
			color: #1e1848;
			background-color: rgba(30, 24, 72, 0.1);
		}
	}
</style>