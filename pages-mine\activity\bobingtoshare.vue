<template>
	<view>
		<image style="width:100%;display:block" mode="widthFix" src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1661995680207toshare.jpg" @click="share"></image>
	</view>
</template>

<script>
	export default {
		data(){
			return{
				// 活动ID和代码
				activityId: 1,
				activityCode: '',
				shareMemberId:'',
				account:uni.getStorageSync('account')
			};
		},
		onLoad(e) {
			this.activityCode = e.activityCode;
			this.shareMemberId = e.shareMemberId;
			if(!uni.getStorageSync('memberId')){
				this.$toast.toast('登录失效，请重新登录')
				uni.setStorageSync('redirectUrl', '/pages-mine/activity/bobingtoshare?activityCode='+this.activityCode+'&shareMemberId='+this.shareMemberId);
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages-mine/login/login'
					})
				}, 2000)
			}
		},
		
		methods:{
			share(){	
				let param = {
					url : 'https://activity.xiaoyujia.com/boBing?shareMemberId='+this.shareMemberId+
					'&activityCode='+this.activityCode+'&account='+this.account
				}
				let data = JSON.stringify(param);
				uni.redirectTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
				})
			}
		}
	}
	
</script>

<style>
</style>