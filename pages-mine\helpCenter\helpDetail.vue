<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<view style="padding: 30rpx;">
			<view class="f18 fb lh40">{{unionQaData.title}}</view>
			<view class="f15 c3 lh20">{{unionQaData.replyText||replyList.length?'':'未配置答复'}}</view>
		</view>

		<view style="padding: 0 40rpx;">
			<view v-if="unionQaData.replyText">
				<view v-html="unionQaData.replyText" style="padding: 0rpx 0 20rpx 0;">
				</view>
			</view>
			<view v-else-if="replyList.length">
				<view v-for="(item,i) in replyList" :key="i">
					<view class="f15 c3 lh20" style="padding-bottom: 20rpx;">{{item.sort}}、{{item.reply}}</view>
					<image :src="item.pictureUrl"
						style="display: block; width: 70%;height: 70%;border-radius: 5px;margin: 0 auto;margin-top: 20rpx;padding-bottom: 20rpx;"
						mode="widthFix" v-if="item.pictureUrl" @click="openImgPreview(item.pictureUrl)" />
					<view class="f15 c3 lh20" style="color:deepskyblue" v-if="item.routeUrl"
						@click="gotoRoute(item.routeUrl)">点击跳转</view>
				</view>
			</view>
		</view>

		<view style="padding: 25rpx;">
			<view style="margin: 40rpx auto;"></view>
			<view class="flac-row-a" style="margin: 20rpx auto;">
				<view class="w5 mg-at">
					<u-icon name="kefu-ermai" size="22" label="联系客服" labelPos="right" labelSize="16"
						@click="clickCustomer" />
				</view>
				<view class="w4 mg-at mg-at flac-row-b">
					<u-icon name="thumb-up" size="22" :label="unionQaData.ofUse" labelPos="right" labelSize="16"
						:color="upColor" :labelColor="upLabelColor" @click="clickUse(1)" />
					<u-icon name="thumb-down" size="22" :label="unionQaData.noUse" labelPos="right" labelSize="16"
						:color="downColor" :labelColor="downLabelColor" @click="clickUse(0)" />
					<button plain open-type='share' style="width: 80rpx;margin: 0 0;padding: 0;" @click="share()">
						<uni-icons type="redo" size="22"></uni-icons>
					</button>
				</view>
			</view>

			<view class="f15 c3 lh20" style="color:deepskyblue;margin-left: 20rpx;" @click="back()">返回帮助中心</view>
		</view>

		<u-gap height="80"></u-gap>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1f2120'
					}
				},

				searchVal: '',
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				tabsIndex: 0,
				tabsList: [],
				unionQaList: [],
				replyList: [],
				answerList: [{
					title: '',
					content: ''
				}],
				showPopup: false,
				qaDataId: 0,
				unionQaData: {},
				upColor: '',
				upLabelColor: '',
				downColor: '',
				downLabelColor: '',
				typeID: null,
			};
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			share() {
				//  #ifdef H5
				let url = 'https://jiajie.xiaoyujia.com/pages-mine/helpCenter/helpDetail?id=' + this.qaDataId
				uni.setClipboardData({
					data: url,
					success: () => {
						this.$refs.uNotify.success(name + '分享链接复制成功!')
					}
				})
				// #endif
			},
			back() {
				uni.navigateTo({
					url: '/pages-mine/helpCenter/helpIndex'
				})
			},
			gotoRoute(val) {
				uni.navigateTo({
					url: val
				})
			},
			// 打开图片预览
			openImgPreview(url) {
				let data = []
				data.push(url)
				uni.previewImage({
					urls: data,
					current: url
				})
			},
			getUnionQaById() {
				this.http({
					url: 'getUnionQaById',
					method: 'GET',
					hideLoading: true,
					data: {
						id: this.qaDataId,
					},
					success: res => {
						if (res.code == 0) {
							this.unionQaData = res.data.unionQa
							this.replyList = res.data.replyList
							this.getUseLogData()
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg
							})
						}
					}
				})
			},
			getUseLogData() {
				this.http({
					url: 'getUseLogData',
					method: 'GET',
					hideLoading: true,
					data: {
						qaId: this.qaDataId,
						memberId: uni.getStorageSync('memberId')
					},
					success: res => {
						if (res.code == 0) {
							if (res.data == 1) {
								this.downColor = "#4991f7"
								this.upColor = ""
							} else if (res.data == 2) {
								this.upColor = "#4991f7"
								this.downColor = ""
							} else {
								this.upColor = ""
								this.downColor = ""
							}
						}
					}
				})
			},
			clickDetail(i) {
				this.unionQaData = this.unionQaList[i]
				this.showPopup = true
				this.getUnionQaById()
				this.getUseLogData()
			},
			clickCustomer() {
				// #ifdef  MP-WEIXIN
				wx.openCustomerServiceChat({
					extInfo: {
						url: 'https://work.weixin.qq.com/kfid/kfc51d7db04b2a4a1d7' //客服地址链接
					},
					corpId: 'wx25c9a236883d741d', //必须和你小程序上的一致
					success(res) {
						console.log(res, 1)
					},
					fail(res) {
						console.log(res, 2)
					},
				})
				// #endif
				// #ifdef  MP-TOUTIAO || H5
				uni.showToast({
					icon: 'none',
					title: '请在小程序内操作!'
				})
				// #endif
			},
			clickUse(val) {
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.error('登录后才可操作哦!')
					return
				}

				this.http({
					url: 'saveQaUseLog',
					method: 'POST',
					data: {
						memberId: uni.getStorageSync('memberId'),
						clickType: val,
						qaId: this.qaDataId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.getUnionQaById()
							setTimeout(() => {
								this.getUseLogData()
							}, 500)
						} else {
							uni.showToast({
								icon: 'none',
								title: '操作失败!'
							})
						}
					}
				})
			},
			// 分享
			onShareAppMessage(res) {
				let title = '帮助中心-' + this.unionQaData.title
				return {
					title: title,
					path: '/pages-mine/helpCenter/helpDetail?id=' + this.qaDataId,
					mpId: 'wx8342ef8b403dec4e'
				}
			},
		},
		onLoad(options) {
			this.qaDataId = options.id || 0
			this.getUnionQaById()
		}
	}
</script>

<style lang="scss" scoped>
	/deep/.uni-searchbar__box {
		border: 2rpx solid #eee;
		justify-content: unset;
	}

	button[plain] {
		border: 0
	}
</style>