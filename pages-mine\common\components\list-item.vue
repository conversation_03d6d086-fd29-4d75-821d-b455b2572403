<template>
	<view>
		<!-- 列表栏目 -->
		<view class="list-item" v-if="JSON.stringify(item)!='{}'">
			<view class="item-left">
				<img v-if="item.index<3" :src="rankIcon[item.index]">
				<text v-if="item.index>=3&&item.index<99999">{{item.index+1}}</text>
			</view>
			<!-- 会员信息 -->
			<view class="item-content" v-if="!showRealName">
				<img :src="item.headImg !== ''&&item.headImg !== null ? item.headImg : blankImg" />
				<img :src="rankImg[item.index]" v-if="item.index<3">
				<view class="item-text">
					<text>{{checkName(item.name)}}</text>
					<text>编号：{{item.memberId||'暂无'}}</text>
				</view>
			</view>
			<!-- 员工信息 -->
			<view class="item-content" v-if="showRealName">
				<img :src="item.headPortrait !== ''&&item.headPortrait !== null ? item.headPortrait : blankImg" />
				<img :src="rankImg[item.index]" v-if="item.index<3">
				<view class="item-text">
					<text>{{checkName(item.realName)}}</text>
					<text>编号：{{item.employeeId||'暂无'}}</text>
				</view>
			</view>
			<view class="item-right">
				<text>{{formatScore(item.recordScore)}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "listItem",
		props: {
			item: {
				type: Object,
				default: {}
			},
			showRealName: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return { // 确认弹框 checkType: 0,
				checkTitle: "",
				checkText: "",
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#ffffff'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				rankIcon: [
					"https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/exam/rank_icon1.png",
					"https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/exam/rank_icon2.png",
					"https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/exam/rank_icon3.png"
				],
				rankImg: [
					"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_img1_f.png",
					"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_img2_f.png",
					"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_img3_f.png"
				],
				examRankImg: "https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/exam/exam-ranking.png",
				blankImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png',
			}
		},
		methods: {
			// 格式化字符
			checkName(str) {
				if (str == null || str == "") {
					return "匿名用户"
				} else {
					let max = 14
					if (str.length > max) {
						str = str.substring(0, max) + '...'
					}
					return str
				}
			},
			// 格式化分数
			formatScore(value) {
				let str = value + ""
				if (value == null) {
					return '暂无成绩'
				}

				if (str.includes(".")) {
					value = parseFloat(value)
					return value.toFixed(1) + '分'
				} else {
					return value + '分'
				}
			}
		},
		onLoad(options) {}
	}
</script>


<style lang="scss">
	/*
		排行榜部分
	*/

	.list {
		width: 100%;
		display: flex;
		flex-direction: column;
	}

	.list-item {
		width: 100%;
		height: 140rpx;
		display: flex;
		border-bottom: #f4f4f5 2px solid;
		// box-shadow: 5rpx 10rpx 20rpx #dedede;
	}

	.item-left,
	.item-right {
		width: 20%;
		height: 140rpx;
		line-height: 140rpx;

		img {
			display: block;
			width: 50rpx;
			height: 60rpx;
			margin: 40rpx auto;
		}

		text {
			display: block;
			text-align: center;
			font-size: 36rpx;
		}
	}

	.item-left {
		text {
			font-size: 36rpx;
			color: #909399;
		}
	}

	.item-right {
		width: 25%;

		text {
			text-align: right;
			padding-right: 40rpx;
		}
	}

	.item-content {
		display: flex;
		width: 55%;
		height: 140rpx;

		img:nth-child(1) {
			display: block;
			border-radius: 50%;
			width: 80rpx;
			height: 80rpx;
			margin: 30rpx 0;
		}

		img:nth-child(2) {
			position: absolute;
			width: 89rpx;
			height: 89rpx;
			margin: 26rpx 0rpx 20rpx -5rpx;
		}
	}

	.item-text {
		display: flex;
		flex-direction: column;
		margin: 20rpx 20rpx;

		text {
			font-size: 32rpx;
			line-height: 50rpx;
		}

		text:nth-child(2) {
			font-size: 28rpx;
			color: #909399;
		}
	}
</style>