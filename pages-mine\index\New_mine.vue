<template>
	<view class="page">

		<!-- #ifdef  MP-WEIXIN -->
		<!-- <u-gap height="90"></u-gap> -->
		<!-- #endif -->

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 头部 用户信息 -->
		<!-- <view class="header"> -->
			<view class="user-head cf">
				<view class="head-img">
					<img :src="headImg!=null&&headImg!=''?headImg:blankImg" @click="openAccount()" alt="">
				</view>

				<view class="user-info" v-if="isLogin">
					<text class="cfdd472">{{ employeeName||memberName||'暂无用户名'}}</text>
				</view>
				<view class="user-info" v-if="!isLogin" @click="openLogin()">
					<text>立即登录</text>
				</view>

				<view class="setting">
					<img :src="settingImg" alt="" @click="openSetting()">
				</view>
			</view>
			<view class="w88 mg-at bacf c0 boxStyle flac-row-a">
				<view class="flac-row-a" v-for="(item,i) in navList" :key="i">
					<image style="width: 60rpx;" :src="item.icon" mode="widthFix"></image>
					<view class="t-indent" @click="goPage(item.url)">
						<view class="f14 lh20">{{item.title}} ></view>
						<view class="c9 f12">{{item.text}}</view>
					</view>
					<view class="line" v-if="i != 2"></view>
				</view>
			</view>
		<!-- </view> -->

		<!-- 菜单栏目列表 -->
		<view class="w88 mg-at menu-list">
			<view class="list-column" v-for="(menuList, index) in menuList" :key="index" @click="openMineDetail(index)"
				v-if="menuList.show">
				<image style="width: 30px;height: 30px;" :src="menuList.listImage"></image>
				<text class="column-text">{{menuList.listTitle}}</text>
				<text class="column-text-right">&ensp;❯</text>
			</view>
		</view>

		<!-- 退出登录 -->
		<view class="logout" v-if="isLogin">
			<text @click="tryLogout()">退出登录</text>
		</view>

		<view style="width: 100%;color: #909399;margin: 10rpx 0 0 40rpx;font-size: 32rpx;"
			v-if="checkStr(introducerIdExisted)=='暂无'?false:true">
			<text>推荐人：{{checkStr(introducerIdExisted)}}</text>
		</view>

		<u-gap height="80"></u-gap>

	</view>
</template>

<script>
	export default {
		name: 'mine',
		props: {
			refresh: {
				type: Boolean
			}
		},
		data() {
			return {
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				roleId: uni.getStorageSync("roleId") || 0,
				franchiseId: "",
				isLogin: false,
				////审核状态  0：未通过 1：未处理  2：已通过
				flag: null,
				//银行卡信息标识
				flagB: null,
				//商户信息标识  1：不存在 2:存在
				flagC: 1,
				checkText: "",
				bankReservedPhone: null,
				authenticationType: null,
				agent: '',
				introducerIdExisted: null,
				isEmployee: false,
				isBaomu: false,
				//是否付款 1：已付款
				orderState: null,
				memberId: uni.getStorageSync('memberId') || null,
				employeeId: uni.getStorageSync('employeeId') || null,
				memberName: uni.getStorageSync('memberId') || "",
				employeeName: uni.getStorageSync('employeeName') || "",
				headImg: uni.getStorageSync("memberHeadImg"),

				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				settingImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-set.png",
				menuList: [{
						index: 0,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E5%90%88%E4%BC%99%E4%BA%BA.png',
						listTitle: '合伙人',
						listUrl: '/pages-mine/franchise/enter/topUpMoney',
						show: true,
					}, {
						index: 1,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-jl.png',
						listTitle: '我的简历',
						listUrl: '/pages-mine/resume/resume',
						show: true,
					},
					{
						index: 2,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-ms.png',
						listTitle: '我的面试',
						listUrl: '/pages-mine/interview/interview',
						show: true,
					},
					{
						index: 3,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-myjn.png',
						listTitle: '我的技能',
						listUrl: '/pages-mine/resume/ability',
						show: false,
					},
					{
						index: 4,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E6%88%91%E7%9A%84%E8%AE%A2%E5%8D%95.png',
						listTitle: '我的订单',
						listUrl: '/pages-mine/orders/orders',
						show: true,
					}, {
						index: 5,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E6%88%91%E7%9A%84%E5%90%88%E5%90%8C.png',
						listTitle: '我的合同',
						listUrl: '/pages-mine/contract/contract',
						show: true,
					}, {
						index: 6,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E6%88%91%E7%9A%84%E8%AF%81%E4%B9%A6.png',
						listTitle: '我的证书',
						listUrl: '/pages-mine/certificate/certificate',
						show: true,
					}, {
						index: 7,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E6%88%91%E7%9A%84%E4%BD%B3%E5%B8%81.png',
						listTitle: '我的积分',
						listUrl: '/pages-mine/signIn/signin',
						show: true,
					},
					{
						index: 8,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E9%82%80%E8%AF%B7%E6%9C%89%E7%A4%BC.png',
						listTitle: '邀请有礼',
						listUrl: '/pages-mine/invitation/invitation',
						show: true,
					}, {
						index: 9,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-study.png',
						listTitle: '学习中心',
						listUrl: '/pages-mine/studyCenter/index',
						show: true,
					}, {
						index: 10,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E6%88%91%E7%9A%84%E8%80%83%E8%AF%95.png',
						listTitle: '考试中心',
						listUrl: '/pages-mine/exam/exam-center',
						show: true,
					}, {
						index: 11,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-hd.png',
						listTitle: '活动中心',
						listUrl: '/pages-mine/activity/bobingshare',
						show: false,
					}, {
						index: 12,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E4%B8%9A%E5%8A%A1%E7%AE%A1%E7%90%86.png',
						listTitle: '业务管理',
						listUrl: '/pages-work/business/businessIndex',
						show: false,
					}, {
						index: 13,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E5%91%98%E5%B7%A5%E7%AE%A1%E7%90%86.png',
						listTitle: '员工管理',
						listUrl: '/pages-other/hr/staff',
						show: false,
					},
					{
						index: 14,
						listImage: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon_help_center.png',
						listTitle: '帮助中心',
						listUrl: '/pages-mine/helpCenter/helpIndex',
						show: true,
					},
					{
						index: 15,
						listImage: 'https://xyjcloud-ui.obs.cn-east-3.myhuaweicloud.com/static/icon/%E4%B8%9A%E5%8A%A1%E7%AE%A1%E7%90%86.png',
						listTitle: '直播中心',
						listUrl: '/pages-mine/room/liveCenter',
						show: false,
					},
					// {
					// 	listImage: 'https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6307328417f5d3001295f38b/16614161443817950029.png',
					// 	listTitle: '我的培训',
					// 	listUrl: '/pages-mine/train/train',
					//  show: true,
					// },
					// {
					// 	listImage: 'https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6307328417f5d3001295f38b/16614161443974261862.png',
					// 	listTitle: '工作手册',
					// 	listUrl: '/pages-mine/handbook/handbook',
					//  show: true,
					// },
					// {
					// 	listImage: 'https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6307328417f5d3001295f38b/16614161443974261862.png',
					// 	listTitle: '帮助中心',
					// 	listUrl: '/pages-mine/help/help',
					//  show: true,
					// }, {
					// 	listImage: 'https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6307328417f5d3001295f38b/16614161443916814885.png',
					// 	listTitle: '建议反馈',
					// 	listUrl: '/pages-mine/feedback/feedback',
					//  show: true,
					// },
					// {
					// 	listImage: 'https://codefun-proj-user-res-1256085488.cos.ap-guangzhou.myqcloud.com/6307328417f5d3001295f38b/16614161443974261862.png',
					// 	listTitle: '小羽佳课堂',
					// 	listUrl: '/pages-mine/signIn/index',
					//  show: true,
					// },
				],
				navList:[{
					icon:'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/jf_order.png',
					title:'做任务',
					text:'签到有礼',
					url:'/pages-mine/signIn/taskPage'
				},{
					icon:'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/jf_xz.png',
					title:'领勋章',
					text:'家政新星',
					url:'/pages-mine/signIn/medalPage'
				},{
					icon:'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/jf_jb.png',
					title:'去商城',
					text:'积分兑换',
					url:'/pages-mine/signIn/jiabi-store'
				}]
			}
		},
		methods: {
			checkStr(str) {
				if (str == null || str == "" || str == "null") {
					return "暂无"
				} else {
					return str
				}
			},
			// 打开登录
			openLogin() {
				uni.reLaunch({
					url: '/pages-mine/login/login'
				});
			},
			// 打开个人信息
			openAccount() {
				// 未登录的情况
				if (!this.isLogin) {
					return uni.navigateTo({
						url: "/pages-mine/login/notlogin"
					})
				}

				// uni.navigateTo({
				// 	url: '/pages-mine/resume/account'
				// });
				uni.navigateTo({
					url: '/pages-mine/mine-detail/setting'
				});
			},
			// 打开设置页面
			openSetting() {
				// 未登录的情况
				if (!this.isLogin) {
					return uni.navigateTo({
						url: "/pages-mine/login/notlogin"
					})
				}

				uni.navigateTo({
					url: "/pages-mine/mine-detail/setting"
				});
			},
			//如果是员工并开通体验权限就生成商户信息
			getIfEmployeeByMemberId() {
				this.http({
					outsideUrl: "https://api.xiaoyujia.com/unionMerchant/getIfEmployeeByMemberId",
					method: 'GET',
					data: {
						memberId: uni.getStorageSync("memberId"),
						agent: this.agent,
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							//返回值为2  则生成商户信息成功或存在商户信息
							this.flagC = res.data
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			},
			// 打开对应的详细页面
			openMineDetail(index) {
				let url = this.menuList[index].listUrl
				// 未登录情况-可允许在不登录情况下查看的菜单索引
				let allowNoLoginMenu = [4, 6, 7, 10, 11, 14]
				if (!this.isLogin) {
					if (allowNoLoginMenu.findIndex(item => item == index) == -1) {
						return uni.navigateTo({
							url: "/pages-mine/login/notlogin?url=" + url
						})
					}
				}
				if (index == 0 && !uni.getStorageSync("employeeNo") && this.flagC == 2 && !this.bankReservedPhone && this
					.franchiseId) {
					url = "/pages-mine/franchise/enter/platformAudit"
				} else
					//如果点击的是合伙人  银行卡存在或已存在商户信息 则跳转工作台
					if (index == 0 && this.flagC == 2) {
						url = "/pages-work/index"
					} else
						//已付款且不存在银行卡信息、存在商户信息 则跳转平台审核页
						if ((index == 0 && this.orderState == 1 && this.franchiseId) ||
							index == 0 && this.orderState == 1 && this.authenticationType == 1) {
							url = "/pages-mine/franchise/enter/platformAudit"
						} else
							//已付款不存在商户信息跳转主体页面
							if (index == 0 && this.orderState == 1 && !this.franchiseId) {
								url = "/pages-mine/franchise/enter/bodyMsg"
							}
				if (index == 12) {
					let tmpIds = [];
					// if (uni.getStorageSync("employeeType") == 10 || uni.getStorageSync("roleId") == 1) {
					// 	取消订单
					// 	tmpIds.push("YRSv7oviwbjl0U-fo-ghtq6OUG2Zn4u-pqd6Y9ums90")
					// 	接单
					// 	tmpIds.push("U2NPysqnHSDt_1oVl1Nvbwy_vivhiRfrlHqwwEI3xEQ")
					// 	服务评价
					// 	tmpIds.push("x64aI96dYKraSdcHcCTSh7sS0Bdvx8rRfzo3TU2RXK0")
					// }

					if (uni.getStorageSync("employeeType") == 20 || uni.getStorageSync("roleId") == 1) {
						// 活动通知
						tmpIds.push("ZEuR4v5y6Bpt-G4Bxx5YHo5beBSGo1XTOSqvgpv1DsI")
						// 获取订阅消息授权
						// #ifdef  MP-WEIXIN
						wx.requestSubscribeMessage({
							tmplIds: tmpIds,
							success: res => {
								console.log("用户同意进行小程序消息订阅！")
							},
							fail: res => {}
						})
						// #endif
					}
				}
				uni.navigateTo({
					url: url
				});
			},
			// 退出登录
			tryLogout() {
				this.openCheck(0, "确定退出登录吗？", "下次需要重新登录哦～")
			},
			// 退出登录
			logout() {
				// 清除用户数据并返回到登录页
				this.$toast.toast('退出成功！')
				if (uni.getStorageSync('employeeId')) {
					//更新员工token
					this.http({
						url: 'updateEmployeeToken',
						method: 'POST',
						data: {
							employeeId: uni.getStorageSync('employeeId')
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						hideLoading: true,
						success: res => {

						}
					})
				}
				uni.clearStorage()
				let timer = setTimeout(() => {
					uni.redirectTo({
						url: "/pages-mine/login/login"
					})
				}, 500);
			},
			// 检查员工状态
			checkState() {
				if (uni.getStorageSync("isInvited") !== null) {
					if (uni.getStorageSync("isInvited") == true) {
						return true
					}
				}
				let employeeState = uni.getStorageSync("employeeState")
				if (employeeState != -1) {
					return true
				} else {
					return false
				}
			},
			// 检查员工身份
			checkEmployee() {
				// 测试时加上
				// return
				this.isEmployee = uni.getStorageSync("isEmployee") == true ? true : false
				this.isBaomu = uni.getStorageSync("isBaomu") == true ? true : false
				let employeeType = uni.getStorageSync("employeeType")
				console.log("是否员工：", this.isEmployee, "是否保姆：", this.isBaomu)
				if (this.isEmployee) {
					if (this.isBaomu) {
						this.menuList[0].show = false
					} else {
						this.menuList[1].show = false
						this.menuList[2].show = false
						this.menuList[3].show = false
					}
				}
				if (employeeType == 10) {
					this.menuList[0].show = false
				}

				if ((employeeType == 10 && !this.isBaomu) || this.roleId == 1) {
					this.menuList[12].show = true
				}
				// 规避审核人员
				if (!this.checkState()) {
					this.menuList[1].show = false
					this.menuList[2].show = false
					this.menuList[3].show = false
				}

				this.checkHRBPPower()
			},
			// 校验员工是否符合HRBP权限（员工管理）
			checkHRBPPower() {
				return
				let showHRBPP = uni.getStorageSync("showHRBPP") || false
				// 不缓存
				showHRBPP = false
				if (showHRBPP) {
					this.menuList[13].show = true
				} else {
					this.http({
						url: "checkHRBPPower",
						method: 'POST',
						data: {
							id: this.employeeId
						},
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						hideLoading: true,
						success: res => {
							if (res.code == 0) {
								if (res.data) {
									this.menuList[13].show = true
									uni.setStorageSync("showHRBPP", true)
								} else {
									uni.setStorageSync("showHRBPP", false)
								}
							} else {
								uni.setStorageSync("showHRBPP", false)
							}
						},
					})
				}
			},
			// 尝试从登录后的缓存中获取用户信息
			getMemberInfor() {
				let iId = uni.getStorageSync("introducerIdExisted")
				this.introducerIdExisted = iId == "" || iId == undefined || iId == null ? "" : iId
				this.checkEmployee()
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open(this.type)
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：确认退出
				if (this.checkType == 0) {
					this.logout()
				}
			},
			getState() {
				this.http({
					url: "getFranchiseMsgById",
					method: 'GET',
					path: uni.getStorageSync("memberId"),
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							//审核状态  0：未通过 1：未处理  2：已通过
							this.flag = res.data.status
							//是否付款  1：已付款
							this.orderState = res.data.flag
							this.franchiseId = res.data.franchiseId
							this.authenticationType = res.data.authenticationType
							this.bankReservedPhone = res.data.bankReservedPhone
							//判断认证类型是个人还是企业 1：个体  2：企业
							//个体判断个人银行卡  企业判断法人银行卡 是否存在
							// if (res.data.authenticationType == 1 && res.data.bankCardNo ||
							//     res.data.authenticationType == 2 && res.data.personBankCard) {
							//   this.flagB = 1
							// }
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			},
			getUnionMerchantData() {
				this.http({
					url: "getUnionMerchantData",
					method: 'GET',
					data: {
						memberId: uni.getStorageSync("memberId") || 0
					},
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							uni.setStorageSync("merchantCode", res.data.merchantCode)
						}
					}
				})
			},
			checkLogin() {
				console.log("正在检查登录状态...")
				if (!uni.getStorageSync('memberId')) {
					this.$toast.toast("您还未进行登录哦，登录后可体验更多功能！")
					uni.setStorageSync('redirectUrl', '/pages/index/index')
					this.menuList[1].show = false
					this.menuList[2].show = false
					// setTimeout(() => {
					// 	uni.redirectTo({
					// 		url: '/pages-mine/login/login'
					// 	})
					// }, 2000)
					return false
				} else {
					this.isLogin = true
					this.getMemberInfor()
					this.getState()
					this.getIfEmployeeByMemberId()
					return true
				}
			},
			goPage(url) {
				uni.navigateTo({
					url:url
				})
			}
		},
		watch: {
			// 监听返回操作并刷新页面数据
			refresh: {
				handler(newValue, oldVal) {
					this.getMemberInfor()
				},
				deep: true
			}
		},
		onShow() {
			this.getMemberInfor()
		},
		mounted() {
			this.checkLogin()
			this.getUnionMerchantData()
		}
	}
</script>

<style lang="scss" scoped>
	.boxStyle {
		border-radius: 20rpx;
		// border-top-right-radius: 30rpx;
		padding: 40rpx 20rpx;
		margin: auto auto 30rpx;
	}
	.line {
		width: 2rpx;height: 100rpx;
		background-color: #333;
		margin-left: 20rpx;
	}
	
	.page {
		width: 100%;min-height: 100vh;
		background-color: #f0f0f0;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/jifen/jf_bg2.png') no-repeat center;
		background-size: 100% 105%;
	}

	// .header {
	// 	width: 100%;
	// 	height: 360rpx;
	// 	box-shadow: 0 4rpx 20rpx #dedede;
	// 	margin-bottom: 20rpx;
	// 	padding: 20rpx 0;
	// 	border-bottom-left-radius: 30%;
	// 	border-bottom-right-radius: 30%;
	// }

	// 用户信息
	.user-head {
		display: flex;
		width: 100%;
		height: 200rpx;
	}

	.head-img {
		position: relative;
		width: 20%;
		height: 200rpx;

		img {
			display: block;
			margin-left: 40rpx;
			margin-top: 50rpx;
			width: 120rpx;
			height: 120rpx;
			border-radius: 50%;
		}
	}

	.user-info {
		width: 60%;
		height: 100%;

		text {
			position: relative;
			left: 40rpx;
			top: 80rpx;
			display: block;
			width: 100%;
			height: 50rpx;
			line-height: 50rpx;
			font-size: 36rpx;
		}
	}

	.setting {
		width: 20%;

		img {
			position: relative;
			display: block;
			left: 40rpx;
			top: 90rpx;
			width: 50rpx;
			height: 50rpx;
		}
	}

	.user-name {
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		margin-top: 10rpx;
		text-align: center;
		height: 40rpx;
		line-height: 40rpx;
	}

	// 课程资料
	.course-info {
		display: flex;
		width: 90%;
		height: 180rpx;
		margin: 20rpx auto;
		border-radius: 20rpx;
		border: #f4f4f5 2px solid;
		box-shadow: 0 4rpx 20rpx #dedede;
	}

	.course-left,
	.course-right {
		width: 50%;
		height: 100%;
	}

	.left-content,
	.right-content {
		width: 100%;
		height: 50%;
		margin: 12% auto;
	}

	.course-left view {
		border-right: #dedede 1px solid;
	}

	.text-head {
		display: block;
		width: 100%;
		height: 40rpx;
		text-align: center;
		margin: 0;
	}

	.course-time {
		font-size: 40rpx;
		font-weight: bold;
	}

	.course-unit {
		font-size: 32rpx;
		margin-left: 10rpx;
	}

	.course-text {
		display: block;
		font-size: 36rpx;
		text-align: center;
		margin-top: 10rpx;
	}

	// 菜单列表
	.menu-list {
		display: block;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx #dedede;
		background-color: #fff;
		padding: 0 20rpx;
		border-radius: 20rpx;
	}

	.list-column {
		width: 100%;
		height: 120rpx;
	}

	.list-column image {
		float: left;
		margin: 32rpx 0 0 40rpx;
		width: 50rpx;
		height: 50rpx;
	}

	.column-text {
		line-height: 120rpx;
		margin: 0 0 0 20rpx;
		font-size: 32rpx;
	}

	.column-text-right {
		float: right;
		margin-right: 40rpx;
		line-height: 120rpx;
		color: #909399;
	}

	.logout {
		width: 100%;
		height: 100rpx;
		border: #909399;
		box-shadow: 0 4rpx 20rpx #dedede;

		text {
			display: block;
			text-align: center;
			width: 40%;
			height: 100rpx;
			margin: 0 auto;
			line-height: 100rpx;
			color: #1e1848;
			font-size: 36rpx;
		}
	}
</style>