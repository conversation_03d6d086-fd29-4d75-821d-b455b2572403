<template>
	<view>
		<view style="height: 20rpx;width: 100%;background-color: #ffffff;" v-if="!showCameraPage"></view>

		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<!-- 日期选择器 -->
		<u-datetime-picker :show="show" v-model="validity" mode="date" @cancel="show=false" @confirm="show=false"
			:minDate="minDate"></u-datetime-picker>

		<!-- 人脸识别相机 -->
		<cameraPage :showCameraPage="showCameraPage" @submitCameraPhoto="submitCameraPhoto"
			@closeCameraPage="closeCameraPage" />

		<!-- 上传头像提示弹窗 -->
		<u-popup :show="popupShow" mode="bottom" @close="popupShow = false">
			<view class="popup-title">
				<text>头像照片示例</text>
			</view>
			<view class="popup-tips">
				<text style="color: #909399;text-align: left;">穿着整洁，头发整齐，站在白墙前拍摄照片，你的头像会更好看。</text>
			</view>
			<view class="popup-img" @longpress="uploadCertificate(4)">
				<img :src="headUplpadTips" alt="" @click="uploadCertificate(0)" mode="widthFix">
			</view>
			<view class="popup-tips" @longpress="uploadCertificate(4)">
				<text style="color: #ff4d4b;">*无法上传可以尝试长按这里哦</text>
			</view>
			<view class="btn-big" style="padding-bottom: 0rpx;">
				<button class="btnStyle" style="margin: 40rpx auto 60rpx auto;"
					@click="uploadCertificate(0)">我知道了</button>
			</view>
		</u-popup>

		<!-- 上传健康证提示弹窗 -->
		<u-popup :show="popupShow1" mode="bottom" @close="popupShow1 = false">
			<view class="popup-title">
				<text>健康证照片示例</text>
			</view>
			<view class="popup-tips">
				<text style="color: #909399;text-align: left;">需上传有效期内的健康证，照片清晰，没有反光，会更容易通过审核。</text>
			</view>
			<view class="popup-img">
				<img :src="healthUplpadTips" alt="" @click="uploadCertificate(1)" mode="widthFix">
			</view>
			<view class="btn-big" style="padding-bottom: 0rpx;">
				<button class="btnStyle" style="margin: 40rpx auto 60rpx auto;"
					@click="uploadCertificate(1)">我知道了</button>
			</view>
		</u-popup>

		<!-- 上传体检表提示弹窗 -->
		<u-popup :show="popupShow2" mode="bottom" @close="popupShow2 = false">
			<view class="popup-title">
				<text>体检表照片示例</text>
			</view>
			<view class="popup-tips">
				<text style="color: #909399;text-align: left;">需上传有效期内的体检表，文件清晰，没有反光，会更容易通过审核。</text>
			</view>
			<view class="popup-img">
				<img :src="healthUplpadTips1" alt="" @click="uploadCertificate(2)" mode="widthFix">
			</view>
			<view class="btn-big" style="padding-bottom: 0rpx;"><button class="btnStyle"
					style="margin: 40rpx auto 60rpx auto;" @click="uploadCertificate(2)">我知道了</button></view>
		</u-popup>

		<!-- 须知提示弹窗 -->
		<u-popup :show="popupShowTips" mode="bottom" @close="popupShowTips = false">
			<view class="lh50 text-c fb f18" @click="popupShowTips = false">
				平台入驻须知
			</view>
			<view class="filter-content" style="height: 500rpx;">
				<view class="filter-content-text">
					<text v-for="(item,index) in attentionList" :key="index">{{item}}</text>
				</view>
			</view>
		</u-popup>

		<view class="btn-small" v-if="steps==0&&isLogin&&!showCameraPage&&openOldEmployee">
			<button @click="isOldEmployee=!isOldEmployee">{{isOldEmployee?"新员工入驻":"老员工入驻"}}</button>
		</view>
		<view class="logo" v-if="!showCameraPage">
			<img :src="logoImg" alt="" mode="widthFix">
		</view>

		<!-- 老员工入驻 -->
		<view v-if="isOldEmployee&&!showCameraPage">
			<view class="tab">
				<view class="tab-head">
					<text>老员工入驻</text>
				</view>

				<view class="tab-head-smail">
					<text>在小羽佳注册过才可使用哦</text>
				</view>
				<view class="tab-inputbox">
					<view class="tab-input">
						<input class="single-input" v-model="employeeInfo.idCard" placeholder="请输入您的身份证号">
						</input>
					</view>
				</view>

				<view class="btn-bottom" style="margin: 80rpx 0;">
					<button class="btnStyle" @click="checkEmployeeByIdCard()">填写好了</button>
				</view>

				<view style="display: flex;flex-wrap: wrap;">
					<view class="cer-content flac-col-c" v-for="(item, index) in employeeList" :key="index">
						<img :src="item.headPortrait" width="40" height="36" />
						<text>{{item.realName}}</text>
						<text>{{item.storeName||'小羽佳总部'}}</text>
						<view class="btn-middle">
							<button @click="choiceOldEmployee(index)" class="btnStyle">这个是我</button>
						</view>

					</view>
				</view>
			</view>
		</view>



		<view v-if="!isOldEmployee&&!showCameraPage">
			<view class="tab-head-smail" v-if="steps<=2">
				<text>第 {{steps+1}} 步（共3步）</text>
			</view>

			<!-- 头像 -->
			<view v-if="steps==0">
				<view class="tab-head">
					<text>拍摄/上传您本人的相片</text>
				</view>
				<view class="tab">
					<view class="img-upload"><img :src="headImg !== ''&&headImg !== null ? headImg : blankImg"
							@click="openUpload()" />
					</view>
				</view>

			</view>

			<!-- 身份证 -->
			<view v-if="steps==1">
				<!-- 身份证上传部分 -->
				<view>
					<view class="tab-head">
						<text>拍摄/上传您本人的二代身份证</text>
					</view>

					<view class="upload">
						<view class="upload-view">
							<img class="upload-img" :src="identityFront" @click="uploadImg(0)">
						</view>
					</view>

					<view class="upload">
						<view class="upload-view">
							<img class="upload-img" :src="identityBack" @click="uploadImg(1)">
						</view>
					</view>
				</view>

				<view v-if="!isShowUpload">
					<view class="tab-head">
						<text @click="isShowUpload=true">已经上传过啦，点击后可重新上传!</text>
					</view>
				</view>

				<view class="tab">
					<view class="tab-head">
						<text>身份证信息</text>
					</view>

					<view class="tab-head-smail">
						<text>姓名</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input">
							<input class="single-input" v-model="employee.realName" :placeholder="uploadTips"
								:disabled="isAllowInput?false:true">
							</input>
						</view>
					</view>

					<view class="tab-head-smail">
						<text>身份证号</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input">
							<input class="single-input" v-model="employeeInfo.idCard" :placeholder="uploadTips"
								disabled="true">
							</input>
						</view>
					</view>

					<view class="tab-head-smail">
						<text>身份证地址</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input">
							<input class="single-input" v-model="employeeInfo.hometown" :placeholder="uploadTips"
								disabled="true">
							</input>
						</view>
					</view>

					<view class="tab-head-smail">
						<text>有效期</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input">
							<input class="single-input" v-model="baomuInfo.idCardTime" :placeholder="uploadTips"
								disabled="true">
							</input>
						</view>
					</view>

					<view class="tab-head-smail">
						<text>生日</text>
					</view>
					<view class="tab-inputbox">
						<view class="tab-input">
							<input class="single-input" v-model="employeeInfo.birthTime" :placeholder="uploadTips"
								disabled="true">
							</input>
						</view>
					</view>

					<u-gap height="80"></u-gap>
				</view>
			</view>

			<!-- 健康证 -->
			<view v-if="steps==2">
				<view class="tab-head">
					<text>拍摄/上传您本人的健康证明</text>
				</view>
				<view class="tab-head">
					<text></text>
					<text style="margin-left: 0rpx;">小贴士：健康证或体检表，提交其一即可</text>
				</view>
				<view class="tab" style="display: flex; flex-direction: row;">
					<view class="img-upload-row"><text>健康证</text><img
							:src="healthImg !== ''&&healthImg !== null ? healthImg : blankImg"
							@click="popupShow1 = true" />
					</view>
					<view class="img-upload-row"><text>体检表</text><img
							:src="healthImg1 !== ''&&healthImg1 !== null ? healthImg1 : blankImg"
							@click="popupShow2 = true" />
					</view>
				</view>

				<view class="upload-tab">
					<text>体检时间</text>
					<text v-if="nowDate==validity" style="color: #909399;" @click="show = true">请选择&ensp;❯</text>
					<text v-if="nowDate!==validity" @click="show = true">{{ formatDate(validity) }}</text>
				</view>
			</view>

			<!-- 个人信息-旧版本的步骤-暂时隐藏 -->
			<view v-if="steps==30">
				<view>
					<view class="tab">
						<view class="tab-head"><text>姓名</text></view>
						<view class="tab-inputbox">
							<view class="tab-input"><input class="single-input" type="text" v-model="employee.realName"
									placeholder="暂未填写用户名" /></view>
						</view>
					</view>
				</view>

				<view>
					<view class="tab">
						<view class="tab-head">
							<text>手机号</text>
						</view>
						<view class="tab-inputbox">
							<view class="tab-input"><input class="single-input" type="number" v-model="employee.phone"
									placeholder="暂未填写手机号" /></view>
						</view>
					</view>
				</view>

				<view>
					<view class="tab">
						<view class="tab-head">
							<text>您的现居住地</text>
						</view>
						<view class="tab-inputbox">
							<view class="tab-input"><input class="single-input" type="text" v-model="employee.address"
									placeholder="暂未填写地址" /></view>
						</view>
					</view>
				</view>
			</view>

			<!-- 欢迎页 -->
			<view v-if="steps==3">
				<!-- <view class="img-upload"><img :src="introduceImg" mode="widthFix" style="width: 600rpx;height: auto;" />
				</view> -->

				<!-- 审核中 -->
				<view class="head-img"><img
						:src="employee.headPortrait !== ''&&employee.headPortrait !== null ? employee.headPortrait : blankImg" />
				</view>
				<view class="title">
					<view style="font-size: 40rpx;">
						<text>{{checkState()?'已通过审核':'审核中'}}</text>
					</view>
					<view style="font-size: 36rpx;color: #909399;">
						<text>姓名：{{checkStr(employee.realName)}}</text>
					</view>
					<view style="font-size: 36rpx;color: #909399;">
						<text>会员ID：{{memberId}}</text>
					</view>
				</view>

				<u-gap height="40"></u-gap>
				<view class="btn-bottom" v-if="!checkState()">
					<button @click="showProcess()">查看进度</button>
				</view>
				<u-gap height="10"></u-gap>
				<view class="btn-bottom" v-if="!checkState()">
					<button @click="openIntention()">先填写求职意向</button>
				</view>
				<u-gap height="10"></u-gap>
				<view class="btn-bottom">
					<button
						:style="checkState()?'background-color:#1e1848;color:#f6cc70':'background-color:#909399;color:#fff'"
						@click="openWork()">{{checkState()?'进入家姐联盟':'进入工作台'}}
					</button>
				</view>
			</view>


			<!-- 文字提示和保存按钮 -->
			<view style="margin-top: 0rpx;" v-if="!showCameraPage">
				<view style="display: flex; flex-direction: column; align-items: flex-end;">
					<img :src="arrowIcon" alt="" style="width: 80rpx; height: 80rpx;margin:0 100rpx 20rpx 0 ;" />
					<view class="f20 fb text-r lh20" style="color: deepskyblue;margin:0 20rpx 0 0;" @click="openChat"
						v-if="steps==0">
						派单老师咨询
					</view>
				</view>


				<view class="agreement cf262462" v-if="steps==0">
					<img @tap="agreement = !agreement"
						:src="agreement==true?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty1.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty0.png'"></img>
					<text @tap="agreement = !agreement"> 同意</text>
					<navigator @click="goview('https://agent.xiaoyujia.com/ServiceAgreement.html')">
						《服务协议》</navigator>
					<navigator @click="goview('https://agent.xiaoyujia.com/PrivacyAgreement.html')">
						《隐私协议》</navigator>
				</view>
				<view class="" v-if="steps<=2">
					<view class="upload-tips" @click="popupShowTips = true">
						<text>* 平台入驻须知</text>
					</view>
					<!-- 下一步按钮 -->
					<view class="btn-bottom" v-if="steps!=1"><button
							@click="nextSteps()">{{steps>=2?'完成信息认证':'下一步'}}</button>
					</view>
					<view class="btn-bottom-fixed" v-if="steps==1"><button
							@click="nextSteps()">{{steps>=2?'完成信息认证':'下一步'}}</button>
					</view>
				</view>
			</view>

			<view class="upload-tips flac-col" @click="openIntroducerTips">
				<view class="flac-row-c" style="height: 50rpx;" v-if="isInvited||!isLogin">
					<text class="red" v-if="checkOldIntroducer()">* 已被人邀请，邀请码：{{oldIntroducerId||'-'}}</text>
					<text v-if="!checkOldIntroducer()">邀请码：{{introducerId||'-'}}</text>
					<text v-if="introducerDto">（{{introducerDto.realName}}）</text>
				</view>
				<view class="flac-row-c" style="height: 50rpx;">
					<text v-if="introducerDto">门店：{{introducerDto.storeName||'小羽佳家政'}}</text>
					<text>（{{eType==0?'保姆月嫂':eType==1?'标准员工':'车辆司机'}}）</text>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	import {
		pathToBase64,
		base64ToPath
	} from '@/pages-mine/common/js/image-tools/index.js'
	import cameraPage from "@/pages-mine/common/components/cameraPage.vue"
	export default {
		components: {
			cameraPage
		},
		data() {
			return {
				// 可配置选项
				// 是否开启图片裁剪
				isOpenClipper: true,
				// 是否开启人脸识别相机
				isOpenCamera: true,
				// 开始时的步骤进度
				steps: 0,
				// 新员工入驻后通知的员工id
				noticeEmployeeId: 35659,
				// 当前是否是老员工入驻
				isOldEmployee: false,
				// 是否开启老员工入驻入口
				openOldEmployee: true,
				// 最大入驻年龄
				maxAge: 65,
				// 最小入驻年龄
				minAge: 18,
				// 日期可选择最小值
				minDate: Number(new Date().setYear(new Date().getFullYear() - 4)),

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				isLogin: false,
				show: false,
				showCameraPage: false,
				showClipper: false,
				popupShow: false,
				popupShow1: false,
				popupShow2: false,
				popupShowTips: false,
				memberId: null,
				baomuId: null,
				memberName: '',
				headImg: '',
				headPortraitUrl: null,
				healthImg: '',
				healthImgUrl: '',
				healthImg1: '',
				healthImgUrl1: '',
				blankImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1669101235017img-upload.png',
				healthUplpadTips: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/blank_health_img.png',
				healthUplpadTips1: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/blank_tijian_img.png',
				headUplpadTips: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/head_upload_tips.png",
				identityFront: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663226047582identity-front.png",
				identityBack: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1663224975448identity-back.png",
				introduceImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1670405016767introduce_img_1.png",
				logoImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png",
				arrowIcon: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon_arrow.png',
				nowDate: Number(new Date()),
				validity: Number(new Date()),
				fileFront: [],
				isInvited: false,
				isUploadSuccess: true,
				employeeState: -1,
				shimingState: false,
				agreement: false,
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				attentionList: [
					"1.上传信息仅用于身份验证，小羽佳保障您的信息安全！",
					"2.若需要入驻保姆/月嫂/育儿嫂等，根据平台规定，您的年龄需在55岁以下！",
					"3.若为邀请入驻，请确认好下方所显示的邀请人及入驻门店哦！"
				],
				stepsList: [{
						title: "上传头像"
					},
					{
						title: "身份认证"
					},
					{
						title: "健康认证"
					},
					// {
					// 	title: "个人信息"
					// }, 
					{
						title: "完成入驻"
					}
				],
				fileBack: [],
				memberId: null,
				baomuId: null,
				employeeId: null,
				isAllowInput: false,
				isAuthenticated: false,
				isShowUpload: true,
				uploadTips: "上传身份证自动识别",
				certificateList: [],
				eType: 0,
				introducerDto: null,
				introducerId: null,
				introducerId1: null,
				oldIntroducerId: null,
				oldIntroducerId1: null,
				birthTimeYear: 2023,
				idcard1: [{
					content: "",
					certificate: {
						title: "身份证正面",
						employeeId: this.baomuId,
						certificateType: 1,
						certificateImg: null,
						validity: null
					}
				}],
				idcard2: [{
					content: "",
					certificate: {
						title: "身份证反面",
						employeeId: this.baomuId,
						certificateType: 2,
						certificateImg: null,
						validity: null
					}
				}],
				certificate: {
					title: "健康证",
					employeeId: this.baomuId,
					certificateType: 3,
					certificateImg: null,
					validity: null
				},
				certificate1: {
					title: "体检表",
					employeeId: this.baomuId,
					certificateType: 8,
					certificateImg: null,
					validity: null
				},
				employeeList: [],
				employeeChoice: 0,
				// 员工
				employee: {
					id: this.baomuId,
					password: null,
					realName: null,
					phone: null,
					cityId: null,
					areaId: null,
					address: null,
					headPortrait: null,
					remark: null,
					updateDate: null,
					updatePerson: null,
					siteId: null,
					lsTime: null,
					leTime: null,
					baomuWorkType: null,
					score: null
				},
				// 员工详细信息
				employeeInfo: {
					employeeId: this.employeeId,
					sex: null,
					zodiac: 0,
					workingState: 0,
					religion: 0,
					languagenum: null,
					educationnum: null,
					birthTime: null,
					idCard: null,
					hometown: null,
					nation: null,
					education: null,
					married: null,
					tomarried: null,
					language: null,
					workYear: null,
					family: null,
				},
				baomuInfo: {
					baomuId: this.baomuId,
					workType: null,
					urgent: null,
					urgentPhone: null,
					urgentType: null,
					idCardTime: null,
					health: null,
					religion: null,
					zodiac: null,
					baomuId: null,
					updateTime: null,
					status: null,
					serverContent: null,
					otherSkills: null,
					introduce: null,
					languages: null,
					constellation: null,
				}
			};
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			showProcess() {
				this.openCheck(1, "请耐心等待吧！", "需要等待人工审核哦")
			},
			// 校验员工状态
			checkState() {
				if (this.isInvited) {
					return true
				}

				if (this.employeeState != -1) {
					return true
				}

				// 规避审核
				// if (this.employeeState != -1 && this.employeeState != 2) {
				// 	return true
				// }
				return false
			},
			// 格式化字符串
			checkStr(str) {
				if (str == null || str == "") {
					return "暂无用户名"
				} else {
					return str
				}
			},
			// 通过身份证号校验员工
			checkEmployeeByIdCard() {
				this.http({
					url: 'checkEmployeeByIdCard',
					method: 'POST',
					data: {
						idcard: this.employeeInfo.idCard
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.employeeList = res.data
							if (!this.isOldEmployee && this.openOldEmployee) {
								// this.openCheck(4, "系统检测到您可能是老员工", "要使用老员工入驻功能吗？")

								// 强制使用老员工入驻
								this.$toast.toast("系统检测到您可能是老员工，请使用老员工入驻！")
								this.isOldEmployee = true
							}
						} else {
							this.employeeList = []
							if (this.isOldEmployee) {
								this.openCheck(2, "查询不到相关员工！", "要切换为新员工入驻吗？")
							}
						}
					},
				})
			},
			// 选择旧员工进行注册
			choiceOldEmployee(index) {
				this.employeeChoice = index
				this.openCheck(3, "确定是本人吗？", "注册后将会更新旧资料的手机号！")
			},
			// 通过员工ID获取保姆关联信息（没有则进行初始化）
			getBaomuCollectByEmployeeId() {
				let employeeId = this.employeeList[this.employeeChoice].id
				this.http({
					url: 'getBaomuCollectByEmployeeId',
					method: 'POST',
					data: {
						memberId: this.memberId,
						id: employeeId,
						idcard: this.employeeInfo.idCard
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.isUploadSuccess = true
							this.baomuId = res.data.baomuId
							uni.setStorageSync("baomuId", this.baomuId)
							console.log('通过会员ID获取保姆关联信息-成功！')
							console.log("初始化的保姆ID为" + this.baomuId)
							uni.setStorageSync("isEmployee", true)
							uni.setStorageSync("isBaomu", true)
							uni.setStorageSync("employeeType", 10)

							this.getBaomuDetail()

							this.newEmployeeNotice()
							this.steps = 3
							this.isOldEmployee = false
							this.$toast.toast("老员工资料同步完成！欢迎入驻！")
						} else {
							this.isUploadSuccess = false
						}
					},
				})
			},
			openWork() {
				this.getMsgGrant()
				if (this.checkState()) {
					uni.reLaunch({
						url: "/pages-mine/index/index"
					})
				} else {
					this.$toast.toast("审核通过后才可进入哦！")
				}
			},
			openIntention() {
				uni.reLaunch({
					url: "/pages-mine/resume/intention"
				})
			},
			openIntroducerTips() {
				if (!this.introducerId) {
					return
				}
				this.openCheck(6, '欢迎入驻家姐联盟！', '入驻后会绑定对应的邀请人和门店哦！若已被他人邀请，则需等待三天以上才可继续邀请！')
			},
			goview(ur) {
				let param = {
					url: ur
				}
				let data = JSON.stringify(param)
				uni.navigateTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
				})
			},
			openChat() {
				let url = 'https://work.weixin.qq.com/kfid/kfc286c63c19f27e461'
				if (this.eType == 1) {
					url = 'https://work.weixin.qq.com/kfid/kfc49151a5f48007bbd'
				} else if (this.eType == 2) {
					url = 'https://work.weixin.qq.com/kfid/kfc5fcb162252650cb5'
				}
				// #ifdef MP-WEIXIN
				wx.openCustomerServiceChat({
					extInfo: {
						url: url //客服地址链接
					},
					corpId: 'wx25c9a236883d741d', //必须和你小程序上的一致
					success(res) {
						console.log(res, 1)
					},
					fail(res) {
						console.log(res, 2)
					},
				})
				// #endif
				// #ifdef  APP-PLUS || H5
				let param = {
					url: url
				}
				let data = JSON.stringify(param)
				uni.navigateTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
				})
				// #endif
			},
			// 点击下一步
			nextSteps() {
				let steps = this.steps
				this.isUploadSuccess = true
				// 校验上一步的信息是否填写完整，若无误则进入下一步
				switch (steps) {
					case 0:
						if (!this.checkLogin(1)) {
							break
						}

						if (!this.agreement) {
							this.$toast.toast("请先阅读服务协议并且同意哦！")
							break
						}

						if (uni.getStorageSync("merchantCode")) {
							this.$toast.toast("您当前是合伙人，请勿上传一线员工信息！")
							break
						}

						if (this.headPortraitUrl != null) {
							this.uploadHeadImg()
							this.$toast.toast("个人头像上传完成！")
							this.steps = 1
						} else {
							this.$toast.toast("个人头像还未上传哦！")
						}
						break;
					case 1:
						if (this.employeeInfo.idCard == null) {
							this.$toast.toast("身份证正面还未上传或无法识别！")
						} else if (this.baomuInfo.idCardTime == null) {
							this.$toast.toast("身份背面还未上传或无法识别！")
						} else if (this.overAge()) {

						} else {
							this.checkBaomuAndInit()
							let timer = setTimeout(() => {
								if (this.baomuId != null) {
									console.log("开始认证身份证信息！" + this.baomuId)
									this.employee.headPortrait = this.headPortraitUrl
									this.addCertificate(0)
									this.addCertificate(1)
									this.shimingState = true
									this.updateEmployee()
									this.updateEmployeeInfo()
									this.updateBaomuInfo()
									if (this.isUploadSuccess) {
										this.$toast.toast("身份证信息认证完成！")
										this.steps = 2
										// 新增-传完身份证即完成录入，即便后续步骤没完成
										uni.setStorageSync("employeeState", 2)
										uni.setStorageSync("isInvited", true)
									} else {
										this.$toast.toast("身份证信息认证失败！")
									}
								} else {
									console.log("更新身份证信息失败！" + this.baomuId)
								}
							}, 2000);

							// 标准单员工，直接流转
							if (this.eType >= 1) {
								let timer = setTimeout(() => {
									this.employeeToStaff()
								}, 4000);
							}
							console.log('成功入驻！')
						}
						break;
					case 2:
						if (this.healthImgUrl == "" && this.healthImgUrl1 == "") {
							this.openCheck(5, "是否跳过该步骤？", "健康证或体检表至少上传一个，若暂无证件或证件过期可暂时跳过，后续记得重新完善！")
						} else if (this.validity == this.nowDate) {
							this.$toast.toast("请填写体检时间！")
						} else {
							// 上传健康证或体检表
							if (this.healthImgUrl !== "") {
								this.addCertificate(2)
							}
							if (this.healthImgUrl1 !== "") {
								this.addCertificate(3)
							}
							let timer = setTimeout(() => {
								if (this.isUploadSuccess) {
									this.$toast.toast("健康证明上传成功！")
									this.completeStep()
								} else {
									this.$toast.toast("健康证明上传失败！")
								}
							}, 600);
						}
						break;
					case 3:
						this.openCheck(0, "恭喜您！个人信息已完善！", "欢迎入驻家姐联盟，去首页看看吧！")
						break;
				}
			},
			// 完成信息上传步骤
			completeStep() {
				uni.setStorageSync("employeeState", 2)
				uni.setStorageSync("isInvited", true)

				// 信息填写完成，同步更新一下简历分
				this.getBaomuDetail()
				this.newEmployeeNotice()
				this.steps = 3
			},
			// 年龄超出
			overAge() {
				// 除了保姆月嫂不限制年龄
				if (this.eType != 0) {
					return false
				}

				let nowDateYear = new Date().getFullYear()
				let age = nowDateYear - (this.birthTimeYear || 2023)
				console.log('入驻员工年龄：', age)
				if (age > this.maxAge) {
					this.$toast.toast("抱歉，您的年龄高于" + this.maxAge + "岁，依据平台规定，暂不符合入驻条件！")
					return true
				} else if (age < this.minAge && this.birthTimeYear != 2023) {
					this.$toast.toast("抱歉，您的年龄低于" + this.minAge + "岁，依据平台规定，暂不符合入驻条件！")
					return true
				}
				return false
			},
			// 校验个人信息是否填写完善
			checkInfo() {
				if (this.employee.realName == "" || this.employee.realName == null) {
					this.$toast.toast("请填写你的姓名哦！")
					return false
				}
				if (this.employee.phone == "" || this.employee.phone == null) {
					this.$toast.toast("请填写你的手机号哦！")
					return false
				}
				if (this.employee.address == "" || this.employee.address == null) {
					this.$toast.toast("请补充完整你的现居地哦！")
					return false
				}
				return true
			},
			closeCameraPage(flag) {
				console.log("关闭人脸相机！")
				this.showCameraPage = flag
			},
			// 提交照片
			submitCameraPhoto(tempFilePaths) {
				const url = 'https://api.xiaoyujia.com/system/imageUpload'
				uni.uploadFile({
					url: url,
					filePath: tempFilePaths[0],
					name: 'file',
					formData: {
						route: 'userPhotos'
					},
					dataType: 'json',
					success: res => {
						let result = JSON.parse(res.data)
						this.headImg = tempFilePaths[0]
						this.headPortraitUrl = result.data
						this.showCameraPage = false
					}
				});
			},
			// 打开头像上传
			openUpload() {
				if (!this.checkLogin(1)) {
					return
				}
				this.popupShow = true
			},
			// 上传证件
			uploadCertificate(index) {
				this.popupShow = false
				this.popupShow1 = false
				this.popupShow2 = false

				if (index == 4) {
					index = 0
				}
				// #ifdef  MP-WEIXIN
				// 上传头像，打开图片裁剪
				else if (index == 0 && this.isOpenCamera) {
					this.showCameraPage = true
					return
				}
				// #endif

				const url = 'https://api.xiaoyujia.com/system/imageUpload'
				uni.chooseImage({
					success: chooseImageRes => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {
								route: 'userPhotos',
								watermark: 'ture'
							},
							dataType: 'json',
							success: res => {
								let result = JSON.parse(res.data)
								if (index == 0) {
									this.headImg = tempFilePaths[0]
									this.headPortraitUrl = result.data
								} else if (index == 1) {
									this.healthImg = tempFilePaths[0]
									this.healthImgUrl = result.data
								} else if (index == 2) {
									this.healthImg1 = tempFilePaths[0]
									this.healthImgUrl1 = result.data
								}
							}
						});
					}
				});
			},
			// 上传头像
			uploadHeadImg() {
				this.isUploadSuccess = true
				// this.checkBaomuAndInit()
				// let timer = setTimeout(() => {
				// 	if (this.baomuId !== null) {
				// 		this.employee.headPortrait = this.headPortraitUrl
				// 		this.updateEmployee()
				// 	}
				// }, 1500);

				this.employee.headPortrait = this.headPortraitUrl
			},
			// 上传身份证图片
			uploadImg(value) {
				let url = "https://api.xiaoyujia.com/system/imageUpload"
				uni.chooseImage({
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths
						// 将图片上传至后台
						uni.uploadFile({
							url: url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {
								route: 'idCard'
							},
							dataType: 'json',
							success: res => {
								let result = JSON.parse(res.data)
								if (value == 0) {
									this.idcard1[0].certificate.certificateImg = result
										.data
								} else if (value == 1) {
									this.idcard2[0].certificate.certificateImg = result
										.data
								}
							}
						})
						// #ifdef  H5
						pathToBase64(tempFilePaths)
							.then(base64 => {
								if (value == 0) {
									this.identityFront = tempFilePaths[0]
									this.idcard1[0].content = base64
									this.vidcardNext()
								} else if (value == 1) {
									this.identityBack = tempFilePaths[0]
									this.idcard2[0].content = base64
									this.vidcard1Next()
								}
							})
							.catch(error => {
								console.error("转化失败！")
							})
						// #endif
						// #ifdef  MP-WEIXIN
						const base64 = this.urlTobase64(tempFilePaths[0])
						if (value == 0) {
							this.identityFront = tempFilePaths[0]
							this.idcard1[0].content = base64
							this.vidcardNext()
						} else if (value == 1) {
							this.identityBack = tempFilePaths[0]
							this.idcard2[0].content = base64
							this.vidcard1Next()
						}
						// #endif
					}
				});
			},
			// 图片格式转换
			urlTobase64(url) {
				const imgData = uni.getFileSystemManager().readFileSync(url, 'base64')
				const base64 = 'data:image/jpeg;base64,' + imgData
				return base64
			},
			// 检查识别的信息，若信息不全则无法更新身份证信息
			isIdCardInfoError() {
				let result = false
				if (this.employee.realName == "" || this.employeeInfo.idCard == "" || this.employeeInfo
					.birthTime == "" ||
					this
					.employeeInfo.hometown == "" || this.baomuInfo.zodiac == "") {
					result = true
				}
				return result
			},
			// 识别身份证正面
			vidcardNext() {
				let url = "https://api.xiaoyujia.com/acn/imgsToIdInfo"
				// 请求：识别身份证
				let data = {
					file: this.idcard1[0].content,
					cardTpe: 0
				}
				uni.request({
					url: url,
					method: 'POST',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: JSON.stringify(data),
					success: res => {
						let status = res.data.image_status;
						if (status === 'normal') {
							this.$toast.toast("身份证正面上传成功！")
							this.employee.realName = res.data.words_result.姓名.words
							this.employeeInfo.idCard = res.data.words_result.公民身份号码.words
							this.employeeInfo.birthTime = this.getMyTime(res.data.words_result.出生
								.words)
							this.employeeInfo.hometown = res.data.words_result.住址.words
							this.baomuInfo.zodiac = this.getshengxiao(res.data.words_result.出生.words
								.substring(
									0, 4))
							this.baomuInfo.constellation = this.getConstellation(res.data.words_result.出生
								.words)
							this.employeeInfo.nation = res.data.words_result.民族.words
							this.employeeInfo.sex = this.getSex(res.data.words_result.性别.words)
							this.checkEmployeeByIdCard()
						} else if (status === 'unknown' && res.data.idcard_number_type === 0) {
							this.employee.realName = res.data.words_result.姓名.words
							this.employeeInfo.idCard = res.data.words_result.公民身份号码.words
							this.employeeInfo.birthTime = this.getMyTime(res.data.words_result.出生
								.words)
							this.employeeInfo.hometown = res.data.words_result.住址.words
							this.baomuInfo.zodiac = this.getshengxiao(res.data.words_result.出生.words
								.substring(
									0, 4))
							this.baomuInfo.constellation = this.getConstellation(res.data.words_result.出生
								.words)
							this.employeeInfo.nation = res.data.words_result.民族.words
							this.employeeInfo.sex = this.getSex(res.data.words_result.性别.words)
							this.isAllowInput = true
							this.$toast.toast("因第三方接口不识别，请自行输入身份证号码！")
						} else {
							let msg = '身份证认证失败，请重新上传';
							if (status != null) {
								if (status === 'reversed_side') {
									msg = '身份证应上传照片面'
								}
								if (status === 'non_idcard') {
									msg = '上传的图片中不包含身份证'
								}
								if (status === 'blurred') {
									msg = '身份证模糊'
								}
								if (status === 'other_type_card') {
									msg = '其他类型证照'
								}
								if (status === 'over_exposure') {
									msg = '身份证关键字段反光或过曝'
								}
								if (status === 'over_dark') {
									msg = '身份证欠曝（亮度过低）'
								}
							}
							this.idcard1[0].content = ""
							this.$toast.toast(msg)
						}

						if (this.employeeInfo.birthTime && this.employeeInfo.birthTime.length > 8) {
							this.birthTimeYear = this.employeeInfo.birthTime.substring(0, 4) || 2023
							this.birthTimeYear = parseInt(this.birthTimeYear)
						}
					},
					fail: err => {
						console.log('兑换奖品-请求失败！' + res.data.code)
					}
				})
			},
			// 识别身份证背面
			vidcard1Next() {
				let url = "https://api.xiaoyujia.com/acn/imgsToIdInfo"
				let data = {
					file: this.idcard2[0].content,
					cardTpe: 1
				}
				uni.request({
					url: url,
					method: 'POST',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					data: JSON.stringify(data),
					success: res => {
						let status = res.data.image_status
						if (status === 'normal') {
							this.$toast.toast("身份证背面上传成功！")
							this.baomuInfo.idCardTime = this.getMyTime(res.data.words_result.失效日期
								.words)
						} else {
							this.idcard2[0].content = ""
							this.$toast.toast("身份证背面认证失败，请检查有效期限后请重新上传")
						}
					},
					fail: err => {
						console.log('兑换奖品-请求失败！' + res.data.code)
					}
				})
			},
			// 获取星座
			getConstellation(dateStr) {
				let constellation = ''
				// 使用正则表达式检查日期格式
				if (!dateStr || !/^\d{4}\d{2}\d{2}$/.test(dateStr)) {
					return constellation
				}
				// 解析年月日
				const year = parseInt(dateStr.substring(0, 4));
				const month = parseInt(dateStr.substring(4, 6));
				const day = parseInt(dateStr.substring(6, 8));
				// 根据日期计算星座
				if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) {
					constellation = '水瓶座';
				} else if ((month === 2 && day >= 19) || (month === 3 && day <= 20)) {
					constellation = '双鱼座';
				} else if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) {
					constellation = '白羊座';
				} else if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) {
					constellation = '金牛座';
				} else if ((month === 5 && day >= 21) || (month === 6 && day <= 21)) {
					constellation = '双子座';
				} else if ((month === 6 && day >= 22) || (month === 7 && day <= 22)) {
					constellation = '巨蟹座';
				} else if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) {
					constellation = '狮子座';
				} else if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) {
					constellation = '处女座';
				} else if ((month === 9 && day >= 23) || (month === 10 && day <= 23)) {
					constellation = '天秤座';
				} else if ((month === 10 && day >= 24) || (month === 11 && day <= 22)) {
					constellation = '天蝎座';
				} else if ((month === 11 && day >= 23) || (month === 12 && day <= 21)) {
					constellation = '射手座';
				} else if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) {
					constellation = '摩羯座';
				} else {
					constellation = '';
				}
				return constellation
			},
			getshengxiao(yyyy) {
				var arr = ['猴', '鸡', '狗', '猪', '鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊']
				return /^\d{4}$/.test(yyyy) ? arr[yyyy % 12] : null
			},
			getMyTime(time) {
				let dateTime = time.substring(0, 4) + "-" + time.substring(4, 6) + "-" + time.substring(6, 8)
				if (time.includes("长期")) {
					dateTime = "长期有效"
				}
				return dateTime
			},
			getMyTime1(time) {
				let dateTime = time.substring(0, 4) + "-" + time.substring(5, 7) + "-" + time.substring(8, 10)
				if (time.includes("3000-01-01")) {
					dateTime = "长期有效"
				}
				return dateTime
			},
			getMyTime2(time) {
				if (time.includes("长期")) {
					return "3000/01/01"
				} else {
					let dateTime = time.substring(0, 4) + "/" + time.substring(5, 7) + "/" + time.substring(8, 10)
					return dateTime
				}
			},
			getSex(sex) {
				if (sex == "男") {
					return 1
				} else if (sex == "女") {
					return 2
				} else {
					return 3
				}
			},
			// 时间格式化
			formatDate(value) {
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '/' + MM + '/' + d
			},
			getLocation() {
				//#ifdef  MP-WEIXIN || APP-PLUS || H5
				let that = this;
				uni.getLocation({
					type: 'gcj02',
					isHighAccuracy: true,
					geocode: true,
					success: res => {
						console.log("定位调用成功")
						console.log('纬度：' + res.latitude);
						console.log('经度：' + res.longitude)
						console.log("（高精度）当前的纬度：", res.latitude, "当前的经度", res.longitude)
						uni.setStorageSync("lat", res.latitude)
						uni.setStorageSync("lng", res.longitude)
						that.getNearStore()
					},
					fail: err => {

					},
				})
				// #endif

				//#ifdef  H5
				// let url = "https://apis.map.qq.com/ws/location/v1/ip";
				// this.$jsonp(url, {
				// 		key: 'RNRBZ-ZMZ6R-BSVWC-WHRJI-SACIT-VLBVI',
				// 		output: 'jsonp'
				// 	})
				// 	.then(res => {
				// 		// 纬度和经度
				// 		let lat = res.result.location.lat
				// 		let lng = res.result.location.lng
				// 		console.log("当前的纬度：", lat, "当前的经度", lng)
				// 		uni.setStorageSync("lat", lat)
				// 		uni.setStorageSync("lng", lng)
				// 		this.getNearStore()
				// 	})
				// 	.catch(err => {
				// 		console.log(err);
				// 	});
				// #endif
			},
			// 获取最近的自营门店
			getNearStore() {
				let lng = parseFloat(uni.getStorageSync("lng"))
				let lat = parseFloat(uni.getStorageSync("lat"))
				console.log("（传给后端接口的定位）当前的纬度：", lat, "当前的经度", lng)
				this.http({
					url: 'getNearStore',
					method: 'POST',
					hideLoading: true,
					data: {
						lng: lng,
						lat: lat,
						district: 3
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							let storeId = res.data.storeId
							uni.setStorageSync("nearStoreId", storeId)
						} else {
							uni.setStorageSync("nearStoreId", -1)
						}
					},
				})
			},
			// 更新证件图片
			addCertificate(index) {
				let data = []
				if (index == 0) {
					this.$set(this.idcard1[0].certificate, 'employeeId', this.baomuId)
					this.idcard1[0].certificate.validity = this.getMyTime2(this.baomuInfo.idCardTime)
					data = this.idcard1[0].certificate
				} else if (index == 1) {
					this.$set(this.idcard2[0].certificate, 'employeeId', this.baomuId)
					this.idcard2[0].certificate.validity = this.getMyTime2(this.baomuInfo.idCardTime)
					data = this.idcard2[0].certificate
				} else if (index == 2) {
					this.$set(this.certificate, 'employeeId', this.baomuId)
					this.certificate.certificateImg = this.healthImgUrl
					this.certificate.validity = this.formatDate(this.validity)
					data = this.certificate
				} else if (index == 3) {
					this.$set(this.certificate1, 'employeeId', this.baomuId)
					this.certificate1.certificateImg = this.healthImgUrl1
					this.certificate1.validity = this.formatDate(this.validity)
					data = this.certificate1
				}
				this.http({
					url: 'addCertificate',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: data,
					success: res => {
						if (res.code == 0) {
							// this.$toast.toast("证件上传成功！")
						} else {
							this.$toast.toast("证件上传失败！" + res.msg)
							// this.isUploadSuccess = false
						}
					}
				})
			},
			// 更新员工
			updateEmployee() {
				this.$set(this.employee, 'id', this.baomuId)
				// 同步更新实名认证状态
				if (this.shimingState) {
					this.employee.shimingState = 1
				}
				this.http({
					url: 'updateBaomu',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: this.employee,
					success: res => {
						if (res.code == 0) {
							if (res.data) {
								uni.setStorageSync('emeployHeadImg', this.employee.headPortrait)
							} else {
								// this.isUploadSuccess = false
							}
						} else {
							// this.isUploadSuccess = false
							this.$toast.toast('个人头像上传失败，请求错误！' + res.msg)
						}
					}
				});
			},
			// 更新员工详细信息
			updateEmployeeInfo() {
				this.$set(this.employeeInfo, 'employeeId', this.baomuId)
				this.http({
					url: 'updateEmployeeInfo',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: this.employeeInfo,
					success: res => {
						if (res.code == 0) {
							if (this.employeeInfo.idCard !== null) {
								uni.setStorageSync("isAuthenticated", true)
							}
						} else {
							// this.isUploadSuccess = false
							this.$toast.toast('更新员工详细信息失败，请求错误！' + res.msg)
						}
					},
				})
			},
			// 更新保姆信息
			updateBaomuInfo() {
				this.$set(this.baomuInfo, 'baomuId', this.baomuId)
				let idCardTime = this.baomuInfo.idCardTime
				if (idCardTime.includes("长期")) {
					this.baomuInfo.idCardTime = "3000-01-01" + " 00:00:00.000"
				} else {
					this.baomuInfo.idCardTime = idCardTime + " 00:00:00.000"
				}
				this.http({
					url: 'updateBaomuInfo',
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					method: 'POST',
					hideLoading: true,
					data: this.baomuInfo,
					success: res => {
						this.baomuInfo.idCardTime = idCardTime
						if (res.code == 0) {

						} else {
							// this.isUploadSuccess = false
							this.$toast.toast('更新保姆信息失败，请求错误！' + res.msg)
						}
					},
				})
			},
			// 获取员工已经上传的证件
			getCertificateByEmployeeId() {
				if (this.baomuId !== null) {
					this.http({
						url: 'getCertificateByEmployeeId',
						method: 'GET',
						hideLoading: true,
						path: this.baomuId,
						hideLoading: true,
						success: res => {
							if (res.code == 0) {
								this.certificateList = res.data
								// 格式化证件数据，显示已上传的身份证正反面
								for (let item of this.certificateList) {
									let certificateType = item.certificateType
									if (certificateType == 1) {
										this.identityFront = item.certificateImg
									} else if (certificateType == 2) {
										this.identityBack = item.certificateImg
									}
								}
							}
						},
					});
				}
			},
			// 获取订阅消息授权
			getMsgGrant() {
				// #ifdef  MP-WEIXIN
				console.log("小程序订阅消息授权！")
				wx.requestSubscribeMessage({
					tmplIds: ["XSPkvv4X5gph2Pny0dzeFz7Qwt2mffO6q6yzzAARI-w"],
					success: res => {
						console.log("用户同意进行小程序消息订阅！")
						// this.$toast.toast('通过技能鉴定后会收到通知哦！请耐心等待吧！')
					},
					fail: res => {}
				})
				// #endif
			},
			// 新员工入驻，发送企微通知给鉴定师
			newEmployeeNotice() {
				this.http({
					url: 'newEmployeeNotice',
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					data: {
						employeeId: this.noticeEmployeeId,
						msgType: "news",
						title: "有新的员工扫码入驻联盟啦！",
						description: "您好！家姐联盟有新的员工扫码入驻啦，请打开联盟进行工作技能鉴定和上架吧！",
						url: "http://jiajie.xiaoyujia.com/#/pages-other/employee/employee-appraisal",
						picUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671158255939192x192.png",
						// appId: "wx8342ef8b403dec4e",
						// pagepath: "pages-other/employee/workSkill"
					},
					success: res => {
						if (res.code == 0) {
							console.log('员工入驻-企微消息推送成功！')
						} else {
							console.log('员工入驻-企微消息推送失败！' + res.msg)
						}
					}
				})
			},
			// 获取保姆详细信息成功
			getBaomuDetail() {
				if (this.baomuId !== null) {
					this.http({
						url: 'getBaomuDetail',
						method: 'GET',
						hideLoading: true,
						path: this.baomuId,
						hideLoading: true,
						success: res => {
							if (res.code == 0) {
								let baomuDetail = res.data
								// 获取员工信息
								let realName = this.employee.realName
								this.employee = baomuDetail.employee
								if (realName != null) {
									this.employee.realName = realName
								}

								// 尝试取出身份证信息
								if (baomuDetail.employeeInfo.idCard != null) {
									this.getCertificateByEmployeeId()
								}

								// 获取员工详细信息
								if (baomuDetail.employeeInfo.idCard != null && this.employeeInfo.idCard ==
									null) {
									this.employeeInfo = baomuDetail.employeeInfo
								}

								// 获取保姆详细
								if (baomuDetail.baomuInfo.idCardTime != null) {
									this.baomuInfo = baomuDetail.baomuInfo
									this.baomuInfo.idCardTime = this.getMyTime1(this.baomuInfo.idCardTime)
								}

								console.log('获取保姆详细信息成功-请求成功！')
							} else {
								// this.$toast.toast('获取保姆详细信息失败，请求错误！' + res.msg)
							}
						}
					});
				}
			},
			// 判断是否存在保姆信息（不存在则初始化）
			checkBaomuAndInit() {
				let nearStoreId = uni.getStorageSync("nearStoreId") || -1
				if (this.baomuId == null) {
					this.http({
						url: 'getBaomuCollectByMemberId',
						method: 'POST',
						data: {
							memberId: this.memberId,
							nearStoreId: nearStoreId
						},
						success: res => {
							if (res.code == 0) {
								this.isUploadSuccess = true
								this.baomuId = res.data.baomuId
								uni.setStorageSync("baomuId", this.baomuId)
								uni.setStorageSync('employeeId', this.baomuId)
								uni.setStorageSync("isEmployee", true)
								uni.setStorageSync("isBaomu", true)
								this.getBaomuDetail()
							} else {
								this.isUploadSuccess = false
								this.$toast.toast('初始化员工信息失败，请稍候再试！' + res.msg)
							}
						},
						fail: err => {
							console.log('通过会员ID获取保姆关联信息-请求失败！' + res.code)
						}
					})
				}
			},
			// 员工流转
			employeeToStaff() {
				let id = this.baomuId || null
				this.http({
					url: 'employeeToStaff',
					method: 'POST',
					hideLoading: true,
					data: {
						id: id,
						realName: "邀请入驻->流转"
					},
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {

						}
					}
				});
			},
			// 添加邀请信息
			addMemberInvitation() {
				if (!this.memberId) {
					return
				}
				this.http({
					url: 'addMemberInvitation',
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.introducerId,
						inviteeId: this.memberId,
						employeeId: this.introducerId1 || null,
					},
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {},
				})
			},
			getEmployeeDtoById(id) {
				this.http({
					url: 'getEmployeeDtoById',
					method: 'GET',
					hideLoading: true,
					path: id || 0,
					success: res => {
						if (res.code == 0) {
							this.introducerDto = res.data
						}
					}
				});
			},
			calculateDaysBetween(value) {
				let diffDays = 999
				if (!value) {
					return diffDays
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				const startDate = new Date(value)
				const endDate = new Date()
				const timeDiff = Math.abs(endDate - startDate)
				diffDays = Math.ceil(timeDiff / (1000 * 60 * 60 * 24))
				return diffDays
			},
			// 获取邀请人信息
			getIntroducer() {
				if (!this.introducerId) {
					return
				}
				this.http({
					url: 'getMemberInvitation',
					method: 'POST',
					hideLoading: true,
					data: {
						inviteeId: this.memberId || -1,
					},
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					success: res => {
						if (res.code == 0) {
							let data = res.data[0].createTime
							if (this.calculateDaysBetween(data) <= 3) {
								this.oldIntroducerId = res.data[0].memberId || null
								this.oldIntroducerId1 = res.data[0].employeeId || null
								this.getEmployeeDtoById(this.oldIntroducerId1)
							} else {
								this.addMemberInvitation()
								this.getEmployeeDtoById(this.introducerId1)
							}
						} else {
							this.addMemberInvitation()
							this.getEmployeeDtoById(this.introducerId1)
						}
					},
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：跳转到联盟首页
				if (this.checkType == 0) {
					uni.reLaunch({
						url: "/pages/index/index"
					})
				} else if (this.checkType == 1) {

				} else if (this.checkType == 2) {
					this.isOldEmployee = false
				} else if (this.checkType == 3) {
					this.getBaomuCollectByEmployeeId()
				} else if (this.checkType == 4) {
					this.isOldEmployee = true
				} else if (this.checkType == 5) {
					// 跳过健康证上传
					this.completeStep()
				}
			},
			initInfo() {
				this.getBaomuDetail()
				this.getLocation()
			},
			// 校验是否已被别人邀请
			checkOldIntroducer() {
				return (this.oldIntroducerId && this.introducerId != this.oldIntroducerId)
			},
			// 登录状态检查
			checkLogin(value) {
				if (!uni.getStorageSync('memberId')) {
					if (value == 1) {
						this.$toast.toast('您还未进行登录哦，先去登录吧！')
						let url = '/pages-mine/resume/resume-simplify'
						if (this.introducerId) {
							url += '?introducerId=' + this.introducerId + '&introducerId1=' + this.introducerId1 +
								'&eType=' + this.eType
						}
						uni.setStorageSync('redirectUrl', url)
						setTimeout(() => {
							uni.redirectTo({
								url: '/pages-mine/login/login'
							});
						}, 2000);
					}
					return false
				} else {
					this.memberId = uni.getStorageSync('memberId') || null
					this.baomuId = uni.getStorageSync('employeeId') || null
					this.initInfo()
					this.isLogin = true
					return true
				}
			}
		},
		onLoad(options) {
			// 尝试获取邀请员工信息
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				let introducerId = obj.id || null
				let introducerId1 = obj.id1 || null
				let eType = obj.t || 0
				console.log("邀请会员ID：", introducerId, "邀请人员工ID：" + introducerId1)
				console.log("员工类型：", eType)

				uni.setStorageSync('introducerId', introducerId)
				uni.setStorageSync('introducerId1', introducerId1)

				this.introducerId = parseInt(introducerId)
				this.introducerId1 = introducerId1
				this.eType = parseInt(eType)
			} else {
				console.log("未获取邀请人信息！")
			}
			this.introducerId = options.introducerId ? parseInt(options.introducerId) : this.introducerId
			this.introducerId1 = options.introducerId1 ? parseInt(options.introducerId1) : this.introducerId1
			this.eType = options.eType ? parseInt(options.eType) : this.eType
			// 标准单员工打开老员工校验
			if (this.eType >= 1) {
				this.openOldEmployee = true
			}
			// 标记为扫描邀请二维码进入
			if (this.introducerId) {
				this.isInvited = true
				uni.setStorageSync("isInvited", true)
			}
		},
		mounted() {
			this.checkLogin(0)
			this.getIntroducer()
			if (uni.getStorageSync("employeeState") !== undefined) {
				this.employeeState = uni.getStorageSync("employeeState")
			}
			// 已经上传过证件的员工，直接跳到第三步
			if (this.employeeState == 2 || this.employeeState == 1) {
				this.steps = 3
			}
		}
	};
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/resume-tab.scss";

	page {
		height: 100%;
		background-color: #ffffff;
	}

	.logo {
		width: 100%;
		height: auto;

		img {
			display: block;
			width: 120rpx;
			height: auto;
			margin: 20rpx auto;
		}
	}

	.steps {
		width: 100%;
		padding: 50rpx 0;
	}

	// 提示弹窗标题
	.popup-title {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;

		text {
			display: block;
			text-align: center;
			font-weight: bold;
			font-size: 40rpx;
		}
	}

	// 提示图片
	.popup-img {
		width: 100%;
		height: auto;

		img {
			display: block;
			width: 600rpx;
			height: auto;
			margin: 40rpx auto 0 auto;
		}
	}

	// 上传提示
	.popup-tips {
		width: 80%;
		line-height: 60rpx;
		margin: 20rpx auto 0 auto;

		text {
			display: block;
			font-size: 36rpx;
			text-align: center;
		}
	}

	.img-upload {
		width: 100%;
		height: 600rpx;

		img {
			display: block;
			width: 400rpx;
			height: 600rpx;
			margin: 80rpx auto;
			border-radius: 10rpx;
		}
	}

	.img-upload-row {
		width: 50%;
		height: 400rpx;

		text {
			display: block;
			font-size: 36rpx;
			margin-left: 40rpx;
			line-height: 80rpx;
		}

		img {
			display: block;
			width: 320rpx;
			height: 240rpx;
			margin: 10rpx auto;
			border-radius: 10rpx;
		}
	}

	.upload {
		margin-bottom: 60rpx;
		width: 100%;
	}

	.upload-view {
		width: 620rpx;
		height: 400rpx;
		margin: 0rpx auto;
	}

	.upload-img {
		width: 620rpx;
		height: 400rpx;
		border-radius: 20rpx;
	}

	// 上传选择栏目
	.upload-tab {
		height: 120rpx;
		width: 100%;
		border-bottom: #f4f4f5 2px solid;
		background-color: #ffffff;

		text {
			display: block;
			line-height: 120rpx;
			height: 100%;
			font-size: 36rpx;
		}

		text:first-child {
			float: left;
			width: 260rpx;
			padding-left: 40rpx;
			font-weight: bold;
		}

		text:nth-child(2) {
			float: right;
			padding-right: 40rpx;
			color: #909399;
		}

	}

	.agreement {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		margin-top: 80rpx;
		text-align: center;
		height: 40rpx;
		line-height: 40rpx;

		img {
			width: 40rpx;
			height: 40rpx;
		}
	}

	.upload-tips {
		height: 100rpx;
		width: 100%;
		bottom: 200rpx;

		text {
			display: block;
			font-size: 32rpx;
			color: #909399;
			text-align: center;
			height: 100rpx;
			line-height: 100rpx;
		}
	}

	.head-img {
		width: 100%;
		height: auto;

		img {
			margin: 200rpx auto 60rpx auto;
			display: block;
			width: 200rpx;
			height: 200rpx;
			border-radius: 50%;
		}
	}

	.title {
		width: 100%;
		height: auto;
		margin: 0 auto;
		text-align: center;

		text {
			line-height: 60rpx;
		}
	}

	// 小按钮
	.btn-small {
		width: 100%;
		height: 50rpx;
		margin-top: 20rpx;

		button {
			padding: 0;
			float: right;
			width: 200rpx;
			line-height: 50rpx;
			font-size: 32rpx;
			margin-right: 20rpx;
			border-radius: 40rpx;
			color: #f6cc70;
			background-color: #1e1848;
		}
	}

	.btn-middle {
		width: 100%;
		height: 50rpx;

		button {
			padding: 0;
			width: 180rpx;
			line-height: 60rpx;
			height: 60rpx;
			font-size: 32rpx;
			border-radius: 30rpx;
		}
	}

	// 证件照片
	.cer-content {
		width: 50%;
		height: auto;
		padding: 30rpx 0;

		img {
			display: block;
			width: 200rpx;
			height: 200rpx;
			margin: 0 auto;
		}

		text {
			display: block;
			text-align: center;
			font-size: 36rpx;
			line-height: 80rpx;
		}
	}

	// 筛选组件标题
	.filter-title {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
		padding: 0 40%;

	}

	// 筛选组件内容
	.filter-content {
		width: 100%;
		height: 800rpx;
	}

	// 弹框组件文本
	.filter-content-text {
		width: 90%;
		padding: 0 5%;

		text {
			display: block;
			line-height: 60rpx;
			height: auto;
			font-size: 36rpx;
		}
	}
</style>