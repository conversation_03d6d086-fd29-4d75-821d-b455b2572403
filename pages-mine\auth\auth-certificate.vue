<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<web-view :webview-styles="webviewStyles" :src="webUrl" v-if="viewShow"></web-view>

		<scroll-view class="scrollContainer" :scroll-x="true">
			<img :src="certImg" alt="" mode="widthFix" />
		</scroll-view>
		<button class="btnStyle" @click="downloadImg()">证书下载</button>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				returnTime: 8000,

				viewShow: true,
				webUrl: '',
				webviewStyles: {
					progress: {
						color: '#FF3333'
					}
				},

				employeeId: null,
				authReport: null,
				certImg: ''
			}
		},
		methods: {
			downloadImg() {
				// #ifdef H5
				uni.setClipboardData({
					data: this.certImg,
					success: () => {
						this.$refs.uNotify.success('已复制下载链接!请粘贴至浏览器下载！')
					}
				})
				// #endif
				// #ifndef H5
				uni.downloadFile({
					url: this.certImg,
					success: (res) => {
						var imageUrl = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: imageUrl,
							success: (res) => {
								this.$refs.uNotify.success('证书已保存！')
							},
							fail: (err) => {
								this.$refs.uNotify.error('证书保存失败！', err)
							}
						})
					}
				})
				// #endif
			},
			// 获取鉴定报告
			getAuthReport() {
				if (this.employeeId && !this.certImg) {
					this.http({
						url: 'getAuthReport',
						method: 'GET',
						hideLoading: true,
						path: this.employeeId,
						success: res => {
							if (res.code == 0) {
								this.authReport = res.data
								if (this.authReport.certImg) {
									this.certImg = this.authReport.certImg
									this.viewShow = false
								} else {
									this.openAuthView()
								}
							}
						},
					});
				}
			},
			openAuthView() {
				let url = "https://pv.xiaoyujia.com/pages-mine/auth/auth-certificate?id=" + this.employeeId
				this.webUrl = url
				let timer = setTimeout(() => {
					this.viewShow = false
					this.getAuthReport()
				}, this.returnTime);
			},
			checkLogin() {
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.error('您还未进行登录哦，先去登录吧！')
					uni.setStorageSync('redirectUrl', "/pages-mine/auth/auth-certificate?id=" + this.employeeId)
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						});
					}, 2000);
				} else {
					this.getAuthReport()
				}
			},
		},
		onShow() {
			this.checkLogin()
		},
		mounted() {

		},
		onLoad(options) {
			this.employeeId = options.id || uni.getStorageSync("employeeId") || null

		}
	}
</script>
<style lang="scss" scoped>
	.scrollContainer {
		width: 100%;
		height: 100%;
		margin: auto;
		margin: 40rpx auto;
		white-space: nowrap;
		display: inline-block;
	}

	.btnStyle {
		border-radius: 0;
	}
</style>