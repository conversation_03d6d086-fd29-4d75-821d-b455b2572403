import {
	requestUtil
} from "../util/requestUtil.js"

/**
 * 支付
 * @param {Object} postParam
 */
export const getWxMiniProgramPayInfo = function(postParam) {
	requestUtil({
		url: '/pays/getWxMiniProgramPayInfo',
		method: 'POST',
		data: postParam.data,
		success: postParam.onSuccess,
		fail: postParam.onFail,
		complete: postParam.onComplate
	})
}

/**
 * 线索创建
 * @param {Object} postParam
 */
export const addOrderNeedsByUnpaid = function(postParam) {
	requestUtil({
		url: '/order/addOrderNeedsByUnpaid',
		method: 'POST',
		data: postParam.data,
		success: postParam.onSuccess,
		fail: postParam.onFail,
		complete: postParam.onComplate
	})
}

/**
 * 已支付，线索状态变更
 * @param {Object} postParam
 */
export const needsPayCallBack = function(postParam) {
	requestUtil({
		url: '/order/needsPayCallBack',
		method: 'POST',
		data: postParam.data,
		success: postParam.onSuccess,
		fail: postParam.onFail,
		complete: postParam.onComplate
	})
}

/**
 * 会员订单列表
 * @param {Object} postParam
 */
export const getMemberOrderList = function(postParam) {
	requestUtil({
		url: '/order/getMemberOrderList',
		method: 'POST',
		data: postParam.data,
		success: postParam.onSuccess,
		fail: postParam.onFail,
		complete: postParam.onComplate
	})
}

/**
 * 取消订单
 * @param {Object} postParam
 */
export const memberRefund = function(postParam) {
	requestUtil({
		url: '/order/memberRefund',
		method: 'POST',
		data: postParam.data,
		success: postParam.onSuccess,
		fail: postParam.onFail,
		complete: postParam.onComplate
	})
}

/**
 * 取消原因
 * @param {Object} postParam
 */
export const getCancelReason = function(postParam) {
	requestUtil({
		url: '/order/getCancelReason',
		method: 'GET',
		success: postParam.onSuccess,
		complete: postParam.onComplate
	})
}

/**
 *	再次续约
 * @param {Object} getParam
 */
export const renewAddOrderNeeds = function(getParam) {
	requestUtil({
		url: '/order/renewAddOrderNeeds?billNo=' + getParam.data,
		method: 'GET',
		success: getParam.onSuccess,
		complete: getParam.onComplate
	})
}

/**
 * 获取客服二维码
 * @param {Object} postParam billNo
 */
export const getKfQrCodeByBillNo = function(postParam) {
	requestUtil({
		url: '/order/getKfQrCodeByBillNo/' + postParam.data,
		method: 'GET',
		success: postParam.onSuccess,
		complete: postParam.onComplate
	})
}

