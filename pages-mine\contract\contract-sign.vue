<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<u-popup :show="popupQRImg" @close="popupQRImg=false" mode="center">
			<img :src="qrImg" style="display: block;width: 600rpx;height: auto;" mode="widthFix" @click="saveToPhone" />
		</u-popup>

		<!-- 合同信息 -->
		<view v-if="contract" style="margin: 20rpx 20rpx;">
			<view class="swiper-head">
				<text class="swiper-title flac-row">
					<text @click="copyText(contractNo)">合同号：{{contractNo||'-'}}</text>
					<uni-icons type="scan" size="24" style="margin-left: 10rpx" @click="createQRImg"></uni-icons>
				</text>
				<text class="swiper-tips">{{getContractStatus(contract.status)}}</text>
			</view>
			<view class="swiper-content">
				<view class="content-left">
					<img :src="formatShowImg()" alt="" @click="openImgPreview(formatShowImg())">
				</view>
				<view class="content-right">
					<view class="content-title">
						<text>{{formatProductName()}}</text>
					</view>
					<view class="content-text">
						<text>{{contract.serviceContent||''}}</text>
					</view>
					<view class="content-text">
						<text>甲方：{{contract.memberName||'匿名客户'}}</text>
					</view>
					<view class="content-text">
						<text>乙方：{{contract.employeeName}}</text>
					</view>
					<view class="content-text">
						<text>经纪人：{{contract.agentName}}</text>
					</view>
					<view class="flac-row content-text">
						<text>工资：</text><text style="color: #ff4d4b;">{{contract.servicePay}}元</text>
					</view>
					<view class="content-text">
						<text>时间：{{contract.serviceStarDate}}-{{contract.serviceEndDate}}</text>
					</view>
					<view class="content-text">
						<text>地址：{{contract.memberAdress}}</text>
					</view>
					<u-gap height="20"></u-gap>
				</view>
			</view>
			<view class="flac-row-c">
				<u-button customStyle="width:30%;" plain text="联系客服" color="#1e1848" v-if="contract.billNo"
					@click="getKfQrCodeByBillNo(contract.billNo)" />
				<u-button customStyle="width:30%;" plain text="再次续约" color="#1e1848" v-if="contract.signState == 1"
					@click="showPrompts()">
				</u-button>
				
				
				<!-- <u-button customStyle="width:30%;" text="下载法大大合同" color="#1e1848"
					v-if="(userType==0&&contract.memberSignDate)||(userType==1&&contract.employeeSignDate)"
					@click="downloadContract(contractNo)">
				</u-button> -->
				
				
				<u-button customStyle="width:30%;" text="签约合同" color="#1e1848"
					v-if="(userType==0&&!contract.memberSignDate)||(userType==1&&!contract.employeeSignDate)"
					@click="signPup = true">
				</u-button>
				
				<u-button customStyle="width:30%;" text="查看合同" color="#1e1848"
					v-if="(userType==0&&contract.memberSignDate)||(userType==1&&contract.employeeSignDate)"
					@click="showsignPup = true">
				</u-button>
			</view>
			<u-gap height="20"></u-gap>
		</view>
		<u-popup :show="signPup" mode="center" :round="10" :closeable="true" @close="signPup = false"
			customStyle="width: 100%;">
			<u-button customStyle="width:30%;" text="签约电子合同" color="#1e1848"
				v-if="(userType==0&&!contract.memberSignDate)||(userType==1&&!contract.employeeSignDate)"
				@click="showTip()">
			</u-button>
			<u-gap height="20"></u-gap>
			
			
		</u-popup>
		
		<u-popup :show="showsignPup" mode="center" :round="10" :closeable="true" @close="showsignPup = false"
			customStyle="width: 100%;">
			<u-button customStyle="width:30%;" text="查看电子合同" color="#1e1848"
				v-if="(userType==0&&contract.memberSignDate)||(userType==1&&contract.employeeSignDate)"
				@click="showContract()">
			</u-button>
			<u-gap height="20"></u-gap>
			<u-button customStyle="width:30%;" text="查看链接合同" color="#1e1848"
				v-if="(userType==0&&contract.memberSignDate)||(userType==1&&contract.employeeSignDate)"
				@click="viewagent()">
			</u-button>
			
		</u-popup>
		

		<u-empty v-if="!contract" text="暂无合同数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />

		<u-popup :show="showImgs" mode="center" :round="10" :closeable="true" @close="showImgs = false"
			customStyle="width: 75%;">
			<image :src="qrCodeImgUrl" style="width: 300rpx;height:300rpx;margin: 80rpx auto 20rpx;" />
			<view class="f14 red lh60 text-c">长按保存二维码，扫码添加，联系客服</view>
		</u-popup>

		<u-popup :show="showTips" mode="center" closeIconPos="top-right" @close="showTips = false"
			:closeOnClickOverlay="false" customStyle="z-index: 9999 !important;width: 95%;height: 500rpx;"
			style="display: flex;">
			<view style="margin-top: 40rpx;" v-if="contract">
				<text style="color: red;font-size: 20px;">
					{{signTips}}
				</text>
				<uni-card title="基础卡片">
					<template v-slot:title>
						<uni-list>
							<uni-list-item>
								<template v-slot:body>
									<view style="display: flex; justify-content: space-between;width: 100% !important;">
										<view style="font-size: 16px;color: #222;margin: 0 auto;" class="flac-row">
											合同编号：{{ contract.no }}
										</view>
									</view>
								</template>
							</uni-list-item>
						</uni-list>
					</template>
					<view style="display: flex;">
						<view style="margin: 0 auto;">
							<view style="padding: 5px 0px;color: #000;font-size: 16px;">
								订单号：{{contract.billNo}}
							</view>
						</view>
					</view>
				</uni-card>
				<view style="display: flex;padding-bottom: 10px;justify-content: center;margin-top: 35px;">
					<view style="width: 48%;">
						<u-button plain text="取消签约" color="#1e1848" @click="showTips=false"></u-button>
					</view>
					<view style="width: 1%;"></view>
					<view style="width: 48%;" v-show="signStatus == 0">
						<u-button text="前往认证" color="#1e1848" @click="goCertification(),showTips=false"></u-button>
					</view>
					<view style="width: 1%;"></view>
					<view style="width: 48%;" v-show="signStatus == 1">
						<u-button text="前往签约" color="#1e1848" @click="goSign(),showTips=false">
						</u-button>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 再次续约 -->
		<u-popup :show="showPrompt" mode="center" :round="10" :closeable="true" @close="showPrompt = false"
			:closeOnClickOverlay="false" customStyle="width:90%">
			<view class="w9 mg-at" style="margin: 50rpx auto;" v-if="contract">
				<view class="f16 red text-c lh60">点击确认后为该订单续签</view>
				<uni-card title="基础卡片" padding="10px 0" :thumbnail="contract.thumbnail">
					<template v-slot:title>
						<uni-list>
							<uni-list-item>
								<template v-slot:body>
									<view class="f16 c2 lh30 t-index2">
										合同编号：{{ contract.contractNo }}
									</view>
								</template>
							</uni-list-item>
						</uni-list>
					</template>
					<view class="f16 c0 t-index2 lh30">
						订单号：{{contract.billNo}}
					</view>
				</uni-card>
				<view class="w95 mg-at flac-row-c" style="margin-top: 60rpx;">
					<u-button type="info" text="取消" customStyle="margin:auto 20rpx"
						@click="showPrompt = false"></u-button>
					<u-button type="warning" text="确认" customStyle="margin:auto 20rpx"
						@click="addAgain(contract.billNo)"></u-button>
				</view>
			</view>
		</u-popup>

	</view>
</template>

<script>
	import {
		renewAddOrderNeeds,
		getKfQrCodeByBillNo
	} from '@/api/order.js';
	export default {
		data() {
			return {
				signPup:false,
				showsignPup:false,
				// 可设置
				// 用户类型-0：客户 1：员工
				userType: 0,
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				shareImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/share_contract_sign.png',
				qrCodeImgUrl: null,
				signUrl: '',
				signTips: '',
				signNo: '',
				signStatus: 0,
				contractNo: '',
				billNo: '',
				contract: {
					memberSignDate:'',
					employeeSignDate:''
				},
				showTips: false,
				showPrompt: false,
				showImgs: false,
				popupQRImg: false,
				qrImg: '',
			}
		},
		methods: {
			viewagent() {
			let urlPrefix = 'https://agent.xiaoyujia.com/upbaomu/';
			const type = Number(this.contract.contractType);
			let typeName = '';
			const contractId = this.contract.id
			if(this.userType == 1) {
				typeName = 'baomu'
			}
			if (type === 0 || type === 1 || type === 6 || type === 7) {
				urlPrefix = urlPrefix + typeName + 'contractInfo/' + contractId 
			} else if (type === 2) {
				urlPrefix = urlPrefix + typeName +'contractEscrowInfo/' + contractId 
			} else if (type === 4) {
				urlPrefix = urlPrefix + typeName + 'contractInstallmentInfo/' + contractId 
			}
			let param = {
				url: urlPrefix
			}
			let data = JSON.stringify(param)
			uni.navigateTo({
				url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
			})
				
			},
			copy(info) {

				// #ifndef H5
				uni.setClipboardData({
					data: info, //要被复制的内容
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
							title: `复制成功`,
							icon: 'success'
						})
					}
				}, true);
				// #endif

				// #ifdef H5
				let textarea = document.createElement("textarea")
				textarea.value = info
				textarea.readOnly = "readOnly"
				document.body.appendChild(textarea)
				textarea.select() // 选中文本内容
				textarea.setSelectionRange(0, info.length)
				uni.showToast({ //提示
					title: '复制成功'
				})
				result = document.execCommand("copy")
				textarea.remove()
				// #endif
			},
			downloadContract(no) {
				this.http({
					url: 'downloadPdfUrl',
					method: 'GET',
					data: {
						contractId: no
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('将链接复制到浏览器打开下载')
							this.copy(res.data)
						} else {
							this.$refs.uNotify.error(res.msg)
						}

					}

				})
			},
			// 格式化产品名
			formatProductName() {
				let name = this.contract.productName || this.contract.serviceType || ''
				if (name.includes('普通月嫂')) {
					name = '月嫂'
				}
				return name
			},
			copyText(text) {
				uni.setClipboardData({
					data: text,
					success: () => {
						this.$refs.uNotify.success('合同号复制成功!')
					}
				})
			},
			// 打开图片预览
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 续约
			showPrompts(index) {
				this.showPrompt = true
			},
			// 生成分享码
			createQRImg() {
				if (this.qrImg) {
					this.popupQRImg = true
					return
				}

				let title = this.userType == 0 ? '合同签约-客户版' : '合同签约-员工版'
				uni.showModal({
					title: '是否生成分享二维码？',
					content: '可发送给客户/员工进行合同签约',
					success: res => {
						if (res.confirm) {
							let scene = "t/" + this.userType + '*n/' + this.contractNo
							let param = {
								textList: [{
										"text": title,
										"fontSize": 50,
										"isCenter": true,
										"color": "0xfed472",
										"isBold": true,
										"x": 0,
										"y": 1020
									},
									{
										"text": '合同号：' + this.contractNo,
										"fontSize": 40,
										"isCenter": true,
										"color": "0xfed472",
										"isBold": true,
										"x": 0,
										"y": 1090
									}
								],
								qrCodeStyle: {
									"width": 350,
									"height": 350,
									"x": 200,
									"y": 250
								},
								maxWidth: 600,
								img: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/QR_img/QR_examEntry.png',
								path: 'pages-mine/contract/contract-sign',
								scene: scene,
								source: "xyjacn",
								type: 1
							}
							this.http({
								outsideUrl: 'https://biapi.xiaoyujia.com/image/getWxShareImg',
								method: 'POST',
								data: param,
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								success: res => {
									if (res.status == 200) {
										this.qrImg = res.data
										this.$refs.uNotify.success('合同签约分享码已生成，可点击进行保存！')
										this.popupQRImg = true
									}
								},
							})
						}
					}
				});
			},
			// 保存图片到手机
			saveToPhone() {
				uni.downloadFile({
					url: this.qrImg,
					success: (res) => {
						var imageUrl = res.tempFilePath
						uni.saveImageToPhotosAlbum({
							filePath: imageUrl,
							success: res => {
								this.$refs.uNotify.success('图片保存成功！')
							},
							fail: err => {
								this.$refs.uNotify.error('图片保存失败！')
							}
						})
					}
				})
			},
			// 查看合同
			showContract() {
				this.http({
					url: 'showSignContractByTencent',
					method: 'GET',
					hideLoading: true,
					data: {
						no: this.contractNo
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							// let param = {
							// 	url: res.data.url
							// }
							// let data = JSON.stringify(param)
							// uni.navigateTo({
							// 	url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
							// })
							uni.downloadFile({
								url: res.data.url,
								success: function(res) {
									var filePath = res.tempFilePath;
									uni.openDocument({
										filePath: filePath,
										showMenu: true,
										success: function(res) {
											console.log('打开文档成功');
										}
									});
								}
							});
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			getContractStatus(status) {
				status = Number(status);
				switch (status) {
					case 0:
						return '暂存中';
					case 1:
						return '生效中';
					case 2:
						return '已完成';
					case 3:
						return '补签单';
					case 4:
						return '未续签(已过期)';
					case 5:
						return '合同终止';
					case 99:
						return '作废单';
					case 100:
						return '暂存下户';
					default:
						break;
				}
			},
			formatShowImg() {
				let img = this.userType == 0 ? this.contract.headPortrait : this.contract.productImg
				img = img || this.blankImg
				return img
			},
			// 格式化时间
			formatDate(value) {
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				var dt = new Date(value)
				let year = dt.getFullYear()
				let month = (dt.getMonth() + 1).toString().padStart(2, '0')
				let date = dt.getDate().toString().padStart(2, '0')
				return `${year}-${month}-${date}`

			},
			addAgain(billNo) {
				renewAddOrderNeeds({
					data: billNo,
					onSuccess: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success(res.data)
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
					onFail: res => {}
				})
			},
			// 联系客服
			getKfQrCodeByBillNo(billNo) {
				this.qrCodeImgUrl = null
				getKfQrCodeByBillNo({
					data: billNo,
					onSuccess: res => {
						if (res.code === 0) {
							this.qrCodeImgUrl = res.data
							this.showImgs = true
						} else {
							// this.$refs.uNotify.warning('生成二维码失败，拨打 0592-5178888 联系客服进行处理')
							uni.makePhoneCall({
								phoneNumber: '0592-5178888', //电话号码
								success: function(e) {
									console.log(e)
								},
								fail: function(e) {
									console.log(e)
								}
							});
						}
					},
					onFail: res => {
						this.$refs.uNotify.error(res.msg)
					}
				});
			},
			showTip() {
				let source = 'h5'
				// #ifdef MP-WEIXIN
				source = 'xcx'
				// #endif
				this.http({
					url: 'createSignByTencent',
					method: 'GET',
					data: {
						no: this.contractNo,
						type: this.userType,
						source: source
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							// #ifdef H5
							// window.open(res.data.signUrl, '_blank')
							location.href = res.data.signUrl;

							// #endif
							// #ifdef MP-WEIXIN
							wx.navigateToMiniProgram({
								appId: 'wxa023b292fd19d41d', // 电子签appId; 联调时, 请使用demo小程序appId: 'wx371151823f6f3edf'
								path: res.data
									.signUrl, // 跳转的页面路径，可选，默认跳转到目标小程序首页; 签约时，需使用后台API返回的完整链接（类似pages/guide?id=xxx&foo=bar）
								extraData: null,
								envVersion: 'release', // 跳转正式或demo小程序，都需要传 'release'
								success(res) {
									// 成功跳转到目标小程序后的回调函数
								},
								fail(res) {
									// 跳转失败的回调函数
								}
							})
							// #endif

						} else {
							let msg = res.msg;
							uni.showToast({
								title: msg,
								icon:'none'
							})
						}
					}
				})
				// this.certification(this.contractNo)
			},
			// 获取合同信息
			getContract() {
				this.http({
					url: 'getContractByNo',
					path: this.contractNo,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.contract = res.data
							this.billNo = this.contract.billNo
						}
					},
				})
			},
			certification() {
				let type = this.userType == 0 ? 2 : 1
				let contractNo = this.contractNo
				this.signUrl = ''
				this.signTips = ''
				this.signNo = ''
				const param = {
					contractNo: contractNo,
					type: type //1员工 2客户
				}
				this.http({
					url: 'registeredAndCertification',
					method: 'GET',
					data: param,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							switch (res.msg) {
								case "成功获取实名认证地址":
									console.log("认证url", res.data)
									this.signUrl = res.data
									this.signTips = '未实名认证，请点击下面按钮前往认证'
									this.signNo = contractNo
									this.signStatus = 0
									break
								case "成功获取签约地址":
									console.log("签约url", res.data)
									this.signUrl = res.data
									this.signTips = '已实名认证，请点击下面按钮前往签约'
									this.signNo = contractNo
									this.signStatus = 1
									break
							}
							this.showTips = true
							console.log(this.signTips)
							console.log(this.signStatus)
						} else {
							let msg = res.msg;
							this.signTips = res.msg
							uni.showToast({
								title: msg
							})
						}
					}
				})
			},
			goCertification() {
				//#ifdef MP-WEIXIN
				wx.navigateToMiniProgram({
					appId: 'wxa1439f77c6d06a15', // pord
					// appId: 'wx855361a721050c6b', // test
					path: 'pages/result-loading/result-loading?verifyUrl=' + encodeURIComponent(this.signUrl),
					envVersion: 'release',
					success(res) {
						// 打开成功
					}
				})
				//#endif

				//#ifdef H5 || APP-PLUS
				return uni.showToast({
					title: '请从小程序打开该页面在认证',
					icon: 'none',
					duration: 5000
				})
				//#endif
			},
			goSign() {
				wx.navigateToMiniProgram({
					appId: 'wxa1439f77c6d06a15',
					path: 'pages/h5sign/h5sign',
					envVersion: 'release',
					extraData: {
						verifyUrl: this.signUrl
					},
					success(res) {
						// 打开成功
					}
				})
			},
			// 分享到好友
			onShareAppMessage(res) {
				let title = '签约入户，用心服务，就在小羽佳'
				title += this.userType == 0 ? '（客户版）' : '（员工版）'
				return {
					title: title,
					path: '/pages-mine/contract/contract-sign?userType=' + this.userType + '&contractNo=' + this
						.contractNo,
					mpId: 'wx8342ef8b403dec4e',
					imageUrl: this.shareImg
				}
			},
		},
		onLoad(options) {
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				this.userType = obj.t || this.userType
				this.contractNo = obj.n || this.contractNo
			}
			this.userType = options.userType || this.userType
			this.contractNo = options.contractNo || this.contractNo
			uni.setNavigationBarTitle({
				title: this.userType == 0 ? '合同签约（客户版）' : '合同签约（员工版）'
			})
			this.getContract()
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/swiper-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}
</style>