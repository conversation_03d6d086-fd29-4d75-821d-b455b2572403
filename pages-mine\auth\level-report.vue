<template>
	<view class="page f15 lh28">
		<view class="headBox flex-col-c">
			<image class="w8" src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jjtest/head2.png"
				mode="widthFix"></image>
			<image class="iconStyle" src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jjtest/icon_hg.png"
				mode=""></image>
		</view>
		<view class="text-c lh30">{{formatRealName(employee.realName,employeeInfo.sex)}}</view>
		<view class="w9 flac-row-b" style="margin: 40rpx auto;">
			<image class="w4" src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jjtest/img_echart.png"
				mode="widthFix"></image>
			<view class="w45">
				<view class="flac-row">
					<image class="w1" src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jjtest/icon_hz.png"
						mode="widthFix"></image>
					<view class="t-indent">{{levelName||''}}</view>
				</view>
				<view class="lh40 f12 c3">超过{{reportDetail.authSalaryRate||'0.00'}}%的员工</view>
			</view>
		</view>
		<view class="w9 mg-at" style="margin-bottom: 50rpx;" @click="goPage('./level')">
			<image class="w10" src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jjtest/bg_img3.png"
				mode="widthFix"></image>
		</view>

		<!-- 项目列表 -->
		<view v-for="(item,index) in projectList" :key="index" v-if="item.projectText.length">
			<view class="w9 mg-at flac-row">
				<view class="">{{item.projectName}}</view>
				<view class="w7 flac-row-a">
					<image class="w10" style="margin: auto 6rpx;" v-for="(imgItem,i) in 5" :key="i"
						:src="item.projectLevel <= i ?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jjtest/line_2.png' :'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jjtest/line_21.png'"
						mode="widthFix"></image>
				</view>
			</view>
			<view class="boxStyle f14">
				<view class="textStyle" v-for="(item1,index1) in item.projectText" :key="index1">{{index1+1}}.{{item1}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				basenum: 1,
				skillnum: 2,
				expernum: 3,
				evalnum: 4,
				baseList: ['基础条件好', '47岁，已婚已育', '初中学历'],
				experList: ['5年家政经验', '带过1~3周宝宝，擅长做家常菜'],
				evalInfo: {},

				level: 0,
				levelName: '',
				updaterNo: '',
				updaterName: '',
				updateTime: '',
				salary: '',
				authWorkType: 0,
				baomuId: 0,
				baomuDetail: {},
				baomu: {},
				employee: {},
				employeeInfo: {},
				baomuInfo: {},
				baomuAuthId: 0,
				certificateList: [],
				reportDetail: [],
				projectList: [],
			};
		},
		methods: {
			formatRealName(name, sex) {
				let xin = name.substring(0, 1)
				let data = ['', '师傅', '阿姨', '', '']
				let call = data[sex]
				if (call == '') {
					call = '阿姨'
				}
				return xin + call
			},
			goPage(url) {
				uni.navigateTo({
					url: url
				})
			},
			// 获取保姆详细信息
			getBaomuDetail() {
				this.http({
					url: 'getBaomuDetail',
					method: 'GET',
					hideLoading: true,
					path: this.baomuId,
					success: res => {
						if (res.code == 0) {
							let baomuDetail = res.data
							this.baomuDetail = baomuDetail
							this.employee = baomuDetail.employee
							this.employeeInfo = baomuDetail.employeeInfo
							this.baomuInfo = baomuDetail.baomuInfo
						} else {}
					},
				})


				this.http({
					url: 'getBaomuPage',
					method: 'POST',
					data: {
						baomuId: this.baomuId,
						current: 1,
						size: 10
					},
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							if (value == 0) {
								this.baomu = res.data.records[0]
							}
						}
					}
				})
			},
			// 获取鉴定记录
			getBaomuAuth() {
				this.http({
					url: 'listBaomuAuth',
					method: 'POST',
					hideLoading: true,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					data: {
						memberId: this.memberId || 0,
						baomuId: this.baomuId || null,
						authType: 0
					},
					success: res => {
						if (res.code == 0) {
							for (let i = 0; i < res.data.length; i++) {
								this.baomuAuthId = res.data[i].id
								this.getAuthReportDetail()
								break
							}
						}
					}
				})
			},
			getAuthReportDetail() {
				this.http({
					url: 'getAuthReportDetail',
					method: 'GET',
					path: this.baomuAuthId,
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							let detail = res.data
							this.reportDetail = detail
							this.workTypeId = detail.authWorkType
							this.salary = detail.authSalary
							this.levelName = (detail.authLevelName || '') + (detail.authWorkTypeName || '')
							this.updaterNo = detail.authEmployeeNo
							this.updaterName = detail.authEmployeeName
							this.updateTime = detail.updateTime
							this.projectList = detail.projectList
						}
					}
				})
			}
		},
		onLoad(options) {
			this.baomuId = parseInt(options.baomuId) || uni.getStorageSync("employeeId") || 0
			this.getBaomuDetail()
			this.getBaomuAuth()
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		width: 100%;
		min-height: 100vh;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jjtest/bg_img1.png') no-repeat center;
		background-size: 100% 100%;
		padding: 50rpx 0;
	}

	.headBox {
		width: 130rpx;
		height: 130rpx;
		margin: auto;
		border-radius: 50%;
		background-color: #fef9de;
		position: relative;
	}

	.iconStyle {
		width: 38rpx;
		height: 33rpx;
		position: absolute;
		bottom: 0;
		right: 0;
	}

	.boxStyle {
		width: 80%;
		margin: 40rpx auto;
		background-color: #fef9de;
		border-radius: 20rpx;
		box-shadow: 0rpx 6rpx 12rpx 6rpx rgba(0, 0, 0, 0.1);
		padding: 30rpx 40rpx;
	}

	.textStyle {
		margin: 10rpx auto;
	}
</style>