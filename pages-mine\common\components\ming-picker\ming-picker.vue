<template>
	<view>
		<picker class="address-picker" mode="multiSelector" :range="[province, city, area, street]" :value="value"
			@change="changeHandler" @cancel="close" @columnchange="columnchange">
			<slot></slot>
		</picker>
	</view>
</template>

<script>
	export default {
		props: {
			defaultAddress: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				show: false,
				addressData: [],
				province: [],
				city: [],
				area: [],
				street: [],
				value: [0, 0, 0, 0]
			};
		},
		mounted() {
			this.getAllDictLocation()
		},
		created() {

		},
		methods: {
			getAllDictLocation() {
				let data = uni.getStorageSync("addressData") || []
				data = []
				if (data.length != 0) {
					this.formatAddressData(data)
					return
				}

				this.http({
					url: 'getAllDictLocation',
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.formatAddressData(res.data)
						} else {
							console.log("获取省市区街道数据字典失败！")
						}
					},
				});
			},
			formatAddressData(data) {
				this.addressData = data
				this.province = this.addressData.map(it => it.label)
				if (this.addressData[0].children != undefined) {
					this.city = this.addressData[0].children.map(it => it.label)
				}

				if (this.addressData[0].children[0].children != undefined) {
					this.area = this.addressData[0].children[0].children.map(it => it.label)
				}

				if (this.addressData[0].children[0].children[0].children != undefined) {
					this.street = this.addressData[0].children[0].children[0].children.map(it => it
						.label)
				}

				// 存入本地缓存
				uni.setStorageSync("addressData", data)
				this.onAttached()
			},
			onAttached() {
				const address = this.defaultAddress;
				const addressData = this.addressData;
				if (address[0]) {
					// 如果有初始值，则需要初始地址
					const filter = (index) => (index > -1 ? index : 0);
					const currentProvince = filter(addressData.findIndex(it => it.label === address[0]));
					const currentCity = filter(addressData[currentProvince].children.findIndex(it => it.label === address[
						1]));
					const currentArea = filter(addressData[currentProvince].children[currentCity].children.findIndex(it =>
						it.label === address[2]));
					const currentStreet = filter(addressData[currentProvince].children[currentCity].children[currentArea]
						.children.findIndex(it => it.label === address[3]));

					const city = addressData[currentProvince].children;
					const area = addressData[currentProvince].children[currentCity].children;
					const street = addressData[currentProvince].children[currentCity].children[currentArea].children;
					this.value = [currentProvince, currentCity, currentArea, currentStreet];
					this.city = city.map(it => it.label);
					this.area = area.map(it => it.label);
					this.street = street.map(it => it.label);
					this.address = [addressData[currentProvince].label, city[currentCity].label, area[currentArea].label,
						street[currentStreet].label
					];
				}
			},
			changeHandler() {
				let value = this.getAddress(...this.value),
					code = this.getCode(...this.value)
				this.$emit("address", {
					value,
					code,
					data: {
						[code[0]]: value[0],
						[code[1]]: value[1],
						[code[2]]: value[2],
						[code[3]]: value[3],
					}
				})
			},
			close() {
				this.$emit("close", {})
			},
			getAddress(p, c, a, s) {
				const {
					province,
					city = [],
					area = [],
					street = []
				} = this;
				return [province[p], city[c] || '', area[a] || '', street[s] || ''];
			},
			getCode(p, c, a, s) {
				const addressData = this.addressData
				let province = addressData.map(it => it.value),
					city = addressData[p].children.map(it => it.value),
					area = addressData[p].children[c].children.map(it => it.value),
					street = addressData[p].children[c].children[a].children.map(it => it.value)
				return [province[p], city[c] || 0, area[a] || 0, street[s] || 0];
			},
			columnchange(e) {
				// wx.showLoading({ mask: true });
				const addressData = this.addressData;
				const {
					column,
					value: index
				} = e.detail;
				if (column === 0) {
					// 省份变了
					this.city = addressData[index].children.map(it => it.label)
					this.area = addressData[index].children[0].children.map(it => it.label);
					this.street = addressData[index].children[0].children[0].children.map(it => it.label);
					this.value = [index, 0, 0, 0];
				} else if (column === 1) {
					// 城市变了
					const currentProvince = this.value[0];
					this.area = addressData[currentProvince].children[index].children.map(it => it.label);
					this.street = addressData[currentProvince].children[index].children[0].children.map(it => it
						.label);
					this.value = [currentProvince, index, 0, 0];
				} else if (column === 2) {
					// 区域变了
					const currentProvince = this.value[0];
					const currentCity = this.value[1];
					this.street = addressData[currentProvince].children[currentCity].children[index].children.map(
						it => it
						.label);
					this.value = [currentProvince, currentCity, index, 0];
				} else {
					// 街道
					const value = this.value;
					value[3] = index;
					this.value = value;
				}
			}
		}
	}
</script>

<style scoped>

</style>