	// 大号按钮
	.btn-big {
		padding-bottom: 60rpx;

		button {
			margin: 80rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	// 底部按钮
	.btn-bottom {
		button {
			bottom: 20rpx;
			margin: 20rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	// 底部固定按钮
	.btn-bottom-fixed {
		position: fixed;
		z-index: 999;
		bottom: 0rpx;
		width: 100%;
		height: 120rpx;
		background-color: #ffffff;

		button {
			margin: 20rpx 5%;
			width: 90%;
			height: 80rpx;
			line-height: 80rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}
	
	// 底部悬浮按钮
	.btn-bottom-float {
		position: fixed;
		z-index: 999;
		bottom: 0rpx;
		width: 80%;
		height: 120rpx;
		margin: 0rpx 10%;
	
		button {
			width: 100%;
			height: 80rpx;
			line-height: 80rpx;
			color: #1e1848;
			background-color: #fff;
			border: #1e1848 2rpx solid;
			border-radius: 50rpx;
			font-size: 32rpx;
			box-shadow: 4rpx 4rpx 10rpx #909399;
		}
	}
	
	// 镂空按钮
	.btn-hollow {
		button {
			margin: 80rpx auto;
			width: 40%;
			height: 70rpx;
			line-height: 70rpx;
			color: #1e1848;
			background-color: #fff;
			border: #1e1848 2rpx solid;
			border-radius: 40rpx;
			font-size: 36rpx;
		}
	}
	
	// 按钮组
	.btn-group {
		width: 100%;
		height: 90rpx;
		display: flex;
		flex-direction: row;
		padding: 80rpx 0 200rpx 0;
	
		button {
			width: 40%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}
