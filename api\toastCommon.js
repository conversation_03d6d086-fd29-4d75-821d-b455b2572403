// 调用
// this.$toast.toast("提示消息");
// this.$toast.sucToast('成功提示消息');
// this.$toast.errToast('错误提示消息');
// this.$toast.showLoading();
// this.$toast.tipLiading("提示loading");
// this.$toast.hideLoading();

// 不含icon提示框
const toast = str => {
	return new Promise((resolve, reject) => {
		uni.showToast({
			title: str,
			icon: "none",
			duration: 2000,
			success: () => {
				setTimeout(() => {
					resolve()
				}, 2000)
			}
		})
	})
};
// 成功提示框
const sucToast = str => {
	return new Promise((resolve, reject) => {
		uni.showToast({
			title: str,
			icon: "success",
			duration: 2000,
			success: () => {
				setTimeout(() => {
					resolve()
				}, 2000)
			}
		})
	})
};
// 错误提示框
const errToast = str => {
	return new Promise((resolve, reject) => {
		uni.showToast({
			title: str,
			icon: "error",
			duration: 2000,
			success: () => {
				setTimeout(() => {
					resolve()
				}, 2000)
			}
		})
	})
};
// loading
const showLoading = () => {
	return new Promise((resolve, reject) => {
		uni.showLoading({
			success: () => {
				resolve()
			}
		})
	})
};
// tipLoading ==>提示loading
const tipLoading = str => {
	return new Promise((resolve, reject) => {
		uni.showLoading({
			title: str,
			success: () => {
				resolve()
			}
		})
	})
};
// 隐藏loading
const hideLoading = () => {
	return new Promise((resolve, reject) => {
		uni.hideLoading({
			success: () => {
				resolve()
			}
		})
	})
};
export default {
	toast: toast,
	sucToast: sucToast,
	errToast: errToast,
	showLoading: showLoading,
	tipLoading: tipLoading,
	hideLoading: hideLoading,
}


export const showModal = (options) => {
	let params = {
		title: "提示",
		content: "自定义内容",
		align: "center", // 对齐方式 left/center/right
		cancelText: "取消", // 取消按钮的文字
		cancelColor: "#8F8F8F", // 取消按钮颜色
		confirmText: "确定", // 确认按钮文字
		confirmColor: "#FFAD15", // 确认按钮颜色 
		showCancel: true, // 是否显示取消按钮，默认为 true
	}

	Object.assign(params, options)

	let list = []
	Object.keys(params).forEach(ele => {
		list.push(ele + "=" + params[ele])
	})
	let paramsStr = list.join('&')

	uni.navigateTo({
		url: "/pages/modal/modal?" + paramsStr
	})

	return new Promise((resolve, reject) => {
		uni.$once("AppModalCancel", () => {
			reject()
		})
		uni.$once("AppModalConfirm", () => {
			resolve()
		})
	});
}
