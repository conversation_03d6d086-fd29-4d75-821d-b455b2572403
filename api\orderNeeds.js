import {
	requestUtil
} from "../util/requestUtil.js"



/**
 * 获取所有门店
 *
 * @param {Object} gettParam
 */
export const searchStore = function(getParam) {
	requestUtil({
		url: '/system/getStoreNameList',
		method: 'GET',
		success: getParam.onSuccess,
		complete: getParam.onComplate
	})
}

/**
 * 根据门店id获取员工
 *
 * @param {Object} gettParam
 */
export const getEmpByStoreId = function(getParam) {
	requestUtil({
		url: '/employee/getEmpByStoreId?storeId='+getParam.data,
		method: 'GET',
		success: getParam.onSuccess,
		complete: getParam.onComplate
	})
}

/**
 * 查询线索列表
 * 
 * @param {Object} postParam
 */
export const getOrderNeedsList = function(postParam) {
	requestUtil({
		url: '/order/getOrderNeedsList',
		data: postParam.data,
		method: 'POST',
		success: postParam.onSuccess,
		complete: postParam.onComplate
	})
}

/**
 * 推荐保姆
 *
 * @param {Object} gettParam
 */
export const getBaoMuByRemarkId = function(getParam) {
	requestUtil({
		url: '/order/getBaoMuByRemarkId?remarkId='+getParam.data,
		method: 'GET',
		success: getParam.onSuccess,
		complete: getParam.onComplate
	})
}

/**
 * 面试列表
 * @param {Object} getParam
 */
export const getOrderDeliveryList = function(getParam) {
	requestUtil({
		url: '/order/getEmpByOrderNeedId?orderNeedsId=' + getParam.data,
		// url: '/order/getOrderDeliveryList?number=17706023252',
		method: 'GET',
		success: getParam.onSuccess,
		complete: getParam.onComplate
	})
}

/**
 * 添加面试信息
 * @param {Object} postParam
 */
export const addOrderDelivery = function(postParam) {
	requestUtil({
		url: '/order/addOrderDelivery',
		data: postParam.data,
		method: 'POST',
		success: postParam.onSuccess,
		complete: postParam.onComplate
	})
}
