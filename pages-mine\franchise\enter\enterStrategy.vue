<template>
	<view>
		<view class="f20 lh50 t-indent2">入驻流程</view>
		<view class="steps f16">
			<u-steps iconSize="30" activeColor="#1e1848" current="0" direction="column" >
				<u-steps-item  desc="缴纳认证费用" title="第一步.认证缴费"/>
				<u-steps-item  class="stepsA" desc="提交公司信息、营业者信息" title="第二步.主体信息"/>
				<u-steps-item  desc="提交店铺基本信息、类目信息、店铺管理人信息" title="第三步.商户信息"/>
				<u-steps-item  desc="平台预计在1~3个工作日反馈审核结果" title="第四步.平台审核"/>
				<u-steps-item  desc="验证账户的银行卡、预留手机号、姓名、证件号四要素" title="第五步.账户验证"/>
				<u-steps-item  desc="入驻成功!" title="第六步.入驻成功"/>
			</u-steps>
			</view>

	</view>
</template>
<script>
	export default {
		
		data() {
			return {
		
			}
		},
	
		methods: {
			// 分享到好友或朋友圈
			onShareAppMessage(res) {
				return {
					title: '小羽佳-家姐联盟',
					path: '/pages-mine/franchise/enter/topUpMoney?id=' + uni.getStorageSync("memberId"),
					mpId: 'wx8342ef8b403dec4e'
				}
			},
			onShareTimeline(res) {
				return {
					title: '小羽佳-家姐联盟',
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			}
		},
		// 页面加载后
		mounted() {

		}
	}
</script>

<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
	}
	
	
	.steps {
		padding-bottom: 30rpx;
		padding-top: 25rpx;
		margin-left: 4%;
	}
	.stepsA {
		font-size: 40rpx;
	}
  
  /deep/.u-text__value {
     font-size: 32rpx !important;
     line-height: 60rpx;
 }
 
 /deep/.u-text__value--tips {
     font-size: 28rpx !important;
 }
	
</style>
