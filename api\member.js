import {requestUtil} from "../util/requestUtil.js"

/**
 * 会员信息列表
 * @param {Object} postParam
 */
export const getMemberCouponAndAmount = function(postParam) {
	requestUtil({
		url: '/member/getMemberCouponAndAmount?memberId=' + postParam.data,
		method: 'GET',
		success: postParam.onSuccess,
		complete: postParam.onComplate
	})
}

/**
 * 会员与工号
 * @param {Object} postParam
 */
export const employeeToMember = function(postParam) {
	requestUtil({
		url: '/member/employeeToMember/' + postParam.data,
		method: 'GET',
		success: postParam.onSuccess,
		complete: postParam.onComplate
	})
}
