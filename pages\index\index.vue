<template>
	<view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			
		},
		mounted() {
			// 跳转到分包中的路径
			// #ifdef MP-TOUTIAO
			// return uni.reLaunch({
			// 	url: "/pages-mine/index/mine"
			// })
			// #endif

			return uni.reLaunch({
				url: "/pages-mine/index/index"
			})
		}
	}
</script>

<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
	}

	.main {
		height: auto;
		margin-bottom: 200rpx;
	}
</style>
