<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>
		 
		<view class="shadow">
			<view style="height: 25rpx;"/>
			<u-steps activeColor="#1e1848" current="4" class="steps">
				<u-steps-item title="认证缴费"/>
				<u-steps-item title="主体信息"/>
				<u-steps-item title="商户信息"/>
				<u-steps-item title="平台审核"/>
				<u-steps-item title="账户验证"/>
			</u-steps>
			<view style="height: 25rpx;"/>
		</view>

		<view class="shadow">
		
			<text class="authentication-type">认证类型(不可编辑)</text>
			<view class="input-state">
				<u--input border="bottom" disabled v-model="pickerData" placeholder="企业"  disabledColor="Light#f4f4f5"></u--input>
			</view>

			<view class="companyMsg">账户验证类型(不可编辑)</text></view>
			<view class="input-state">
				<u--input  v-model="accountVerifyType" border="bottom" disabled
					disabledColor="Light#f4f4f5"></u--input>
			</view>
		
			<view v-if="authenticationType==2">
				
				<view class="companyMsg">公司名称(不可编辑)</text></view>
				<view class="input-state">
					<u--input disabled border="bottom" v-model="companyName" disabled
						disabledColor="Light#f4f4f5">
					</u--input>
				</view>
				
				
				<view class="companyMsg">统一社会信用代码(不可编辑)</text></view>
				<view class="input-state">
					<u--input v-model="creditCode" border="bottom" disabled
						disabledColor="Light#f4f4f5"></u--input>
				</view>
				
			<view class="companyMsg">法定代表人姓名(不可编辑)</text></view>
			<view class="input-state">
				<u--input border="bottom" v-model="legalPerson" disabled
					disabledColor="Light#f4f4f5"></u--input>
			</view>
			
			<view class="companyMsg">法定代表人身份证号码(提交后不可编辑)</text></view>
			<view class="input-state">
				<u--input placeholder="请填写法定代表人身份证号码" v-model="legalPersonICard" border="bottom"
					disabledColor="Light#f4f4f5"></u--input>
			</view>
			
			<view class="companyMsg">法人银行卡号(非必填)</text></view>
			<view class="input-state">
				<u--input placeholder="请填写户名为法人姓名的银行卡号" maxlength="19" v-model="personBankCard" border="bottom" clearable
					disabledColor="Light#f4f4f5"></u--input>
			</view>
			
			</view>
			
			<view v-if="authenticationType==1">
				<view v-if="name"> 
			<view class="companyMsg">经营者姓名(不可编辑)</text></view>
			<view class="input-state">
				<u--input border="bottom" v-model="name" disabled
					disabledColor="Light#f4f4f5"></u--input>
			</view>
			</view>
			
			<view v-if="idCard"> 
			<view class="companyMsg">经营者身份证号码(不可编辑)</text></view>
			<view class="input-state">
				<u--input  v-model="idCard" border="bottom" disabled
					disabledColor="Light#f4f4f5"></u--input>
			</view>
			</view>
			
			<view v-if="bankCardNo"> 
				<view class="companyMsg">经营者银行卡号(非必填)</text></view>
				<view class="input-state">
					<u--input placeholder="请填写经营者银行卡号" maxlength="19" v-model="bankCardNo" border="bottom" clearable
						disabledColor="Light#f4f4f5"></u--input>
				</view>
				</view>
			
				</view>
				
				<view class="companyMsg">手机号</text></view>
				<view class="input-state">
					<u--input placeholder="请填写手机号" maxlength="11" v-model="bankReservedPhone" border="bottom" clearable
						disabledColor="Light#f4f4f5"></u--input>
				</view>
				
				<view class="companyMsg">验证码</text></view>
				<view class="input-stateC">
					<u--input placeholder="请输入短信验证码" maxlength="6" type="number" v-model="verificationCode" border="bottom" clearable
						disabledColor="Light#f4f4f5"></u--input>
				</view>
				<view  class="verificationCodeA">
				<u-button color="#1e1848" :class="{ verificationCodeB: second>0 }" @click="getcode" type="primary" :text="authCode" customStyle="color:#f6cc70"></u-button>
				</view>
				<view  class="button-state">
						<u-button customStyle="width:80%;color:#f6cc70" @click="goTopUpMoney" color="#1e1848" >提交</u-button>
				</view>
			<view>
				<u-link class="ulink"></u-link>
			</view>
			
			</view>
		
		</view>

	</view>
</template>
<script>
	export default {
		
		data() {
			return {
				msgType: "success",
				msgText: "",
				second: 0,
				show: false,
				idCard: '',
				name: '',
				authenticationType: null,
				companyName: '',
				accountVerifyType: '实名验证',
				creditCode: '',
				storeLogo: '',
				legalPerson: null,
				legalPersonICard: '',
				bankReservedPhone: '',
				bankCardNo: '',
				personBankCard: '',
				verificationCode: '',
				pickerData: ''
			
			}
		},
		computed: {
			authCode() {
				if (this.second == 0) {
					return '获取验证码';
				} else {
					if (this.second < 10) {
						return '重新获取' + this.second;
					} else {
						return '重新获取' + this.second;
					}
				}
			}
		},
		methods: {
			goTopUpMoney() {
				if (!this.verificationCode) {
					this.$refs.uNotify.error("请填写验证码!")
				}
				if (!this.bankReservedPhone) {
					this.$refs.uNotify.error("请填写手机号!")
				} else {
					let reg = /^[1][3,4,5,7,8,9][0-9]{9}$/
					if (!reg.test(this.bankReservedPhone)) {
					this.$refs.uNotify.error("请填写正确的手机号!")
					this.bankReservedPhone = ''
					}
				}
				// if (this.authenticationType==1&&!this.bankCardNo){
				// 	this.$refs.uNotify.error("请填写经营者银行卡号!")
				// }
				// if (this.authenticationType==2&&!this.personBankCard){
				// 	this.$refs.uNotify.error("请填写户名为法人姓名的银行卡号!")
				// }
				if (this.authenticationType==2&&!this.legalPersonICard){
					this.$refs.uNotify.error("请填写法定代表人身份证号码!")
				}
				if (this.verificationCode&&this.bankReservedPhone&&this.authenticationType==1||
				this.verificationCode&&this.bankReservedPhone&&this.authenticationType==2&&this.legalPersonICard) {
					let param = {
						bankCardNo: this.bankCardNo,
						personBankCard: this.personBankCard,
						legalPersonICard: this.legalPersonICard,
						bankReservedPhone: this.bankReservedPhone,
						verificationCode: this.verificationCode,
						type: 2,
						userId: uni.getStorageSync("memberId")
					}
					// 提交审核
					this.http({
						url: 'partnerEnter',
						method: 'POST',
						data: param,
						header: {
							"content-type":"application/json;charset=UTF-8"
						},
						success: res => {
							if (res.code==0){
								this.$refs.uNotify.success("验证通过!")
							uni.navigateTo({
								 url: "/pages-work/index"
							})
							}else{
								this.$refs.uNotify.error(res.msg)
							}
						},
						fail: err => {
							console.log('请求失败！' + res)
						}
					})
				}
			},
			getcode() {
			let _this = this;
			let reg = /^[1][3,4,5,7,8,9][0-9]{9}$/
			if (!reg.test(this.bankReservedPhone)) {
				uni.showToast({
					icon: 'none',
					title: '请输入正确的手机号'
				});
				return;
			}
				if (this.second > 0) {
					return;
				}
				this.second = 60;
				uni.request({
					url: 'https://api.xiaoyujia.com/member/sendLoginCode',
					data: {
						phone: this.bankReservedPhone,
					},
					method: 'POST',
					dataType: 'json',
					success: (res) => {
						if (res.data.code != 0) {
							uni.showToast({
								title: res.data.data,
								icon: 'none'
							});
						} else {
							uni.showToast({
								title: "短信发送成功"
							});
							js = setInterval(function() {
								_this.second--;
								if (_this.second == 0) {
									this.clear()
								}
							}, 1000)
						}
					},
					fail() {
						this.second == 0
					}
				});
			},
			getFranchiseMsgById() {
				this.http({
					url: "getFranchiseMsgById",
					method: 'GET',
					path: uni.getStorageSync("memberId"),
					success: res => {
						if (res.code == 0) {
							this.authenticationType = res.data.authenticationType
							this.name = res.data.name
							if(res.data.authenticationType==2){
								this.pickerData = "企业"
							}else{
								this.pickerData = "个人"
							}
							this.companyName = res.data.companyName
							this.creditCode = res.data.creditCode
							this.legalPerson = res.data.legalPerson
							this.idCard = res.data.idCard
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			},
			// 分享到好友或朋友圈
			onShareAppMessage(res) {
				return {
					title: '小羽佳-家姐联盟',
					path: '/pages-mine/franchise/enter/topUpMoney?id=' + uni.getStorageSync("memberId"),
					mpId: 'wx8342ef8b403dec4e'
				}
			},
			onShareTimeline(res) {
				return {
					title: '小羽佳-家姐联盟',
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			}
		},
		// 页面加载后
		mounted() {
			this.getFranchiseMsgById()
		}
	}
</script>

<style lang="scss">
		@import "@/pages-mine/common/css/franchise.scss";
	page {
		height: auto;
		background-color: #ffffff;
		font-size: 32rpx;
	}
	.verificationCodeA {
		margin-left: 74%;
		height: 55rpx;
		width: 180rpx;
		margin-top: -11%;
	}
	
	.verificationCodeB {
		color: #999999 !important;
		border: 1rpx solid #999999;
	}
	
	.link-state {
		margin-top: 10%;
	}

	.ulink {
		padding-bottom: 3%;
	}
	.steps {
		padding-bottom: 30rpx;
		padding-top: 25rpx;
	}

	.text-state {
		text-align: center;
		margin-top: 2%;
	}

	.shadow {
		height: auto;
		width: 100%;
		margin-bottom: 30rpx;
		// 添加栏目底部阴影
		box-shadow: 0 4rpx 20rpx #dedede;
	}

	.authentication-type {
		color: dimgrey;
		margin-left: 30rpx;
		line-height: 100rpx;
	}

	.input-state {
		margin-left: 30rpx;
		margin-top: -2%;
	}
	
	.input-stateC {
		margin-left: 30rpx;
		margin-top: -2%;
		flex: 1;
		width: 70%;
	}
	

	.text-stateB {
		margin-left: 30rpx;
		color: darkgrey;
		margin-top: 3%;
	}
	
	.text-stateC {
		color: darkgrey;
		margin-top: 3%;
		margin-left: 30rpx;
	}

	// 小箭头
	.text-arrow {
		display: block;
		float: right;
		height: 100rpx;
		margin-top: -8%;
		padding-right: 40rpx;
		color: black;
	}



	.link-state {
		margin-top: 10%;
	}



	.button-state {
		width: 100%;
		height: 126rpx;
		margin-top: 16%;
	}




</style>
