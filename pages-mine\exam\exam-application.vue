<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 须知提示弹窗 -->
		<u-popup :show="popupShow" mode="bottom" @close="popupShow = false">
			<view class="filter-title" @click="popupShow = false">
				<text style="font-weight: bold;">考试须知</text>
			</view>
			<view class="filter-content" style="height: 500rpx;">
				<view class="filter-content-text">
					<text v-for="(item,index) in attentionList" :key="index">{{item}}</text>
				</view>
			</view>
		</u-popup>

		<!-- 入口分享图片 -->
		<view class="img-view" v-if="showShareImg">
			<img class="w10 mg-at" :src="shareImg" alt="" mode="widthFix" />
		</view>

		<view v-if="!showShareImg">
			<view class="exam-tab">
				<view class="tab flac-col">
					<view class="tab-head">
						<text>{{(exam.examTitle)}}</text>
					</view>
					<view class="tab-head-smail flac-row">
						<uni-icons type="notification" size="24" style="margin-right: 20rpx;">
						</uni-icons>
						<text>考试限时：{{exam.examDuration||''}}分钟</text>
					</view>

					<view class="tab-head-smail flac-row">
						<uni-icons type="calendar" size="24" style="margin-right: 20rpx;">
						</uni-icons>
						<text>合格：{{exam.examPassScore||''}}分</text>
					</view>

					<view class="tab-head-smail flac-row">
						<uni-icons type="medal" size="24" style="margin-right: 20rpx;">
						</uni-icons>
						<text>满分：{{exam.examScore||''}}分</text>
					</view>
				</view>
			</view>

			<!-- 考试说明 -->
			<view class="exam-tab">
				<view class="tab flac-col">
					<view class="tab-head">
						<text>考试说明</text>
					</view>
					<view class="tab-head-smail">
						<text style="font-size: 32rpx;line-height: 50rpx;">{{exam.examRemark||'-'}}</text>
					</view>

					<view class="tab-head-smail" style="text-align: right;color: #ff4d4b;margin: 20rpx 0;"
						@click="popupShow = true">
						<text class="fb" style="font-size: 32rpx;line-height: 50rpx;">*阅读考试须知</text>
					</view>
				</view>
			</view>

			<!-- 课程学习记录 -->
			<view class="exam-tab" v-if="courseList.length">
				<view class="tab-head">
					<text>课程学习</text>
				</view>
				<view class="mg-at f16 lh25" style="margin: 40rpx 5%;">
					<view class="flac-row" @click="openCourse(index)" style="margin-bottom: 40rpx;"
						v-for="(item,index) in courseList" :key="index">
						<view class="flac-col w8">
							<text class="fb">{{item.courseTitle}}</text>
							<text>学习进度：{{item.added?item.studyDurationRate||'0%':'暂未报名'}}</text>
						</view>
						<view class="bottom-small w2">
							<button>去学习</button>
						</view>
					</view>
				</view>

				<view class="tab-head-smail flex-col" style="text-align: right;color: #ff4d4b;margin: 20rpx 0;">
					<text class="fb" style="font-size: 32rpx;line-height: 50rpx;color: #000;">学习进度：{{totalRate}}%</text>
					<text class="fb" style="font-size: 32rpx;line-height: 50rpx;">*达到80%即可报名考试</text>
				</view>
			</view>
		</view>

		<!-- 考试记录 -->
		<view class="exam-tab" v-if="examRecordList.length">
			<view class="tab-head">
				<text>考试记录</text>
			</view>
			<view class="mg-at f16 lh25" style="margin: 40rpx 5%;">
				<view class="flac-row" @click="openExam(index)" style="margin-bottom: 40rpx;"
					v-for="(item,index) in examRecordList" :key="index">
					<view class="flac-col w8">
						<text class="fb">{{item.examTitle}}</text>
						<text>开考时间：{{item.startedTime}}</text>
						<view class="">
							<text>考试成绩：{{item.recordScore|| ''}}</text>
							<text v-if="item.recordState==2||item.recordState==4"
								:style="item.recordPassed?'color:#19be6b;':'color:#909399;color:#909399'">（{{item.recordPassed?'已通过':'未通过'}}）</text>
							<text v-else>暂未参加</text>
						</view>
					</view>
					<view class="bottom-small w2">
						<button>{{item.recordState<2?'去考试':'去查看'}}</button>
					</view>
				</view>
			</view>
		</view>

		<u-gap height="200"></u-gap>

		<view class="btn-bottom-fixed" v-if="examTimeState==1">
			<button @click="openExam(-1)" v-if="recordPassed"
				:style="studyFinished?'background-color:#1e1848;':'background-color:#909399;color:#fff'">
				<uni-icons type="calendar" size="18" color="#f6cc70"></uni-icons>
				查看考试
			</button>
			<button @click="tryRestartExamRecord()" v-else>
				<uni-icons type="calendar" size="18" color="#f6cc70"></uni-icons>
				{{examRecordList.length>1?'重新考试':'参加考试'}}
			</button>
		</view>
		<view class="btn-bottom-fixed" v-else>
			<button v-if="examTimeState==0"
				style="background-color:#909399">考试未开始{{leaveTime?'-'+formatLeaveTime(leaveTime):''}}</button>
			<button v-else-if="examTimeState==2" style="background-color:#909399">考试已结束</button>
			<button v-else style="background-color:#909399">暂不开放考试</button>
		</view>
	</view>

	</view>
</template>

<script>
	import {
		data
	} from '../../uni_modules/uview-ui/libs/mixin/mixin'
	export default {
		data() {
			return {
				// 可设置
				// 显示考试入口分享图
				showShareImg: false,
				// 是否校验员工
				checkEmployee: true,
				// 检查考试时间
				checkExamTime: true,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				popupShow: false,
				service: "https://biapi.xiaoyujia.com/",
				attentionList: [
					"1.开始考试后，不得中途退出，否则将视为放弃，并重新答题！",
					"2.考试时间到，不得继续答题，否则将自动交卷，请注意把握时间！",
					"3.诚信考试，多次切屏将视为违规，并自动交卷结束考试！"
				],
				headImg: '',
				shareImg: '',
				examEntryImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/QR_img/QR_examEntry.png',
				blankImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png',

				recordId: null,
				memberId: uni.getStorageSync("memberId") || 0,
				employeeId: uni.getStorageSync("employeeId") || null,
				studyFinished: false,
				recordPassed: false,
				examId: -1,
				exam: {},
				examRecordList: [],
				courseList: [],
				notEndExamIndex: -1,
				recordEnded: false,
				examTimeState: -1,
				time: 0,
				leaveTime: null,
				authId: uni.getStorageSync('authId') || 0,
				totalRate: ''
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 打开考试
			openExam(index) {
				if (this.studyFinished) {
					let url = '/pages-mine/exam/exam-center'
					if (this.examRecordList.length && index != -1) {
						url = '/pages-mine/exam/exam-detail?id=' + this.examRecordList[index].id
					}
					uni.navigateTo({
						url: url
					})
				} else {
					this.$refs.uNotify.error("抱歉，学习进度满80%才能进行考试！")
				}
			},
			// 打开课程
			openCourse(index) {
				let id = this.courseList[index].id
				uni.navigateTo({
					url: '/pages-mine/studyCenter/course-detail?id=' + id
				})
			},
			addExam() {
				let idList = ''
				this.courseList.forEach(item => {
					idList += (item.id + ',')
				})
				if (idList.lastIndexOf(',') == idList.length - 1) {
					idList = idList.substring(0, idList.length - 1)
				}
				this.http({
					outsideUrl: 'https://api.xiaoyujia.com/exam/addExamRecordAndNoticeByExamId',
					method: 'POST',
					data: {
						memberId: this.memberId || null,
						employeeId: this.employeeId || null,
						examId: this.examId,
						recordCourseIdList: idList
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						this.$refs.uNotify.success("报名成功！点击参加考试即可开始答题！")
						this.listUnionExamRecord()
					}
				});
			},
			// 重新考试
			tryRestartExamRecord() {
				let index = this.notEndExamIndex
				if (index != -1) {
					let url = '/pages-mine/exam/exam-detail?id=' + this.examRecordList[index].id
					uni.navigateTo({
						url: url
					})
				} else {
					let maxMakeUpCount = this.examRecordList.length ? (this.examRecordList[0].maxMakeUpCount || 3) : 0
					if (this.studyFinished) {
						if (!this.examRecordList.length) {
							return this.addExam()
						}
						this.openCheck(1, '考试未通过，确定重新考试吗？', '共有' + maxMakeUpCount + '次考试机会，当前已考' + this.examRecordList
							.length +
							'次')
					} else {
						this.$refs.uNotify.error("抱歉，学习进度满80%才能进行考试！")
					}
				}
			},
			// 重新考试
			restartExamRecord() {
				if (!this.studyFinished) {
					this.$refs.uNotify.error("抱歉，学习进度满80%才能进行考试！")
					return
				}
				let idList = ''
				this.courseList.forEach(item => {
					idList += (item.id + ',')
				})
				if (idList.lastIndexOf(',') == idList.length - 1) {
					idList = idList.substring(0, idList.length - 1)
				}
				this.http({
					url: 'restartExamRecord',
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.memberId,
						employeeId: this.employeeId,
						examId: this.examId,
						recordCourseIdList: idList
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success("已报名考试，点击重新考试即可开始！")
							this.listUnionExamRecord()
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				});
			},
			// 校验考试权限
			checkCourseExamPower() {
				let idList = []
				this.courseList.forEach(item => {
					idList.push(item.id)
				})
				this.http({
					url: 'checkCourseExamPower',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.memberId,
						courseIdList: idList
					},
					success: res => {
						if (res.code == 0) {
							this.studyFinished = res.data
							this.totalRate = (parseFloat(res.msg) * 100).toFixed(0)
						}
					}
				});
			},
			listCourse() {
				this.http({
					url: 'listCourse',
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.memberId,
						examId: this.examId,
						showContent: true,
						authId: this.authId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.courseList = res.data
							this.checkCourseExamPower()
						}
					}
				});
			},
			// 获取考试列表
			listUnionExamRecord() {
				this.http({
					url: 'listUnionExamRecord',
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: this.memberId || 0,
						examId: this.examId,
						orderBy: 'u.recordCreTime DESC'
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.examRecordList = res.data
							for (let i = 0; i < this.examRecordList.length; i++) {
								if (this.examRecordList[i].recordState < 2 && this.notEndExamIndex == -1) {
									this.notEndExamIndex = i
								}
								if (this.examRecordList[i].recordPassed == 1) {
									this.recordPassed = true
								}
							}

							// 有未完成的考试，则提示跳转
							if (this.studyFinished && this.notEndExamIndex != -1) {
								// this.openCheck(2, '您有未完成的课程学习考核哦！', '确定前往考试中心参加吗？')
							}
						}
					}
				});
			},
			// 获取考试详情
			selectUnionExamById(value) {
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/unionExam/selectUnionExamById',
					method: 'GET',
					hideLoading: true,
					path: this.examId,
					success: res => {
						if (res.status == 200) {
							this.exam = res.data
							this.checkTime()
						}
					}
				});
				if (value == 0) {
					this.listUnionExamRecord()
					this.listCourse()
				}
			},
			formatLeaveTime(time) {
				let h = parseInt(time / (60 * 60))
				let m = parseInt((time - (h * 60 * 60)) / 60)
				let s = parseInt((time - (h * 60 * 60) - (m * 60)))
				h = h < 10 ? '0' + h : h
				m = m < 10 ? '0' + m : m
				s = s < 10 ? '0' + s : s
				let leaveTime = h + ':' + m + ':' + s
				return leaveTime
			},
			formatDateValue(value) {
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				return value
			},
			// 检查考试开始时间
			checkTime() {
				let timeStarted = new Date(this.formatDateValue(this.exam.examStarted)).getTime()
				let timeEnded = new Date(this.formatDateValue(this.exam.examEnded)).getTime()
				let nowDate = new Date().getTime()
				if (timeStarted <= nowDate && nowDate <= timeEnded) {
					this.examTimeState = 1
				} else if (nowDate < timeStarted) {
					this.examTimeState = 0
					let time = parseInt((timeStarted - nowDate) / 1000)
					// 开启倒计时
					if (time < 259200) {
						this.leaveTime = time
					}
				} else if (nowDate > timeEnded) {
					this.examTimeState = 2
				}
			},
			// 登录状态检查
			checkLogin() {
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.error('您还未进行登录哦，先去登录吧！')
					let url = '/pages-mine/exam/exam-entry?id=' + this.examId
					uni.setStorageSync('redirectUrl', url)
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						});
					}, 2000);
					return false
				} else {
					this.selectUnionExamById(0)
					return true
				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 1) {
					this.restartExamRecord()
				} else if (this.checkType == 2) {
					let index = this.notEndExamIndex
					if (index != -1) {
						let url = '/pages-mine/exam/exam-detail?id=' + this.examRecordList[index].id
						uni.navigateTo({
							url: url
						})
					}
				}
			},
		},
		watch: {
			time: {
				handler(newValue, oldVal) {
					if (this.leaveTime && this.leaveTime >= 0) {
						this.leaveTime--
					}
				},
				deep: true
			},
			leaveTime: {
				handler(newValue, oldVal) {
					if (this.leaveTime == 0) {
						this.examTimeState = 1
					}
				},
				deep: true
			}
		},
		onShow() {
			this.selectUnionExamById(0)
		},
		mounted() {
			this.checkLogin()
			let time = setInterval(() => {
				this.time++
			}, 1000)
		},
		onLoad(options) {
			if (options.scene) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/')
					obj[arr[0]] = arr[1]
				}
				this.examId = obj.id || -1
			}
			this.examId = options.id ? parseInt(options.id) : this.examId
			this.showShareImg = options.showShareImg ? true : false
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/exam.scss";

	// 底部按钮
	.bottom-small {
		button {
			padding: 0;
			width: 140rpx;
			height: 50rpx;
			line-height: 50rpx;
			color: #ffffff;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 28rpx;
		}
	}
</style>