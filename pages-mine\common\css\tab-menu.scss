	/*
		栏目菜单-通用样式
	*/
	
	// 头部一级菜单
	.head-menu {
		width: 100%;
	}

	.menu-title {
		display: block;
		margin-top: -50rpx;
		padding: 0rpx 50rpx;
		width: 87%;
		height: 100rpx;
		border-radius: 40rpx;
		background-color: #ffffff;

		text {
			height: 100rpx;
			line-height: 100rpx;
			font-size: 40rpx;
			font-weight: bold;
		}
	}

	// 二级选项菜单
	.work-menu {
		margin-top: -50rpx;
		display: block;
		padding: 0rpx 40rpx;
		width: 95%;
		height: 100rpx;
		border-radius: 40rpx;
		background-color: #ffffff;
	}

	// 二级选项选项
	.choice-menu {
		display: flex;
		width: 95%;
		justify-content: space-around;
		height: 40rpx;
		margin: 20rpx auto;
		padding: 10rpx 0;

		text {
			font-size: 32rpx;
		}
	}
	
	// 选择项目
	.choice-item {
		display: inline-block;
		padding: 0 10rpx;
		// margin: auto;
	}
	
	.activeChoice {
		// color: #2c2c78;
		font-size: 120% !important;
		font-weight: 700;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-bg.png') no-repeat bottom;
		background-size: 100% 25%;
		
		// #ifdef MP-TOUTIAO
		font-size: 100% !important;
		// #endif
	}

	// 筛选按钮
	.choice-filter {
		float: right;
	}
	
	/*
		筛选组件
	*/
   
   .popup-filter {
	   width: 600rpx;
   }

	// 筛选组件标题
	.filter-title {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;

		text {
			display: block;
			text-align: center;
			font-weight: bold;
			font-size: 40rpx;
		}
	}
	
	// 筛选组件内容
	.filter-content {
		width: 100%;
		height: 1000rpx;
	}

	.filter-tab {
		height: auto;
		width: 100%;
		background-color: #ffffff;
		
		text {
		}
	}
	
	.filter-scroll-Y {
		display: block;
		width: 100%;
		height: 750rpx;
	}
	
	.filter-scroll-Y-high {
		display: block;
		width: 100%;
		height: 1250rpx;
	}
	
	// 筛选选项栏目
	.tab {
		overflow: auto;
		min-height: 100rpx;
		height: auto;
		padding-bottom: 20rpx;
	}
	
	// 栏目标题
	.tab-title {
		width: 100%;
		height: 60rpx;
		margin: 0 0 10rpx 40rpx;
	
		text {
			display: bolck;
			width: 200rpx;
			height: 60rpx;
			line-height: 60rpx;
			font-size: 36rpx;
			font-weight: bold;
		}
		
		
		text:nth-child(2) {
			font-size: 32rpx;
			color: #909399;
			margin-left: 10rpx;
			font-weight: 100;
		}
		
		text:nth-child(3) {
			font-size: 32rpx;
			color: #909399;
			font-weight: 100;
		}
	}
	
	.tab-title-choice {
		display: flex;
		width: 100%;
		height: 60rpx;
		margin: 0 0 10rpx 40rpx;
		
		text {
			line-height: 60rpx;
			font-size: 36rpx;
			font-weight: bold;
		}
		
		img{
			margin: 8rpx 10rpx;
			width: 50rpx;
			height: 50rpx;
		} 
	}
	
	// 选择输入框
	.tab-picker {
		display: block;
		margin: 20rpx auto;
		width: 90%;
		height: 100rpx;
		background-color: #f9f9f9;
		border-radius: 20rpx;
		box-shadow: 2rpx 2rpx 10rpx #dedede;
		
	}
	
	.picker-text {
		float: left;
		display: block;
		width: 78%;
		height: 100rpx;
		line-height: 100rpx;
		padding-left: 40rpx;
		color: #87878c;
		font-size: 32rpx;
	}
	
	// 小箭头
	.picker-text-arrow {
		display: block;
		float: right;
		height: 100rpx;
		line-height: 100rpx;
		font-size: 36rpx;
		padding-right: 40rpx;
		color: #88888c;
	}
	
	// 筛选组件按钮
	.filter-button {
		position: fixed;
		z-index: 999;
		bottom: 0rpx;
		width: 100%;
		height: 120rpx;
		background-color: #ffffff;
		box-shadow: 0 4rpx 20rpx #dedede;
		padding: 0;
		display: flex;
		flex-direction: row;
	}

	.filter-button-left,
	.filter-button-right {
		width: 86%;
		height: 80rpx;
		// background-color: red;
		border-radius: 40rpx;
		margin: 20rpx 7%;
		text-align: center;
		line-height: 80rpx;

		text {
			height: 80rpx;
			font-size: 36rpx;
		}
		
		button {
			height: 80rpx;
			font-size: 36rpx;
		}
	}

	.filter-button-left {
		border: #dedede 1px solid;
		// color: #909399;
		background-color: #f9f9f9;
	}

	.filter-button-right {
		color: #fff;
		background-color: #1e1848;
	}
	
	.w82 {
		width: 82%;
	}
	
	/*
		筛选器选框部分
	*/
   
   .tab-checkbox-flex {
	   width: 90%;
	   display: flex;
	   flex-wrap: wrap;
   }
   
   .checkbox-flex {
   	   width: 25%;
	   height: 80rpx;
	   line-height: 80rpx;
	   margin: 20rpx 0 0 5%;
	   border-radius: 20rpx;
	   
	   background-color: #f9f9f9;
	   text {
			display: block;
			width: 100%;
			text-align: center;
			font-size: 32rpx;
	   }
   }
   
	
	.tab-checkbox { 
		float: left;
		display: block;
		width: 25%;
		height: 80rpx;
		line-height: 80rpx;
		margin: 4% 2.6% 2.6% 4%;
	}
	
	.checkbox {
		border-radius: 20rpx;
		background-color: #f9f9f9;
		
		text {
			display: block;
			width: 100%;
			text-align: center;
			font-size: 32rpx;
			white-space : nowrap;
		}
	}
	
	// 选框选中后样式
	.activeBox {
		color: #1e1848;
		background-color: #fff;
		border: #1e1848 3rpx solid;
		line-height: 74rpx;
	}
	
	.activeBoxFlex {
		color: #1e1848;
		background-color: #fff;
		border: #1e1848 3rpx solid;
		height: 76rpx;
		line-height: 76rpx;
	}
	
	// 范围输入框
	.tab-range {
		display: block;
		margin: 20rpx auto 0 5%;
		width: 78%;
		height: 100rpx;
		background-color: #f9f9f9;
		border-radius: 20rpx;
		box-shadow: 2rpx 2rpx 10rpx #dedede;
		display: flex;
	}
	
	
	// 范围输入框样式
	.range-input,
	.range-input1 {
		display: block;
		// 两个输入框时启用
		width: 44%;
		height: 80rpx;
		line-height: 80rpx;
		font-size: 32rpx;
		text-align: center;
		margin: 12rpx 0 0 0rpx;
		border-style: hidden;
	}
	
	.range-input-interval,
	.range-input-unit {
		display: block;
		text-align: center;
		height: 80rpx;
		line-height: 100rpx;
		margin: 0 4% 0 2%;
	}
	
	.range-input-unit {
		display: block;
		font-size: 36rpx;
		margin-right: -220rpx;
	}
	
	// 栏目输入框（单行高）
	.tab-inputbox {
		display: block;
		margin: 20rpx auto;
		width: 90%;
		height: 100rpx;
		background-color: #f9f9f9;
		border-radius: 20rpx;
		box-shadow: 2rpx 2rpx 10rpx #dedede;
	}
	
	.tab-inputbox-high {
		display: block;
		width: 90%;
		margin: 30rpx 40rpx;
	}
	
	// 单行输入框
	.single-input {
		display: block;
		float: left;
		width: 80%;
		height: 80rpx;
		line-height: 80rpx;
		padding-left: 30rpx;
		font-size: 32rpx;
		text-align: left;
		margin: 10rpx 0 0 20rpx;
		border-style: hidden;
	}
	
	// 多行输入框
	.multiline-input {
		padding: 20rpx 20rpx;
		width: 100%;
		height: 300rpx;
		line-height: 100rpx;
		border-radius: 20rpx;
		color: #000000;
		font-size: 36rpx;
	}
	
	// 气泡弹框
	.tips-box {
		margin: 10rpx 10%;
		width: 80%;
		height: 60rpx;
		line-height: 60rpx;
		text-align: center;
		color: #fff;
		font-size: 28rpx;
		background-color: rgba(30, 24, 72, 0.8);
		border-radius: 30rpx;
	}
	
