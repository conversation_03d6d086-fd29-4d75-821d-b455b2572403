<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<view class="head-img">
			<img :src="examCenterImg" mode="widthFix" />
			<view class="btn-mock f14" @click="openMock">
				模拟考试
			</view>
		</view>

		<view class="swiper-menu">
			<u-sticky>
				<u-tabs :list="menuList" @click="choiceMenu" :current="choiceIndex" lineWidth="22" lineHeight="8"
					:lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
						color: '#1e1848',
						fontWeight: 'bold',
						transform: 'scale(1.1)'
					}" :inactiveStyle="{
						color: '#333',
						transform: 'scale(1.05)'
					}" itemStyle="padding-left: 15px; padding-right: 15px; height: 45px;">
				</u-tabs>
			</u-sticky>
		</view>

		<!-- 信息 -->
		<view class="swiper-tab" v-for="(item, index) in list" :key="index">
			<view class="swiper-head" @click="openDetail(index)">
				<text class="swiper-title">{{item.examTitle}}</text>
				<text class="swiper-tips">{{menuList[choiceIndex].name}}</text>
			</view>
			<view class="swiper-content">
				<view class="content-left" @click="openImgPreview(item.examImg)">
					<img :src="item.examImg!=null&&item.examImg!=''?item.examImg:itemImg" alt="">
				</view>
				<view class="content-right" @click="openDetail(index)">
					<view class="content-title">
						<text>{{item.examContent|| '-'}}</text>
					</view>
					<view class="content-text">
						<text>{{item.examRemark|| ''}}</text>
					</view>

					<!-- 待考试 -->
					<view class="content-text" v-if="item.recordState != 2">
						<text>开始日期：{{item.examStarted}}</text>
					</view>
					<view class="content-text" v-if="item.recordState != 2">
						<text>结束日期：{{item.examEnded}}</text>
					</view>
					<view class="content-text" v-if="item.recordState != 2">
						<text>考试限时：{{item.examDuration}}分钟</text>
					</view>
					<!-- 							<view class="content-text" v-if="item.recordState != 2">
								<text>合格：{{item.examPassScore}}分</text>
							</view> -->
					<view class="content-text" v-if="item.recordState != 2">
						<text>满分：{{item.examScore}}分</text>
					</view>
					<view class="content-text" v-if="item.recordState == 1">
						<text style="color: #ff4d4b;">状态：考试中</text>
					</view>

					<!-- 已考 -->
					<view class="content-text" v-if="item.recordState == 2">
						<text>开考时间：{{item.startedTime}}</text>
					</view>
					<view class="content-text" v-if="item.recordState == 2">
						<text>交卷时间：{{item.endedTime}}</text>
					</view>
					<view class="content-text" v-if="item.recordState == 2">
						<text>得分：{{item.recordScore}}分</text>
					</view>
					<view class="content-text" v-if="item.recordState == 2&&item.recordRemark">
						<text>备注：{{item.recordRemark}}</text>
					</view>
					<view class="content-text" v-if="item.recordState == 4">
						<text style="color: #ff4d4b;">状态：历史考试</text>
					</view>

					<u-gap height="20"></u-gap>
				</view>
			</view>
		</view>

		<u-empty v-if="!list.length" text="暂无考试记录" icon="http://cdn.uviewui.com/uview/empty/data.png" />

		<u-gap height="100"></u-gap>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				memberId: uni.getStorageSync('memberId') || 0,
				employeeId: uni.getStorageSync('employeeId') || 0,
				authId: uni.getStorageSync('authId') || 0,
				choiceIndex: 0,
				// 获取问卷团队授权需要的参数
				itemImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/default_img.png",
				examCenterImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/exam-center.png",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},

				list: [],
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				menuList: [{
					name: '待考试',
					stateList: [0, 1]
				}, {
					name: '已考试',
					stateList: [2, 4]
				}, {
					name: '可报名',
					stateList: null
				}]
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 点击切换菜单
			choiceMenu(e) {
				this.choiceIndex = e.index
				if (this.choiceIndex != 2) {
					this.listUnionExamRecord()
				} else {
					this.listUnionExam()
				}
			},
			// 打开图片预览
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 打开详情
			openDetail(index) {
				let url = "/pages-mine/exam/exam-detail?id=" + this.list[index].id
				if (this.choiceIndex == 2) {
					url = "/pages-mine/exam/exam-application?id=" + this.list[index].id
				}
				uni.navigateTo({
					url: url
				})
			},
			// 打开模拟考
			openMock() {
				uni.navigateTo({
					url: '/pages-mine/exam/exam-mock'
				})
			},
			listUnionExam() {
				this.http({
					url: 'listUnionExam',
					method: 'POST',
					hideLoading: true,
					data: {
						authId: this.authId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.list = res.data
						} else {
							this.list = []
						}
					}
				});
			},
			// 获取考试列表
			listUnionExamRecord() {
				let data = {
					memberId: this.memberId,
					recordStateList: this.menuList[this.choiceIndex].stateList,
					orderBy: 'u.id DESC'
				}
				this.http({
					url: 'listUnionExamRecord',
					method: 'POST',
					hideLoading: true,
					data: data,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.list = res.data
						} else {
							this.list = []
						}
					}
				});
			},
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			popupCheck() {
				if (this.checkType == 0) {}
			},
		},
		onLoad(options) {
			this.choiceIndex = options.choiceIndex || 0
			let e = {
				index: this.choiceIndex
			}
			this.choiceMenu(e)
		}
	}
</script>

<style lang="scss">
	@import "@/pages-mine/common/css/swiper-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}

	.btn-mock {
		position: absolute;
		top: 20rpx;
		right: 40rpx;
		border-radius: 20rpx;
		padding: 0 20rpx;
		height: 45rpx;
		line-height: 45rpx;
		color: #ffffff;
		background-color: #1e1848;
	}
</style>