<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 投票 -->
		<u-popup :show="showPopup" @close="showPopup = false" round="10">
			<view class="f18 fb text-c lh60">我的投票</view>
			<scroll-view scroll-y="true" class="scroll-Y">
				<view class="f16 lh50 c6">
					当前投票给：<text>{{choiceName}}</text>
				</view>
			</scroll-view>

			<view class="filter-button" v-if="!voted">
				<view style="width: 40%;height: 120rpx;">
					<view class="filter-button-left" @click="showPopup = false">
						<text>取消</text>
					</view>
				</view>
				<view style="width: 60%;height: 120rpx;" @click="insertExcellentEmployeeVote()">
					<view class="filter-button-right">
						<text>提交</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 投票记录 -->
		<u-popup :show="showPopupVote" @close="showPopupVote = false" round="10">
			<view class="f18 fb text-c lh60">投票记录</view>
			<scroll-view scroll-y="true" class="scroll-Y mg-at f16 lh25" style="height: 1000rpx;">
				<view class="small-tab" v-for="(item,index) in voteList" :key="index">
					<view class="flac-row">
						<view class="w2">
							<u-avatar :src="formatHeadImg(item)" size="50" />
						</view>
						<view class="flac-col w65" style="padding: 10rpx 0;">
							<text style="display: block;">{{formatName(item)}}</text>
							<text style="display: block;">{{item.createTime}}</text>
						</view>
						<view class="w15">
							<text>{{rewardAuth&&showVoteDetail?item.excellentEmployeeName:'已投'}}</text>
						</view>
					</view>
				</view>
				<u-empty v-if="voteList.length==0" text="暂无投票" icon="http://cdn.uviewui.com/uview/empty/data.png" />
			</scroll-view>
		</u-popup>

		<!-- 入口分享图片 -->
		<view class="img-view" v-if="showShareImg">
			<img class="w10 mg-at" :src="shareImg" alt="" mode="widthFix" />
		</view>

		<view v-if="!showShareImg">
			<view>
				<view class="tab">
					<view class="tab-head" v-if="pageType==0">
						<text>{{venueId?'选择本场标杆':'选择今日标杆'}}</text>
					</view>
					<view class="tab-head-smail flac-col" v-if="venue">
						<text>投票主题：{{venue.venueTitle}}</text>
						<text v-if="venue.venueContent">投票内容：{{venue.venueContent}}</text>
					</view>
					<view class="tab-head" v-if="pageType==1" @click="openVoteRecord">
						<text>总投票数：{{totalVoteNum}}</text>
					</view>
					<view class="w10 flac-row mg-at" style="flex-wrap: wrap;margin-bottom: 20rpx;">
						<view class="flex-col-c w5" v-for="(item,index) in excellentList" :key="index"
							@click="pageType==0?choiceItem(index):openDetail(index)"
							@longpress="openImgPreview(formatHeadImg(item))">
							<view class="flac-col-c">
								<view class="img-tab" style="position: relative;">
									<img :src="formatHeadImg(item)"
										:class="choiceIndex==index&&pageType==0?'active':''">
									</img>
									<view class="img-tab-bar">
										{{item.name}}
									</view>
								</view>
								<view class="rate-bar f16" v-if="pageType==1"
									:style="[formatStyle(totalVoteNum!=0?item.voteNum/totalVoteNum:0)]">
									{{item.voteNum}}票 {{ (totalVoteNum!=0?item.voteNum/totalVoteNum*100:0).toFixed(1)}}%
								</view>
							</view>
						</view>
					</view>
					<view class="tab-head-smail" v-if="pageType==0">
						<text style="color: #ff4d4b;">选择您最认同的{{venueTips}}标杆，为Ta投一票吧！</text>
					</view>
					<view class="tab-head-smail" @click="showVoteCount++" v-if="!venue">
						<text>评选日期：{{dateParam}}</text>
					</view>
					<view class="tab-head-smail flac-col" @click="showVoteCount++" v-if="venue">
						<text>开始日期：{{venue.startTime}}</text>
						<text>结束日期：{{venue.endTime}}</text>
					</view>
				</view>
			</view>

			<view v-if="pageType==0&&excellentEmployee">
				<view class="main-bar f16" v-if="excellentEmployee">
					<view class="flac-col" style="padding: 0 20rpx;">
						<view class="flac-row">
							<view class="w2" @click="openImgPreview(formatHeadImg(excellentEmployee))">
								<u-avatar :src="formatHeadImg(excellentEmployee)" size="50" />
							</view>
							<view class="flac-col w6" style="padding: 10rpx 0;" @click="openDetail(choiceIndex)">
								<text class="main-text f18"
									style="display: block;">{{formatName(excellentEmployee)}}({{excellentEmployee.depart}})</text>
								<text style="display: block;">{{excellentEmployee.createDate}}</text>
							</view>
						</view>
						<view style="margin: 15rpx 0rpx;" v-if="formatCreatorName(excellentEmployee)"
							@click="openDetail(choiceIndex)">
							获得<text class="main-text"
								style="margin: 0 10rpx;">{{formatCreatorName(excellentEmployee)}}</text>的赞赏
						</view>
						<view style="margin: 10rpx 0rpx;" @click="openDetail(choiceIndex)">
							<view v-if="excellentEmployee.introduce">工作介绍：{{excellentEmployee.introduce}}</view>
							<view v-if="excellentEmployee.workDeeds">工作事迹：{{excellentEmployee.workDeeds}}</view>
						</view>
						<view v-if="excellentEmployee.dailyReviewId"
							style="color: #00aaff;text-decoration:underline;margin: 20rpx 0;">
							<text
								@click="goPage('/pages-other/excitation/review-detail?id='+excellentEmployee.dailyReviewId)">每日复盘详情
							</text>
						</view>
						<view class="w10" style="margin: 10rpx auto;">
							<u-album :maxCount="maxShowPhoto" rowCount="3" multipleSize="90" space="5.5"
								:urls="excellentEmployee.excellentEmployeeImgList" />
						</view>
					</view>
				</view>
			</view>

			<!-- 菜单 -->
			<view class="flac-row-b w8 mg-at"
				style="margin: 20rpx auto 40rpx auto;padding: 10rpx 40rpx;;background-color: #f4f4f5;border-radius: 20rpx;">
				<view class="flac-col" style="align-items: center;" v-for="(item,index) in tabList" :key="index"
					v-if="item.show" @click="openTab(index)">
					<button plain="true" style="border: none;" :open-type="index==(tabList.length-1)?'share':''">
						<uni-icons :type="item.icon" size="20" color="#1e1848"></uni-icons>
					</button>
					<text>{{item.text}}</text>
				</view>
			</view>

			<view class="flac-col f18 w9 mg-at lh30">
				<view class="fb">
					投票规则
				</view>
				<view class="" v-for="(item,index) in ruleList" :key="index">
					{{index+1}}.{{item}}
				</view>
			</view>

			<view class="btn-bottom-fixed" @click="voted?'':showPopup=true" v-if="pageType==0">
				<button :style="!voted?'background-color:#1e1848;':'background-color:#909399;color:#fff'">
					{{voted?venueTips+'已投票':'选择好了 为Ta投票'}}
				</button>
			</view>
			<view class="btn-bottom-fixed" @click="excellentEmployeeList" v-else>
				<button style="background-color:#1e1848;">
					刷新投票结果
				</button>
			</view>
		</view>

		<u-gap height="400"></u-gap>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 打赏权限
				rewardAuth: false,
				// 是否显示投票给谁
				showVoteDetail: false,
				// 是否显示分享图片
				showShareImg: false,
				// 页面类型（0：投票 1：投票数据）
				pageType: 0,
				// 最大显示图片数
				maxShowPhoto: 3,

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				showVoteCount: 0,
				choiceIndex: 0,
				choiceName: '-',
				excellentEmployee: null,
				totalVoteNum: 0,
				excellentEmployeeVote: {
					excellentEmployeeId: 0,
					memberId: uni.getStorageSync("memberId") || 0,
					employeeId: uni.getStorageSync("employeeId") || null,
				},
				excellentList: [],
				voteList: [],
				voted: false,
				showPopup: false,
				showPopupVote: false,
				total: 0,
				pageCount: 0,
				searchCondition: {
					orderBy: "t.createTime DESC",
					commentState: 0,
					current: 1,
					size: 10
				},
				shareImg: '',
				blankHeadImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				defaultPost: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/vote_share.png",
				memberId: uni.getStorageSync("memberId") || 0,
				employeeId: uni.getStorageSync("employeeId") || null,
				dateParam: '',
				tabList: [{
						text: '首页',
						icon: 'home',
						url: '/pages-other/excitation/index',
						show: true

					},
					{
						text: '我的记录',
						icon: 'calendar',
						url: '',
						show: true
					}, {
						text: '投票结果',
						icon: 'medal',
						url: '',
						show: true
					}, {
						text: '分享投票',
						icon: 'redo',
						url: '',
						show: true
					},
				],
				ruleList: [
					'本次投票为公开投票，可分享所有人。',
					'本次投票为单选，投票后无法撤回。',
					'每个人当天只能投出一票。', '参与投票后可查看票数。',
				],
				venueTips: '今日',
				venueId: null,
				venue: null
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 格式化时间
			formatDate(value) {
				if (value == null) {
					return "-"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '-' + MM + '-' + d
			},
			formatName(val) {
				return val.name || val.employeeName || val.memberName || '匿名用户'
			},
			formatCreatorName(val) {
				if (val.selfReference && val.selfReference == val.name) {
					return '自己'
				}
				let result = val.reference || val.selfReference || val.creatorName || ''
				let depart = val.creatorDepart ? '(' + val.creatorDepart + ')' : ''
				result += result ? depart : ''
				return result
			},
			formatHeadImg(val) {
				return val.employeeHeadImg || val.memberHeadImg || this.blankHeadImg
			},
			formatStyle(val) {
				val = (val * 100.0).toFixed(1)
				let str = val + '%100%'
				let style = {}
				this.$set(style, 'backgroundSize', str)
				return style
			},
			// 选择员工
			choiceItem(index) {
				if (this.pageType == 1) {
					return
				}
				this.choiceIndex = index
				this.excellentEmployee = this.excellentList[index]
				this.choiceName = this.formatName(this.excellentEmployee)
			},
			// 跳转页面
			goPage(url) {
				uni.navigateTo({
					url: url
				})
			},
			// 打开详情
			openDetail(index) {
				uni.navigateTo({
					url: '/pages-other/excitation/excitation-detail?id=' + this.excellentList[index].id
				})
			},
			// 打开菜单
			openTab(index) {
				let item = this.tabList[index]
				if (item.url) {
					uni.navigateTo({
						url: item.url
					})
				}
				if (index == 1) {
					return this.$refs.uNotify.warning('功能开发中，敬请期待！')
				}
				if (index == 2) {
					this.pageType = this.voted ? 1 : 0
					if (!this.voted) {
						return this.$refs.uNotify.warning('投票后才能查看哦！')
					}
					// if (this.pageType == 1) {
					// 	this.excellentEmployeeList()
					// }
				}
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 打开投票记录
			openVoteRecord() {
				// if (!this.rewardAuth) {
				// 	return
				// }
				this.showPopupVote = true
			},
			checkLogin() {
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.warning("您还未进行登录哦，请先登录再来投票吧！")
					uni.setStorageSync('redirectUrl', '/pages-other/excitation/excitation-vote?dateParam=' + this
						.dateParam)
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						})
					}, 2000)
					return false
				} else {
					return true
				}
			},
			// 获取当前投票场次
			getExcellentEmployeeVoteVenueById() {
				this.http({
					url: 'getExcellentEmployeeVoteVenueById',
					method: 'GET',
					path: this.venueId || 0,
					success: res => {
						if (res.code == 0) {
							this.venue = res.data
						}
					},
				})
			},
			// 获取优秀员工信息
			excellentEmployeeList() {
				let data = {
					createDate: this.venueId ? null : this.dateParam + ' 00:00:00',
					venueId: this.venueId,
					orderBy: this.pageType == 1 ? 'voteNum DESC' : 't.id ASC'
				}
				this.http({
					url: 'excellentEmployeeList',
					method: 'POST',
					hideLoading: this.pageType == 1 && this.excellentList.length ? false : true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: data,
					success: res => {
						if (res.code == 0) {
							this.excellentList = res.data
							this.excellentEmployee = res.data[this.choiceIndex]
							this.choiceName = this.formatName(this.excellentEmployee)
							this.totalVoteNum = 0
							for (let item of this.excellentList) {
								this.totalVoteNum += item.voteNum
							}
							this.listExcellentEmployeeVote(1)
						} else {
							this.$refs.uNotify.error(this.venueTips + '标杆候选员工还未出炉哦，晚点再来看看吧！')
						}
					},
				})
			},
			// 进行投票
			insertExcellentEmployeeVote() {
				if (!this.checkLogin()) {
					return
				}
				this.showPopup = false
				uni.showModal({
					title: '确定为Ta投票吗？',
					content: this.venueTips + '只能投票一次，且确认后不可撤回！',
					success: (res) => {
						if (res.confirm) {
							let id = this.excellentList[this.choiceIndex].id || null
							this.$set(this.excellentEmployeeVote, "excellentEmployeeId", id)
							this.$set(this.excellentEmployeeVote, "venueId", this.venueId || 0)
							this.http({
								url: 'insertExcellentEmployeeVote',
								method: 'POST',
								header: {
									'content-type': 'application/json;charset=UTF-8'
								},
								data: this.excellentEmployeeVote,
								success: res => {
									if (res.code == 0) {
										this.voted = true
										this.showPopup = false
										this.$refs.uNotify.success("已成功投票！")
										this.searchCondition.current = 1
										this.excellentEmployeeList()
									} else {
										this.$refs.uNotify.error(res.msg)
									}
								},
							})
						}
					}
				});
			},
			// 获取优秀员工投票（校验今天是否已经投票过）
			listExcellentEmployeeVote(value) {
				this.http({
					url: 'listExcellentEmployeeVote',
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: {
						memberId: value == 0 ? (this.memberId || 0) : null,
						createTime: this.dateParam + ' 00:00:00',
						venueId: this.venueId || 0,
						orderBy: 't.id ASC'
					},
					success: res => {
						if (res.code == 0) {
							if (value == 0) {
								this.voted = true
							} else {
								this.voteList = res.data || []
							}
						}
					},
				})
			},
			// 获取分享图片
			getShareImg() {
				if (!this.showShareImg) {
					return
				}

				let scene = "t/" + this.dateParam
				let tips = this.venueId ? '' : '今日'
				let content = this.venue ? this.venue.venueTitle : this.dateParam
				let param = {
					textList: [{
						"text": tips + '标杆员工' + content,
						// "text": '今日标杆员工投票',
						"fontSize": 50,
						"isCenter": true,
						"color": "0xfed472",
						"isBold": true,
						"x": 0,
						"y": 1020
					}],
					qrCodeStyle: {
						"width": 350,
						"height": 350,
						"x": 200,
						"y": 250
					},
					maxWidth: 600,
					img: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/QR_img/QR_examEntry.png',
					path: 'pages-other/excitation/excitation-vote',
					scene: scene,
					source: "xyjacn",
					type: 1
				}
				this.http({
					outsideUrl: 'https://biapi.xiaoyujia.com/image/getWxShareImg',
					method: 'POST',
					data: param,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.status == 200) {
							this.shareImg = res.data
						}
					},
					fail: err => {
						this.$refs.uNotify.erro("获取分享图片-请求失败！" + err)
					}
				})
			},
			// 校验打赏权限
			checkRewardAuth() {
				this.http({
					url: 'checkRewardAuth',
					method: 'POST',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					hideLoading: true,
					data: {
						roleId: uni.getStorageSync('roleId') || 0,
						employeeId: uni.getStorageSync('employeeId') || null,
					},
					success: res => {
						if (res.code == 0) {
							this.rewardAuth = true
						}
					},
				})
			},
			// 分享到好友或朋友圈
			onShareAppMessage(res) {
				let img = this.defaultPost
				return {
					title: '标杆员工评选，请投出您宝贵的一票吧！',
					path: '/pages-other/excitation/excitation-vote',
					mpId: 'wx8342ef8b403dec4e',
					imageUrl: img
				}
			},
		},
		watch: {
			showVoteCount: {
				handler(newValue, oldVal) {
					if (this.showVoteCount > 3) {
						this.showVoteDetail = true
					}
				},
				deep: true
			}
		},
		onLoad(options) {
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
				this.dateParam = obj.t || this.dateParam
				this.venueId = obj.v || this.venueId
			}
			this.dateParam = options.dateParam || this.dateParam || this.formatDate(new Date())
			this.showShareImg = options.showShareImg ? true : false
			this.pageType = options.pageType || this.pageType
			this.venueId = options.venueId || this.venueId
			this.venueTips = this.venueId ? '本场' : '今日'
			this.excellentEmployeeList()
			this.getExcellentEmployeeVoteVenueById()
			this.listExcellentEmployeeVote(0)
			this.getShareImg()
			this.checkRewardAuth()
			let dataTips = this.venueId ? '规定时间内' : this.dateParam + ' 17:30~18:30'
			this.ruleList.push(dataTips + '可参与投票。')
		},
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";
	@import "@/pages-mine/common/css/resume-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}

	.main-text {
		color: #1e1848;
		font-weight: bold;
	}

	.main-bar {
		width: 94%;
		height: auto;
		padding: 0rpx 3%;
		margin: 20rpx 0;
	}

	.rate-bar {
		text-align: center;
		width: 278rpx;
		height: 80rpx;
		line-height: 80rpx;
		border: 1rpx #dedede solid;
		background-color: #f4f4f5;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/rateBar.png') no-repeat left;
	}

	.small-tab {
		width: 100%;
		height: 140rpx;
		border-bottom: 2rpx #dedede solid;
		margin: 20rpx 0;
	}

	.img-tab {
		img {
			width: 280rpx;
			height: 280rpx;
			display: block;
			margin: 40rpx auto 0rpx auto;
		}
	}

	.img-tab-bar {
		position: absolute;
		bottom: 0;
		color: #fff;
		font-size: 32rpx;
		width: 280rpx;
		height: 60rpx;
		line-height: 60rpx;
		text-align: center;
		background-image: linear-gradient(to bottom, rgba(47, 47, 47, 0.1), rgba(47, 47, 47, 0.9));
	}

	.list-bottom {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;

		text {
			display: block;
			text-align: center;
			font-weight: 100;
			color: #909399;
		}
	}

	.scroll-Y {
		display: block;
		width: 90%;
		height: 850rpx;
		margin: 0rpx 5%;
	}

	.active {
		border-top: 10rpx #19be6b solid;
		border-left: 10rpx #19be6b solid;
		border-right: 10rpx #19be6b solid;
		width: 220rpx;
		height: 300rpx;
		// margin: 10rpx 10rpx;
	}
</style>