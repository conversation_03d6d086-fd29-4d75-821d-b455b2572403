<template>
	<view v-if="show">
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<view class="tab">
			<view class="tab-head">
				<text>基本信息</text>
			</view>
			<view class="tab-head-smail"><text>姓名：{{employee.realName}}（{{resumeScore}}分）</text></view>
			<view class="tab-head-smail" v-if="employee.workType"><text>求职意向：{{employee.workType}}</text></view>
			<view class="tab-head-smail"><text>预估工资：{{salary}}</text></view>
			<view class="tab-head-smail"><text>提审人：{{updaterName}}（{{updaterNo}}）</text></view>
			<view class="tab-head-smail"><text>提审等级：{{levelName}}</text></view>
			<view class="tab-head-smail"><text>提审时间：{{updateTime}}</text></view>
			<view class="f16 lh30" style="margin: 0 40rpx;color:#19be6b">
				<text>小贴士：请检查员工头像、证件等信息，若存在不合格，可将提审驳回！驳回后员工将暂时下架，并通知提审人。</text>
			</view>
		</view>

		<view class="tab">
			<view class="tab-head">
				<text>个人头像</text>
			</view>
			<scroll-view class="w88 mg-at" :scroll-x="true">
				<view class="flac-row">
					<view class="flex-col-c" style="margin: 40rpx 20rpx;position: relative;"
						@click="openImgPreview(employee.headPortrait)">
						<img style="width: 240rpx;height: 320rpx;" mode="widthFix"
							:src="employee.headPortrait||blankImg" />
					</view>
				</view>
			</scroll-view>
		</view>


		<view class="tab">
			<view class="tab-head">
				<text>证件照片</text>
			</view>
			<scroll-view class="w9 mg-at" :scroll-x="true">
				<view class="flac-row">
					<view class="flex-col-c" style="margin: 40rpx 20rpx;position: relative;"
						v-for="(item,index) in certList" :key="index" v-if="showCert(item)"
						@click="openImgPreview(item.certificateImg)">
						<img style="width: 240rpx;height: 320rpx;" mode="widthFix" :src="item.certificateImg" />
					</view>
				</view>
			</scroll-view>
		</view>

		<view class="tab">
			<view class="tab-head">
				<text>驳回原因</text>
				<text></text>
			</view>
			<view class="flac-row" style="margin: 0 40rpx;">
				<img @click="baomuAuthReject.unqualifiedHead=baomuAuthReject.unqualifiedHead==1?0:1"
					:src="baomuAuthReject.unqualifiedHead==1?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty1.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty0.png'"
					mode="widthFix" style="width: 60rpx;height: 60rpx;" />
				<text style="margin-right: 80rpx;">头像不合格</text>
				<img @click="baomuAuthReject.unqualifiedCert=baomuAuthReject.unqualifiedCert==1?0:1"
					:src="baomuAuthReject.unqualifiedCert==1?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty1.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/login/ty0.png'"
					mode="widthFix" style="width: 60rpx;height: 60rpx;" />
				<text>证件不合格</text>
			</view>
			<view class="tab-inputbox">
				<view class="tab-input">
					<input class="single-input" type="text" v-model="baomuAuthReject.remark" placeholder="请输入驳回原因" />
				</view>
			</view>
		</view>

		<view class="btn-group" style="padding: 80rpx 0;">
			<button @click="baomuAuthReject.remark=''">清空备注</button>
			<button @click="tryReject">确定驳回</button>
		</view>

		<view class="tab" v-if="rejectList.length">
			<view class="tab-head">
				<text>驳回记录</text>
			</view>
			<view class="reject-tab" v-for="(item,index) in rejectList" :key="index">
				<view class="tab-head-smail"><text>序号：{{index+1}}</text></view>
				<view class="tab-head-smail">
					<text>驳回人：{{item.rejectEmployeeName}}（{{item.rejectEmployeeNo}}）</text>
				</view>
				<view class="tab-head-smail"><text>驳回原因：{{item.remark}}</text></view>
				<view class="tab-head-smail"><text>驳回时间：{{item.createTime}}</text></view>
			</view>
		</view>

		<u-gap height="80"></u-gap>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},

				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_jj.png",
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,
				employee: {},
				baomuId: null,
				baomuAuthId: 0,
				levelName: '',
				salary: '',
				updaterNo: '',
				updaterName: '',
				updateTime: '',
				resumeScore: '',
				baomuAuthReject: {
					baomuAuthId: null,
					rejectEmployeeId: uni.getStorageSync('employeeId') || null,
					rejectType: 0,
					unqualifiedHead: 0,
					unqualifiedCert: 0,
					remark: ''
				},
				certList: [],
				rejectList: [],
				show: null
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			showCert(data) {
				let type = data.certificateType
				let showTypeList = []
				let result = false
				let typeList = [1, 2, 3, 4, 6, 7, 8, 9, 10, 20, 21, 22, 23]
				typeList.forEach(item => {
					if (item == type) {
						result = true
					}
				})
				return result
			},
			// 打开图片预览
			openImgPreview(url) {
				let data = []
				data.push(url)
				uni.previewImage({
					urls: data,
					current: url
				})
			},
			listBaomuAuthReject() {
				this.http({
					url: 'listBaomuAuthReject',
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					method: 'POST',
					data: {
						baomuAuthId: this.baomuAuthId || 0
					},
					success: res => {
						if (res.code == 0) {
							this.rejectList = res.data
						}
					}
				});
			},
			tryReject() {
				let remark = ''
				if (!this.baomuAuthReject.remark) {
					if (this.baomuAuthReject.unqualifiedHead == 1) {
						remark += '头像'
					}
					if (this.baomuAuthReject.unqualifiedCert == 1) {
						remark += '证件'
					}
					this.baomuAuthReject.remark = remark.length ? remark + '不合格。' : ''
				}
				this.openCheck(0, '确定进行驳回吗？', '员工将暂时下架并通知提审人！')
			},
			insertBaomuAuthReject() {
				if (!this.baomuAuthReject.remark) {
					this.$refs.uNotify.error('请填写驳回原因！')
				}
				this.baomuAuthReject.baomuAuthId = this.baomuAuthId
				this.http({
					url: 'insertBaomuAuthReject',
					method: 'POST',
					hideLoading: true,
					data: this.baomuAuthReject,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.$refs.uNotify.success('驳回成功！')
							this.listBaomuAuthReject()
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					}
				})
			},
			// 获取员工已经上传的证件
			getCertificateByEmployeeId() {
				this.http({
					url: 'getCertByEmployeeId',
					method: 'GET',
					hideLoading: true,
					path: this.baomuId || 0,
					success: res => {
						if (res.code == 0) {
							this.certList = res.data
						} else {
							this.certList = []
						}
					},
				});
			},
			// 获取保姆详细信息成功
			getBaomuDetail() {
				this.http({
					url: 'getBaomuById',
					method: 'GET',
					hideLoading: true,
					path: this.baomuId,
					success: res => {
						if (res.code == 0) {
							this.employee = res.data
							this.resumeScore = this.employee.resumeScore
						}
					}
				});
			},
			// 获取员工技能鉴定记录
			getEmployeeSkill() {
				this.http({
					url: 'getEmployeeSkill',
					method: 'POST',
					hideLoading: true,
					data: {
						employeeId: this.baomuId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.levelName = res.data.levelName
							this.salary = res.data.salary
							this.updaterNo = res.data.updaterNo
							this.updaterName = res.data.updaterName
							this.updateTime = res.data.updateTime
							this.baomuAuthId = res.data.baomuAuthId
							this.getBaomuDetail()
							this.getCertificateByEmployeeId()
							this.listBaomuAuthReject()
						} else {
							console.log("鉴定记录获取失败！员工之前还未鉴定过！")
						}
					}
				})
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				if (this.checkType == 0) {
					this.insertBaomuAuthReject()
				}
			},
		},
		onLoad(options) {
			this.show = options.show || null
			this.baomuId = options.baomuId || 0
			this.getEmployeeSkill()
		},
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";
	@import "@/pages-mine/common/css/resume-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}

	.reject-tab {
		width: 90%;
		height: auto;
		box-shadow: 0 4rpx 20rpx #dedede;
		margin: 40rpx auto;
		padding: 20rpx 0;
		border-radius: 20rpx;
	}
</style>