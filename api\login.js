import {requestUtil} from "../util/requestUtil.js"

/**
 * 获取验证码
 * @param {Object} postParam
 */
export const getImageVerifyCode = function(postParam) {
	requestUtil({
		url: 'get_image_verify_code',
		method: 'GET',
		success: postParam.onSuccess,
		complete: postParam.onComplate
	})
}

/**
 * 用户登录
 * @param {Object} postParam
 */
export const login = function(postParam) {
	requestUtil({
		url: 'login',
		method: 'POST',
		data: postParam.data,
		success: postParam.onSuccess,
		fail: postParam.onFail,
		complete: postParam.onComplate
	})
}

export const modifyPwd = function(postParam) {
	requestUtil({
		url: 'user/info/modifyPwdAddOne',
		method: 'POST',
		data: postParam.data,
		success: postParam.onSuccess,
		complete: postParam.onComplate
	})
}

/**
 * 发送验证码
 * @param {Object} postParam
 */
export const sendSms = function(postParam) {
	requestUtil({
		url: '/member/sendLoginCode',
		method: 'POST',
		data: postParam.data,
		success: postParam.onSuccess,
		fail: postParam.onFail,
		complete: postParam.onComplate
	})
}

/**
 * 用户登录
 * @param {Object} postParam
 */
export const memberLogin = function(postParam) {
	requestUtil({
		url: '/member/memberLogin',
		method: 'POST',
		data: postParam.data,
		success: postParam.onSuccess,
		fail: postParam.onFail,
		complete: postParam.onComplate
	})
}

/**
 * 授权登录
 * @param {Object} postParam
 */
export const wechatOpenLogin = function(postParam) {
	requestUtil({
		url: '/member/wechatOpenLogin',
		method: 'POST',
		data: postParam.data,
		success: postParam.onSuccess,
		fail: postParam.onFail,
		complete: postParam.onComplate
	})
}