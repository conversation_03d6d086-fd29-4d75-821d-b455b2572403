<template>
	<view class="container">
		<!-- 合同信息 -->
		<view class="contract-info" v-if="contractData">
			<view class="info-item">
				<text class="label">合同编号：</text>
				<text class="value">{{ contractData.no }}</text>
			</view>
			<view class="info-item">
				<text class="label">客户姓名：</text>
				<text class="value">{{ contractData.memberName }}</text>
			</view>
			<view class="info-item">
				<text class="label">服务员工：</text>
				<text class="value">{{ contractData.employeeName }}</text>
			</view>
		</view>
		<!-- 操作步骤说明 -->
		<view class="steps-guide">
			<text class="guide-title">操作步骤</text>
			<view class="step-item">
				<view class="step-number">1</view>
				<text class="step-text">先获取合同链接下载</text>
			</view>
			<view class="step-item">
				<view class="step-number">2</view>
				<text class="step-text">线下打印签署，拍照上传</text>
			</view>
			<view class="step-item">
				<view class="step-number">3</view>
				<text class="step-text">点击完成上传</text>
			</view>
		</view>
		
		<!-- 已上传的纸质合同照片 -->
		<view class="uploaded-images" v-if="uploadedImages.length > 0">
			<text class="section-title">已上传的纸质合同照片</text>
			<view class="image-list">
				<view class="image-item" v-for="(image, index) in uploadedImages" :key="index">
					<image :src="image.enclosureUrl" mode="aspectFit" @click="previewImage(image.enclosureUrl)"></image>
				</view>
			</view>
		</view>
		
		<!-- 操作步骤说明 -->
		<view class="steps-guide">
			<text class="guide-title">操作步骤</text>
			<view class="step-item">
				<view class="step-number">1</view>
				<text class="step-text">先获取合同链接下载</text>
			</view>
			<view class="step-item">
				<view class="step-number">2</view>
				<text class="step-text">线下打印签署，拍照上传</text>
			</view>
			<view class="step-item">
				<view class="step-number">3</view>
				<text class="step-text">点击完成上传</text>
			</view>
		</view>
		
		<!-- 功能按钮 -->
		<view class="button-group">
			<u-button 
				text="获取纸质合同链接" 
				color="#1e1848" 
				size="large" 
				customStyle="margin-bottom: 20rpx;"
				@click="downloadContract">
			</u-button>
			
			<u-button 
				text="上传纸质合同照片" 
				type="primary" 
				size="large"
				customStyle="margin-bottom: 20rpx;"
				@click="uploadImage">
			</u-button>
			
			<u-button 
				text="完成上传" 
				type="success" 
				size="large"
				@click="successUpdate">
			</u-button>
		</view>
		
		<!-- 加载提示 -->
		<u-loading-page :loading="loading" loading-text="加载中..."></u-loading-page>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				no: '', // 合同编号
				contractData: null, // 合同数据
				uploadedImages: [], // 已上传的图片
				loading: false
			};
		},
		onLoad(option) {
			this.no = option.no;
			if (this.no) {
				this.initData();
			}
		},
		methods: {
			// 初始化数据
			async initData() {
				this.loading = true;
				try {
					await this.getContractInfo();
					await this.getUploadedImages();
				} catch (error) {
					console.error('初始化数据失败:', error);
				} finally {
					this.loading = false;
				}
			},
			
			// 获取合同信息
			getContractInfo() {
				return new Promise((resolve, reject) => {
					this.http({
						url: 'getContractByNo',
						method: 'GET',
						path: this.no,
						success: res => {
							if (res.code === 0) {
								this.contractData = res.data;
								resolve(res.data);
							} else {
								uni.showToast({
									title: res.msg || '获取合同信息失败',
									icon: 'error'
								});
								reject(res.msg);
							}
						},
						fail: error => {
							uni.showToast({
								title: '网络请求失败',
								icon: 'error'
							});
							reject(error);
						}
					});
				});
			},
			
			// 获取已上传的纸质合同照片
			getUploadedImages() {
				if (!this.contractData || !this.contractData.id) {
					return Promise.resolve();
				}
				
				return new Promise((resolve, reject) => {
					this.http({
						url: 'getContractEnclosureById',
						method: 'GET',
						data: {
							id: this.contractData.id
						},
						success: res => {
							if (res.code === 0 && res.data) {
								this.uploadedImages = res.data;
							}
							resolve(res.data || []);
						},
						fail: error => {
							console.error('获取上传图片失败:', error);
							resolve([]);
						}
					});
				});
			},
			
			// 下载纸质合同
			downloadContract() {
				if (!this.contractData) {
					uni.showToast({
						title: '合同信息未加载',
						icon: 'error'
					});
					return;
				}
				
				let downloadUrl = '';
				const contractId = this.contractData.id;
				
				// 根据合同类型判断下载地址
				if (this.contractData.contractType === 1) {
					downloadUrl = `https://biapi.xiaoyujia.com/contract/downloadByUrl/yuesao/${contractId}`;
				} else {
					downloadUrl = `https://biapi.xiaoyujia.com/contract/downloadByUrl/${contractId}`;
				}
				
				// 使用外部URL请求
				this.http({
					outsideUrl: downloadUrl,
					method: 'GET',
					success: res => {
						if (res.code === 0) {
							this.copy(res.data);
						} else {
							uni.showToast({
								title: res.msg || '下载失败',
								icon: 'error'
							});
						}
					},
					fail: error => {
						uni.showToast({
							title: '下载请求失败',
							icon: 'error'
						});
					}
				});
			},
			
			// 上传图片
			uploadImage() {
				if (!this.contractData || !this.contractData.id) {
					uni.showToast({
						title: '合同信息未加载，无法上传',
						icon: 'error'
					});
					return;
				}
				
				if (!uni.getStorageSync('employeeId')) {
					uni.showToast({
						title: '员工信息未找到，无法上传',
						icon: 'error'
					});
					return;
				}
				
				uni.chooseImage({
					count: 9, // 最多选择9张图片
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						const tempFilePaths = res.tempFilePaths;
						this.uploadFiles(tempFilePaths);
					},
					fail: (error) => {
						console.error('选择图片失败:', error);
						uni.showToast({
							title: '选择图片失败',
							icon: 'error'
						});
					}
				});
			},
			
			// 批量上传文件
			async uploadFiles(filePaths) {
				uni.showLoading({
					title: '上传中...'
				});
				
				try {
					const uploadedUrls = [];
					
					// 先上传所有图片获取URL
					for (let i = 0; i < filePaths.length; i++) {
						const imageUrl = await this.uploadSingleFile(filePaths[i]);
						uploadedUrls.push(imageUrl);
					}
					
					// 组装合同附件数据
					const employeeId = uni.getStorageSync('employeeId');
					const contractId = this.contractData.id;
					
					console.log('合同ID:', contractId);
					console.log('员工ID:', employeeId);
					console.log('上传的图片URLs:', uploadedUrls);
					
					const contractEnclosures = uploadedUrls.map(url => ({
						contractId: parseInt(contractId),
						creatId: parseInt(employeeId),
						state: 1,
						enclosureUrl: url
					}));
					
					console.log('组装的合同附件数据:', contractEnclosures);
					
					// 保存合同附件信息
					console.log('准备保存合同附件，数据:', JSON.stringify(contractEnclosures));
					await this.saveContractEnclosures(contractEnclosures);
					
					// 重新获取上传的图片列表
					await this.getUploadedImages();
					
					uni.showToast({
						title: '上传成功',
						icon: 'success'
					});
				} catch (error) {
					console.error('上传失败:', error);
					uni.showToast({
						title: typeof error === 'string' ? error : '上传失败',
						icon: 'error'
					});
				} finally {
					uni.hideLoading();
				}
			},
			
			// 上传单个文件
			uploadSingleFile(filePath) {
				return new Promise((resolve, reject) => {
					uni.uploadFile({
						url: 'https://api.xiaoyujia.com/system/imageUpload',
						filePath: filePath,
						name: 'file',
						success: (uploadRes) => {
							try {
								const result = JSON.parse(uploadRes.data);
								if (result.code === 0) {
									resolve(result.data);
								} else {
									reject(result.msg || '图片上传失败');
								}
							} catch (error) {
								reject('上传响应解析失败');
							}
						},
						fail: (error) => {
							reject('图片上传请求失败');
						}
					});
				});
			},
			
			// 保存合同附件信息
			saveContractEnclosures(enclosures) {
				return new Promise((resolve, reject) => {
					// 确保数据格式正确
					console.log('保存合同附件数据:', JSON.stringify(enclosures));
					
					this.http({
						url: 'updateContractEnclosure',
						method: 'POST',
						header: {
							"content-type": "application/json;charset=UTF-8"
						},
						data: enclosures,
						success: res => {
							console.log('保存合同附件响应:', res);
							if (res.code === 0) {
								resolve(res.data);
							} else {
								reject(res.msg || '保存合同附件失败');
							}
						},
						fail: error => {
							console.error('保存合同附件请求失败:', error);
							reject('保存合同附件请求失败');
						}
					});
				});
			},
			
			// 预览图片
			previewImage(url) {
				const urls = this.uploadedImages.map(img => img.enclosureUrl);
				uni.previewImage({
					current: url,
					urls: urls
				});
			},
			copy(info) {
				// #ifndef H5
				uni.setClipboardData({
					data: info, //要被复制的内容
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
							title: `复制成功`,
							icon: 'success'
						})
					}
				}, true);
				// #endif
			
				// #ifdef H5
				let textarea = document.createElement("textarea")
				textarea.value = info
				textarea.readOnly = "readOnly"
				document.body.appendChild(textarea)
				textarea.select() // 选中文本内容
				textarea.setSelectionRange(0, info.length)
				uni.showToast({ //提示
					title: '复制成功'
				})
				result = document.execCommand("copy")
				textarea.remove()
				// #endif
			},
			successUpdate() {
				uni.showModal({
					title: '提示',
					content: '是否变更合同信息',
					success: res => {
						if (res.confirm) {
							this.contractData.status = 2;
							this.contractData.operator = uni.getStorageSync('employeeId');
							this.contractData.memberSignDate = new Date();
							this.contractData.employeeSignDate = new Date();
							this.http({
								url: 'updateContractById',
								method: 'POST',
								data: this.contractData,
								header: {
									"content-type": "application/json;charset=UTF-8"
								},
								success: res => {
									if (res.data == true) {
										uni.showToast({
											title:'变更成功'
										})
										setTimeout(()=>{
											this.initData()
										},1000)
									} else {
										uni.showToast({
											title: '更新失败',
											icon: 'none'
										})
									}
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			}
		}
	}
</script>

<style scoped>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.header {
		text-align: center;
		padding: 30rpx 0;
		background-color: #fff;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
	}
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #1e1848;
	}
	
	.contract-info {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
	}
	
	.info-item {
		display: flex;
		margin-bottom: 20rpx;
	}
	
	.label {
		font-weight: bold;
		color: #333;
		width: 200rpx;
	}
	
	.value {
		color: #666;
		flex: 1;
	}
	
	.uploaded-images {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #1e1848;
		margin-bottom: 20rpx;
		display: block;
	}
	
	.image-list {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}
	
	.image-item {
		width: 200rpx;
		height: 200rpx;
		border-radius: 10rpx;
		overflow: hidden;
		border: 2rpx solid #eee;
	}
	
	.image-item image {
		width: 100%;
		height: 100%;
	}
	
	.steps-guide {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
	}
	
	.guide-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #1e1848;
		margin-bottom: 30rpx;
		display: block;
	}
	
	.step-item {
		display: flex;
		align-items: center;
		margin-bottom: 25rpx;
		padding: 20rpx;
		background-color: #f8f9fa;
		border-radius: 8rpx;
		border-left: 6rpx solid #1e1848;
	}
	
	.step-item:last-child {
		margin-bottom: 0;
	}
	
	.step-number {
		width: 60rpx;
		height: 60rpx;
		background-color: #1e1848;
		color: #fff;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: bold;
		font-size: 28rpx;
		margin-right: 20rpx;
		flex-shrink: 0;
	}
	
	.step-text {
		color: #333;
		font-size: 28rpx;
		line-height: 1.5;
		flex: 1;
	}
	
	.button-group {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 30rpx;
	}
</style>