export default {
	// 通用接口
	checkFindWorkPopup: "acn/checkFindWorkPopup",

	// 会员相关接口
	getMemberInvitation: "member/getMemberInvitation",
	addMemberInvitation: "member/addMemberInvitation",
	saveMemberAddr: "member/saveMemberAddr",
	updateMemberById: "acn/updateMemberById",

	selectMemberAddress: "member/selectMemberAddress",
	selectMemberAddressAndInit: "member/selectMemberAddressAndInit",


	// 会员活动相关
	exchangeActivityCount: "member/exchangeActivityCount",
	shareActivity: "member/shareActivity",
	selectMemberById: "member/selectMemberById",


	// 员工相关接口
	getEmployeeById: "employee/getEmployeeById",
	getEmployeeDtoById: "acn/getEmployeeDtoById",
	getEmployeeByPhone: "employee/getEmployeeByPhone",
	getEmployeeByMemberId: "acn/getEmployeeByMemberId",
	getEmployeeInfoById: "employee/getEmployeeInfoById",
	getBaomuCollectByEmployeeId: "acn/getBaomuCollectByEmployeeId",
	checkEmployeeByIdCard: "acn/checkEmployeeByIdCard",
	updatePriorIntroducer: "acn/updatePriorIntroducer",
	portraitUnqualified: "acn/portraitUnqualified",
	getIntroducerPhone: "acn/getIntroducerPhone",
	checkEmployeePower: "acn/checkEmployeePower",
	checkAuthPower: "acn/checkAuthPower",
	checkHRBPPower: "acn/checkHRBPPower",
	checkAuthIdPower: "acn/checkAuthIdPower",

	updateEmployee: "employee/updateEmployee",
	updateEmployeeInfo: "employee/updateEmployeeInfo",
	getStoreManager: "acn/getStoreManager",
	getStoreBusinessAnalysis: "acn/getStoreBusinessAnalysis",
	listStoreStaffData: "acn/listStoreStaffData",

	newEmployeeNotice: "acn/newEmployeeNotice",
	remindIntroduce: "acn/remindIntroduce",
	sendSubscribeMsg: "openapi/sendSubscribeMsg",
	sendTemplateMsg: "openapi/sendTemplateMsg",

	// 简历相关（acn家姐联盟）
	getDictionaryByText: "system/getDictionaryByText",
	getBaomuDictionaryById: "acn/getBaomuDictionaryById",
	getAllBaomuAbilityById: "acn/getAllBaomuAbilityById",
	getBaomuDetail: "acn/getBaomuDetail",
	getResumeScore: "acn/getResumeScore",
	getBaomuPage: "acn/getBaomuPage",
	getBaomuById: "acn/getBaomuById",
	listEmployeeContract: "acn/listEmployeeContract",
	// 人才招募
	pageRecruit: "acn/pageRecruit",
	initRecruit: "acn/initRecruit",
	insertRecruit: "acn/insertRecruit",
	updateRecruit: "acn/updateRecruit",
	checkRecruit: "acn/checkRecruit",

	addBaomuAbility: "acn/addBaomuAbility",
	addAbilityByDictionary: "acn/addAbilityByDictionary",
	addBaomuWorkExperience: "acn/addBaomuWorkExperience",
	imgsToIdInfo: "acn/imgsToIdInfo",
	getMemberByPhone: "member/getMemberByPhone",
	getMemberById: "work/getMemberById",

	checkBaomuCollectByMemberId: "acn/checkBaomuCollectByMemberId",
	getBaomuCollectByMemberId: "acn/getBaomuCollectByMemberId",

	getBaomuInfoById: "acn/getBaomuInfoById",
	getBaomuExpectedWork: "acn/getBaomuExpectedWork",
	getBaomuWorkExperience: "acn/getBaomuWorkExperience",
	openMemberIsAuth: "work/openMemberIsAuth",

	updateBaomu: "acn/updateBaomu",
	updateBaomuState: "acn/updateBaomuState",
	updateBaomuInfo: "acn/updateBaomuInfo",
	updateBaomuExpectedWork: "acn/updateBaomuExpectedWork",
	updateBaomuWorkExperience: "acn/updateBaomuWorkExperience",
	deleteBaomuWorkExperience: "acn/deleteBaomuWorkExperience",
	workReward: "acn/workReward",
	bindReward: "acn/bindReward",

	//认证-鉴定相关（acn家姐联盟）
	employeeSecurityAuth: "acn/employeeSecurityAuth",
	getBaomuExamByBaomuId: "acn/getBaomuExamByBaomuId",
	getBaomuExamById: "acn/getBaomuExamById",
	addBaomuExam: "acn/addBaomuExam",
	updateBaomuExam: "acn/updateBaomuExam",

	getAllBaomuAuth: "acn/getAllBaomuAuth",
	getBaomuAuthRank: "acn/getBaomuAuthRank",
	getWorkSkillAuthRank: "acn/getWorkSkillAuthRank",
	getAllWorkSkill: "acn/getAllWorkSkill",
	getBaomuAuthByBaomuId: "acn/getBaomuAuthByBaomuId",
	getBaomuAuthById: "acn/getBaomuAuthById",
	insertBaomuAuthReject: "acn/insertBaomuAuthReject",
	listBaomuAuthReject: "acn/listBaomuAuthReject",
	checkAuthQualification: "acn/checkAuthQualification",
	getAuthReport: "acn/getAuthReport",
	listUnionLevelEquity: "acn/listUnionLevelEquity",
	getWorkSkillSalary: "acn/getWorkSkillSalary",
	getEmployeeSkill: "acn/getEmployeeSkill",
	getAuthWorkType: "acn/getAuthWorkType",
	checkIsAccordAuth: "acn/checkIsAccordAuth",
	getBaomuWorkLog: "acn/getBaomuWorkLog",
	addBaomuWorkLog: "acn/addBaomuWorkLog",

	// 鉴定相关
	listAuthWorkType: "acn/listAuthWorkType",
	listAuthSkillGroup: "acn/listAuthSkillGroup",
	getAuthSkillSalary: "acn/getAuthSkillSalary",
	startAuthSkillEvaluate: "acn/startAuthSkillEvaluate",
	getAuthReportDetail: "acn/getAuthReportDetail",
	getSuggestSkill: "acn/getSuggestSkill",
	listBaomuAuth: "acn/listBaomuAuth",

	// 等级任务
	getAllTask: "acn/getAllTask",
	getEmployeeTask: "acn/getEmployeeTask",
	getAllTaskRecord: "acn/getAllTaskRecord",
	getEmployeeTaskRecord: "acn/getEmployeeTaskRecord",
	completeEmployeeTaskRecord: "acn/completeEmployeeTaskRecord",

	startWorkSkillAuth: "acn/startWorkSkillAuth",
	confirmWorkSkillAuth: "acn/confirmWorkSkillAuth",
	getWorkSkillAuthSmsCode: "acn/getWorkSkillAuthSmsCode",

	addBaomuAuth: "acn/addBaomuAuth",
	updateBaomuAuth: "acn/updateBaomuAuth",
	getEncryptionCert: "acn/getEncryptionCert",
	getCertificateByEmployeeId: "acn/getCertificateByEmployeeId",
	getCertByEmployeeId: "acn/getCertByEmployeeId",
	addCertificate: "acn/addCertificate",
	addCertificateList: "acn/addCertificateList",
	updateCertificate: "acn/updateCertificate",
	getCertificateByCertType: "acn/getCertificateByCertType",
	deleteCertificate: "acn/deleteCertificate",
	createNewEmployee: "acn/createNewEmployee",
	pushEmployee: "acn/pushEmployee",

	// 地址相关
	getAllSite: "acn/getAllSite",
	getIntentionSite: "acn/getIntentionSite",
	getAllStoreList: "acn/getAllStoreList",
	getAllDictLocation: "acn/getAllDictLocation",
	getDictLocationByDepth: "acn/getDictLocationByDepth",
	getJoinStoreList: "system/getJoinStoreList",

	// 课程相关接口
	getCourseAdded: "course/getCourseAdded",
	getCourseNotAdded: "course/getCourseNotAdded",
	getCourseAddedDetail: "course/getCourseAddedDetail",
	getCourseNotAddedDetail: "course/getCourseNotAddedDetail",
	getAllCourseGroup: "course/getAllCourseGroup",
	getCourseAddedInfo: "course/getCourseAddedInfo",
	getCourseById: "course/getCourseById",
	getCourseContentPage: "course/getCourseContentPage",
	getCourseContentMenu: "course/getCourseContentMenu",
	updateCourseStudy: "course/updateCourseStudy",
	addCourseStudy: "course/addCourseStudy",
	getCourseGroup: "course/getCourseGroup",
	getCoursePage: "course/getCoursePage",
	listCourse: "course/listCourse",
	listCompulsoryCourse: "course/listCompulsoryCourse",
	checkCourseExamPower: "course/checkCourseExamPower",
	getCourseDetail: "course/getCourseDetail",
	getCourseStudyDetail: "course/getCourseStudyDetail",
	pageCourseComment: "course/pageCourseComment",
	listCourseComment: "course/listCourseComment",
	getCourseComment: "course/getCourseComment",
	updateCourseComment: "course/updateCourseComment",
	pageCourseLog: "course/pageCourseLog",
	insertCourseLog: "course/insertCourseLog",
	getStoreMonthStudyDuration: "course/getStoreMonthStudyDuration",
	getCourseTeacherById: "course/getCourseTeacherById",

	// 问卷调查
	checkSurvey: 'course/checkSurvey',
	checkContractEmployee: 'course/checkContractEmployee',
	insertSurveyRecord: 'course/insertSurveyRecord',
	getSurveyById: 'course/getSurveyById',
	checkStoreSurveyRecordAuth: 'course/checkStoreSurveyRecordAuth',

	// 证书相关接口
	getAllUnionCertificates: "acn/getAllUnionCertificates",
	getUnionCertificates: "acn/getUnionCertificates",
	addUnionCertificates: "acn/addUnionCertificates",
	updateUnionCertificates: "acn/updateUnionCertificates",
	deleteUnionCertificates: "acn/deleteUnionCertificates",

	getAllUnionCertRecord: "acn/getAllUnionCertRecord",
	getUnionCertRecord: "acn/getUnionCertRecord",
	addUnionCertRecord: "acn/addUnionCertRecord",
	updateUnionCertRecord: "acn/updateUnionCertRecord",
	deleteUnionCertRecord: "acn/deleteUnionCertRecord",
	getUnionCertRecordById: "acn/getUnionCertRecordById",
	getTrainList: "acn/getTrainList",

	// 消息通知相关
	getUnionInformPage: "acn/getUnionInformPage",
	getToDoListMenu: "acn/getToDoListMenu",
	sendUnionInform: "acn/sendUnionInform",
	getUnionInformMenu: "acn/getUnionInformMenu",
	getUnionInform: "acn/getUnionInform",
	updateUnionInform: "acn/updateUnionInform",
	collectUnionInform: "acn/collectUnionInform",
	checkUnionInformPopup: "acn/checkUnionInformPopup",
	deleteUnionInform: "acn/deleteUnionInform",
	checkEmployeeUnread: "acn/checkEmployeeUnread",
	getUnionInformStatData: "acn/getUnionInformStatData",

	// 积分与签到相关接口
	getJiaBi: "jiabi/getJiaBi",
	getJiaBiDetail: "jiabi/getJiaBiDetail",
	getJiaBiDetailPage: "jiabi/getJiaBiDetailPage",
	updateJiaBi: "jiabi/updateJiaBi",
	exchangeCredit: "jiabi/exchangeCredit",

	getSignInDays: "jiabi/getSignInDays",
	getNearStore: "jiabi/getNearStore",

	isSignInToday: "jiabi/isSignInToday",
	startSignIn: "jiabi/startSignIn",
	checkStoreSignIn: "jiabi/checkStoreSignIn",
	checkArriveSignIn: "jiabi/checkArriveSignIn",
	startStoreSignIn: "jiabi/startStoreSignIn",
	rewardJiaBi: 'jiabi/rewardJiaBi',
	checkRewardAuth: 'jiabi/checkRewardAuth',
	getRewardData: 'jiabi/getRewardData',
	listJiaBiReward: 'jiabi/listJiaBiReward',
	rewardTeacher: 'course/rewardTeacher',

	// 订单相关接口
	addOrder: "order/addOrder",
	getContractByPeopleId: "order/getContractByPeopleId",
	getOrderNeedsPage: "order/getOrderNeedsPage",
	getOrderNeedsDetail: "order/getOrderNeedsDetail",
	getOrderNeedsVoice: "order/getOrderNeedsVoice",
	updateOrderNeedsVoice: "order/updateOrderNeedsVoice",
	memberRefund: "order/memberRefund",
	getCancelReason: "order/getCancelReason",
	updateOrderNeeds: "order/updateOrderNeeds",
	addOrderNeedLog: "order/addOrderNeedsLog",
	updateOrderNeedsLog: "order/updateOrderNeedsLog",
	addOrderNeedsShareLog: "order/addOrderNeedsShareLog",
	getOneBillData: "order/getOneBillData",
	pageOrderNeedsLog: "order/pageOrderNeedsLog",
	addInterviewRecommend: "work/addInterviewRecommend",
	pageRecommendOrderNeeds: "work/pageRecommendOrderNeeds",

	pageServiceAcceptanceForm: "order/pageServiceAcceptanceForm",
	getServiceAcceptanceForm: "order/getServiceAcceptanceForm",
	getServiceAcceptanceFormMenu: "order/getServiceAcceptanceFormMenu",
	getServiceAcceptanceScore: "order/getServiceAcceptanceScore",
	insertServiceAcceptanceForm: "order/insertServiceAcceptanceForm",
	updateServiceAcceptanceForm: "order/updateServiceAcceptanceForm",
	insertServiceAcceptanceFormImgList: "order/insertServiceAcceptanceFormImgList",
	listServiceAcceptanceFormReply: "order/listServiceAcceptanceFormReply",
	insertServiceAcceptanceFormReply: "order/insertServiceAcceptanceFormReply",
	getOrderTsLog: "order/getOrderTsLog",
	pageOrderTs: "order/pageOrderTs",
	getAppealTimeLimit: "order/getAppealTimeLimit",
	appealOrderTs: "order/appealOrderTs",
	listOrderTsAppeal: "order/listOrderTsAppeal",
	listOrderTsReply: "order/listOrderTsReply",
	insertOrderTsReply: "order/insertOrderTsReply",

	// 支付相关接口
	pagPay: "pays/pagPay",
	getWxH5PayInfo: "pays/getWxH5PayInfo",
	sendPagPaySmsCode: "pays/sendPagPaySmsCode",
	getWxMiniProgramPayInfo: "pays/getWxMiniProgramPayInfo",

	//合伙人入驻注册
	partnerEnter: "franchise/partnerEnter",
	//腾讯云营业执照识别
	OcrBusinessLicense: "franchise/OcrBusinessLicense",
	//根据合伙人ID获取合伙人入驻所有信息
	getFranchiseMsgById: "franchise/getFranchiseMsgById",
	//查询合伙人入驻未取消的订单
	getOrderByMemberId: "franchise/getOrderByMemberId",
	//根据memberId判断该会员是否为员工并插入商户信息
	getIfEmployeeByMemberId: "franchise/getIfEmployeeByMemberId",


	//合伙人工作台{
	//查询奖项统计数据
	getFranchiseWorkData: "work/getFranchiseWorkData",
	//获取奖项详情信息
	getAwardsList: "work/getAwardsList",
	//根据会员id获取商户信息
	getMerchantByMemberId: "unionMerchant/getMerchantByMemberId",
	//提现认证
	withdrawAttestation: "work/withdrawAttestation",
	//提现申请
	withdrawApply: "unionMerchant/withdrawApply",
	//提现列表
	withdrawList: "unionMerchant/withdrawList",
	//线索管理{
	//获取任务管理列表
	taskPage: "task/taskPage",
	//线索管理根据店铺id获取员工
	getEmpByStoreId: "employee/getEmpByStoreId",
	//线索管理获取线索列表
	getOrderNeedsList: "work/getOrderNeedsList",
	//线索管理获取保姆信息
	getRecommendNanny: "work/getRecommendNanny",
	// 获取合单线索面试推荐选项
	getInterviewRecommendOption: "work/getInterviewRecommendOption",
	//线索管理保存保姆面试信息
	getOrderDeliveryList: "order/getOrderDeliveryList",
	getOrderDeliveryPage: "order/getOrderDeliveryPage",
	addOrderDelivery: "work/addOrderDelivery",
	addOrderDelivery1: "order/addOrderDelivery",
	addInterviewRecommendOption: "work/addInterviewRecommendOption",
	delDelivery: "work/delDelivery",
	getInterviewList: "work/getInterviewList",
	getNeedList: "work/getNeedList",
	getDyId: "work/getDyId",
	saveNeedList: "work/saveNeedList",
	getNeedListByClueId: "work/getNeedListByClueId",
	getImputedPrice: "work/getImputedPrice",
	sendSalaryMsg: "work/sendSalaryMsg",
	salaryAffirm: "work/salaryAffirm",
	getOrderNeedsById: "work/getOrderNeedsById",
	sendInterviewMsg: "work/sendInterviewMsg",
	pushInterview: "work/pushInterview",
	updateOrderNeedState: "work/updateOrderNeedState",
	orderNeedsChangeOrder: "order/orderNeedsChangeOrder",
	orderDetails: "order/orderDetails",
	orderPayCallBack: "order/orderPayCallBack",
	getOrderPayCode: "order/getOrderPayCode",
	getBaoMuInfoData: "work/getBaoMuInfoData",
	getSubSheet: "work/getSubSheet",
	updateOrderNeedAddress: "work/updateOrderNeedAddress",
	nextSalaryAffirm: "work/nextSalaryAffirm",
	updateInterviewState: "work/updateInterviewState",
	getWorkNameByCode: "work/getWorkNameByCode",
	getStoreOrderCharts: "work/getStoreOrderCharts",
	getEmployeeRatingCharts: "work/getEmployeeRatingCharts",
	getEmployeeRepurchaseCharts: "work/getEmployeeRepurchaseCharts",
	cancelNeeds: "work/cancelNeeds",
	delSound: "work/delSound",
	uploadSound: "work/uploadSound",
	uploadEndSound: "work/uploadEndSound",
	updateBaoMuWages: "work/updateBaoMuWages",
	getGrowthPlan: "work/getGrowthPlan",
	getFlowTask: "work/getFlowTask",
	getTaskForm: "work/getTaskForm",
	uploadWeChatCodeImgUrl: "work/uploadWeChatCodeImgUrl",
	contractStop: "work/contractStop",
	getCanWithdrawalRecruitReturn: "work/getCanWithdrawalRecruitReturn",
	withdrawalRecruitApplyOf: "work/withdrawalRecruitApplyOf",
	uploadServiceToolsUrl: "work/uploadServiceToolsUrl",
	partTimeJobWithdrawal: "work/partTimeJobWithdrawal",
	saveEmployeePayDeclare: "work/saveEmployeePayDeclare",
	getEmployeePayDeclareLog: "work/getEmployeePayDeclareLog",
	getEmployeeBankCard: "work/getEmployeeBankCard",
	getCanWithdrawalDeclare: "work/getCanWithdrawalDeclare",
	declareWithdrawal: "work/declareWithdrawal",
	getDeclareWithdrawalLog: "work/getDeclareWithdrawalLog",
	transmitWorkOrder: "work/transmitWorkOrder",
	getDeclareProportion: "work/getDeclareProportion",
	getNewWorkMenu: "work/getNewWorkMenu",
	getNewWorkData: "work/getNewWorkData",
	getMemberByPhoneTwo: "work/getMemberByPhone",
	bindingMember: "work/bindingMember",
	getOrderArtificialMoney: "work/getOrderArtificialMoney",
	getWorkMenu: "work/getWorkMenu",
	updateMaintenance: "work/updateMaintenance",
	getPotentialMember: "work/getPotentialMember",
	getMemberCommunicate: "work/getMemberCommunicate",
	addQzMemberCommunicate: "work/addQzMemberCommunicate",
	getUpdateTimeNumber: "work/getUpdateTimeNumber",
	getOrderNeedsByPhone: "work/getOrderNeedsByPhone",
	getInterviewRecommend: "work/getInterviewRecommend",
	getDeliverContract: "work/getDeliverContract",
	submitContractLog: "work/submitContractLog",
	getWithdrawalDetails: "work/getWithdrawalDetails",
	getHistoryLog: "work/getHistoryLog",
	getAbcServiceCompensation: "work/getAbcServiceCompensation",
	resetPay: "work/resetPay",
	getStoreReward: "work/getStoreReward",
	withdrawalReward: "work/withdrawalReward",
	rmbToggleCase: "work/rmbToggleCase",
	updateOrderNeedsTag: "work/updateOrderNeedsTag",
	getStoreIssueInvoice: "work/getStoreIssueInvoice",
	applyForInvoicing: "work/applyForInvoicing",
	getWithdrawalInvoice: "work/getWithdrawalInvoice",
	getRecruitmentMoneyData: "work/getRecruitmentMoneyData",
	urgeInvoicing: "work/urgeInvoicing",
	urgeApprove: "work/urgeApprove",
	getRemainingDay: "work/getRemainingDay",
	getAllOpenStore: "work/getAllOpenStore",
	getHeatMapNeedsList: "work/getHeatMapNeedsList",
	//}
	//}

	getAllProductRetail: "product/getAllProductRetail",
	getEmployeePoster: "product/getEmployeePoster",

	//工作台分销接口
	getPromotionInfoList: "work/getPromotionInfoList",
	getPromotionProductList: "work/getPromotionProductList",
	extensionList: "work/extensionList",
	extensionDetail: "work/extensionDetail",

	//业绩列表
	settlementPage: "work/settlementPage",
	settlementData: "work/settlementData",

	//售后列表
	getStoreTsOrder: "work/getStoreTsOrder",
	getDealTsLog: "work/getDealTsLog",

	//合同模块
	contractPage: "order/contractPage",
	selectMemberList: "member/selectMemberList",
	listContractHoliday: "order/listContractHoliday",
	addContract: "order/addContract",
	getContractById: "order/getContractById",
	registeredAndCertification: "order/registeredAndCertification",
	viewContractPdfUrl: "openapi/viewPdfUrl",
	updateContractById: "order/updateContractById",

	//客户管理
	getClientList: "work/getClientList",
	getByMemberId: "work/getByMemberId",
	updateMember: "work/updateMember",
	selectOrderByMemberId: "work/selectOrderByMemberId",
	getMemberCommunicateByMemberId: "work/getMemberCommunicateByMemberId",
	getOrderTsByMemberId: "work/getOrderTsByMemberId",
	selectByOrderNeedsMemberId: "work/selectByOrderNeedsMemberId",
	contractByMemberId: "work/contractByMemberId",
	addCommunicate: "work/addCommunicate",
	saveQuestionnaireResult: "work/saveQuestionnaireResult",
	getStoreSeedData: "work/getStoreSeedData",
	saveSmartPartnerChatLog: "work/saveSmartPartnerChatLog",
	getSmartPartnerChatLog: "work/getSmartPartnerChatLog",
	getChatWorkOrder: "work/getChatWorkOrder",
	getChatDetails: "work/getChatDetails",
	getHistoryChatLog: "work/getHistoryChatLog",
	uploadServiceEndUrl: "work/uploadServiceEndUrl",
	getEmployeeInfoData: "work/getEmployeeInfoData",

	//业务管理
	getSalaryData: "work/getSalaryData",
	getEvaluateData: "work/getEvaluateData",
	getApprenticeData: "work/getApprenticeData",
	getTimeData: "work/getTimeData",
	getDayTimeData: "work/getDayTimeData",
	updateTimeData: "work/updateTimeData",
	getStudyObligation: "work/getStudyObligation",
	getViolationData: "work/getViolationData",
	getMeetOrders: "work/getMeetOrders",
	updateOrderState: "work/updateOrderState",
	saveUpdateOrderServiceTimeLog: "work/saveUpdateOrderServiceTimeLog",
	getOrderData: "work/getOrderData",
	updateOrderLabel: "work/updateOrderLabel",
	updateOrderImg: "work/updateOrderImg",
	updateMoveUrl: "work/updateMoveUrl",
	sendMsgToCustomer: "work/sendMsgToCustomer",
	delRepairOrder: "work/delRepairOrder",
	repairOrder: "work/repairOrder",
	uploadSingUrl: "work/uploadSingUrl",
	updateEndTime: "work/updateEndTime",
	addMemberFamilyInfo: "work/addMemberFamilyInfo",
	getAreaData: "work/getAreaData",
	uploadHeadImg: "work/uploadHeadImg",
	addOrderNeedsLog: "work/addOrderNeedsLog",
	getSubmitPupilData: "work/getSubmitPupilData",
	submitPupilData: "work/submitPupilData",
	updateSecurityAuth: "work/updateSecurityAuth",
	generateAddCarToken: "work/generateAddCarToken",
	updateOrderPushState: "work/updateOrderPushState",
	getIncomeData: "work/getIncomeData",
	updateAgentRemark: "work/updateAgentRemark",
	allianceRenew: "work/allianceRenew",
	getIncomeDetailed: "work/getIncomeDetailed",
	getFundData: "work/getFundData",
	getStoreData: "work/getStoreData",
	updateStoreData: "work/updateStoreData",
	getStorePayData: "work/getStorePayData",
	getStoreDetailed: "work/getStoreDetailed",
	getStoreManageData: "work/getStoreManageData",
	getServerDetailed: "work/getServerDetailed",
	getStoreListData: "work/getStoreListData",
	getStoreServerData: "work/getStoreServerData",
	getStoreOrderData: "work/getStoreOrderData",
	getWeeklyData: "work/getWeeklyData",
	getBusinessTrendData: "work/getBusinessTrendData",
	updateContractLog: "work/updateContractLog",
	getContractLogById: "work/getContractLogById",
	getContractLogGrade: "work/getContractLogGrade",
	gradeContract: "work/gradeContract",
	countStoreMoney: "work/countStoreMoney",
	withdrawalApplyOf: "work/withdrawalApplyOf",
	getAgentList: "work/getAgentList",
	distributionNeeds: "work/distributionNeeds",
	updateStorePayFlag: "work/updateStorePayFlag",
	getCanWithdrawalOrder: "work/getCanWithdrawalOrder",
	addStoreWorkLog: "work/addStoreWorkLog",
	getStoreWorkLog: "work/getStoreWorkLog",
	updateEmployeeToken: "work/updateEmployeeToken",
	getUnionMerchantData: "work/getUnionMerchantData",
	balanceWithdrawal: "work/balanceWithdrawal",
	getFranchiseIfAuth: "work/getFranchiseIfAuth",
	getWithdrawalLog: "work/getWithdrawalLog",
	ifEmployeeOrUnion: "work/ifEmployeeOrUnion",
	updateEmployeeInfoBank: "work/updateEmployeeInfoBank",
	getEmployeeIntegral: "work/getEmployeeIntegral",
	generateLink: "work/generateLink",
	updateNeedTemplate: "work/updateNeedTemplate",
	ifIsSplit: "work/ifIsSplit",
	getGroupOrderData: "work/getGroupOrderData",
	addNursePhoneLog: "work/addNursePhoneLog",
	getAllUnionType: "work/getAllUnionType",
	saveQaUseLog: "work/saveQaUseLog",
	getUseLogData: "work/getUseLogData",
	getUnionQaByType: "work/getUnionQaByType",
	getUnionQaById: "work/getUnionQaById",
	getQADataBySearch: "work/getQADataBySearch",
	getBillNoByMemberId: "work/getBillNoByMemberId",
	updateStoreDeposit: "work/updateStoreDeposit",
	getStoreDepositList: "work/getStoreDepositList",
	getStoreDepositReceipt: "work/getStoreDepositReceipt",
	getNannySelectLog: "work/getNannySelectLog",
	getImgFlowList: "work/getImgFlowList",
	checkJumpNextFlow: "work/checkJumpNextFlow",
	startTaskLog: "work/startTaskLog",
	getLoggedInByData: "work/getLoggedInByData",
	completeUnionTask: "work/completeUnionTask",
	getPromptData: "work/getPromptData",
	saveOrderQm: "work/saveOrderQm",
	addCustomerMsg: "work/addCustomerMsg",
	getMaterialById: "work/getMaterialById",
	getCertificateDataByStoreId: "work/getCertificateDataByStoreId",
	getJzyAuthenticationData: "work/getJzyAuthenticationData",
	getDispatchOrderCount: "work/getDispatchOrderCount",
	getRlwLogData: "work/getRlwLogData",
	getDispatchAllOrder: "work/getDispatchAllOrder",
	getEmployeeStrByFilter: "work/getEmployeeStrByFilter",
	dispatchOrders: "work/dispatchOrders",
	getAllIntegralProduct: "work/getAllIntegralProduct",
	exchangeProduct: "work/exchangeProduct",
	getMemberExchangeLog: "work/getMemberExchangeLog",
	getMemberAmount: "work/getMemberAmount",
	getCanWithdrawalAbnormalOrder: "work/getCanWithdrawalAbnormalOrder",
	updateStoreAccountAuditing: "work/updateStoreAccountAuditing",
	saveStoreAccountAuditing: "work/saveStoreAccountAuditing",
	getStoreAccountAuditing: "work/getStoreAccountAuditing",
	withdrawalDeposit: "work/withdrawalDeposit",
	getStoreSmartPartnerData: "work/getStoreSmartPartnerData",
	getNewChatMsg: "work/getNewChatMsg",
	getQuestionnaireList: "work/getQuestionnaireList",
	getNewWorkBonus: "work/getNewWorkBonus",
	getNewWorkTabulation: "work/getNewWorkTabulation",
	getDeviceList: "work/getDeviceList",
	getCustomerList: "work/getCustomerList",
	deviceAllocation: "work/deviceAllocation",
	getDeviceCustomerList: "work/getDeviceCustomerList",
	synchronousDevice: "work/synchronousDevice",
	getCityNameByLatLng: "work/getCityNameByLatLng",
	getPhysicalOrder: "work/getPhysicalOrder",
	getYjCityList: "work/getYjCityList",
	getYjHospList: "work/getYjHospList",
	getYjPackageList: "work/getYjPackageList",
	getHospitalPackageList: "work/getHospitalPackageList",
	getHospitalScheduling: "work/getHospitalScheduling",
	getPhysicalPatient: "work/getPhysicalPatient",
	addPhysicalPatient: "work/addPhysicalPatient",
	getSlideNotice: "work/getSlideNotice",
	generateHouseworkList: "work/generateHouseworkList",
	getHouseholdChecklist: "work/getHouseholdChecklist",
	getPromotionRewardData: "work/getPromotionRewardData",
	updateOrderAmount: "work/updateOrderAmount",
	checkStoreWithdrawalInvoice: "work/checkStoreWithdrawalInvoice",
	getWalletBalance: "work/getWalletBalance",
	getWalletRecords: "work/getWalletRecords",
	getCarListByName: "work/getCarListByName",
	confirmReportReason: "work/confirmReportReason",
	getStoreClueSetting: "work/getStoreClueSetting",
	getEmployeeTimeTemplate: "work/getEmployeeTimeTemplate",
	initializationEmployeeArea: "work/initializationEmployeeArea",
	changeStoreClueSetting: "work/changeStoreClueSetting",
	oneClickSettlement: "work/oneClickSettlement",
	getStoreWalletBillNo: "work/getStoreWalletBillNo",
	updateStoreWallet: "work/updateStoreWallet",
	corporateTransfer: "work/corporateTransfer",
	saveStoreWalletTopUpLog: "work/saveStoreWalletTopUpLog",
	gitPhysicalGivePay: "pays/gitPhysicalGivePay",
	evokeStoreWalletPay: "pays/evokeStoreWalletPay",



	//订单模块
	listOrderFind: "order/listOrderFind",
	selectOrderFindAllData: "order/selectOrderFindAllData",
	selectOrderFindData: "order/selectOrderFindData",
	updateAgentRemarkByBillNo: "order/updateAgentRemarkByBillNo",
	getContractByOrderId: "order/getContractByOrderId",
	agentOrderInfo: "order/agentOrderInfo",
	getOrderWagesByBillNo: "order/getOrderWagesByBillNo",
	getOrderOperationLogList: "order/getOrderOperationLogList",
	updateOrderRemark: "order/updateOrderRemark",
	updateServiceRemark: "order/updateServiceRemark",
	contractToOrder: "order/contractToOrder",
	changeRealTotalAmount: "order/changeRealTotalAmount",
	splitOrder: "order/splitOrder",
	genSubOrder: "order/genSubOrder",

	//活动模块
	ActivityUnionList: "unionMerchant/ActivityUnionList",
	ActivityUnionCollect: "unionMerchant/ActivityUnionCollect",
	ActivityUnionById: "unionMerchant/ActivityUnionById",
	submitActivityUnion: "unionMerchant/submitActivityUnion",
	ActivityUnionCollectList: "unionMerchant/ActivityUnionCollectList",
	ActivityUnionApplyList: "unionMerchant/ActivityUnionApplyList",
	taskDetailList: 'unionMerchant/taskDetailList',
	submitTaskDetail: 'unionMerchant/submitTaskDetail',

	// HRBP模块
	getTrialStaffPage: "staff/getTrialStaffPage",
	getTrialStaffByTrialId: "staff/getTrialStaffByTrialId",
	getTrialStaffByEmployeeId: "staff/getTrialStaffByEmployeeId",
	listTrialStaffLog: "staff/listTrialStaffLog",
	isValidCard: "staff/isValidCard",
	insertUpdateById: "staff/insertUpdateById",
	selectByProcessId: "staff/selectByProcessId",
	getByRoleList: "staff/getByRoleList",
	getByNo: "staff/getByNo",
	isNoExistence: "staff/isNoExistence",
	distinguishId: "staff/distinguishId",
	ocrBankCard: "staff/ocrBankCard",
	getToken: "staff/getToken",
	setToken: "staff/setToken",
	telSms: "staff/telSms",
	trainSms: "staff/trainSms",
	selectTrialStaffByCondition: "staff/selectTrialStaffByCondition",
	selectOfficialAccount: "staff/selectOfficialAccount",
	saveContractInformation: "staff/saveContractInformation",
	getTrialStaffPost: "staff/getTrialStaffPost",
	getTrialStaffProcess: "staff/getTrialStaffProcess",
	user: "staff/user",
	demo: "staff/demo",
	saveTrialStaffProcessRecords: "staff/saveTrialStaffProcessRecords",
	technologicalProcessRefuse: "staff/technologicalProcessRefuse",
	interviewAdopt: "staff/interviewAdopt",
	trainAdopt: "staff/trainAdopt",
	insuranceAdopt: "staff/insuranceAdopt",
	documentaryAdopt: "staff/documentaryAdopt",
	examinationAdopt: "staff/examinationAdopt",
	trainKeyAdopt: "staff/trainKeyAdopt",
	seeProcess: "staff/seeProcess",
	byStaffIdAddProcessRecord: "staff/byStaffIdAddProcessRecord",
	updateByTrialIdProcessId: "staff/updateByTrialIdProcessId",
	selectByTrialIdProcessId: "staff/selectByTrialIdProcessId",
	oneClickExport: "staff/oneClickExport",
	realNameAuthentication: "staff/realNameAuthentication",
	contractSigning: "staff/contractSigning",
	preJobExperience: "staff/preJobExperience",
	inductionAdministration: "staff/inductionAdministration",
	noticeSign: "staff/noticeSign",
	partTimeStaff: "staff/partTimeStaff",
	platform: "staff/platform",
	realNameCallback: "staff/realNameCallback",
	contractSignCallback: "staff/contractSignCallback",
	enterpriseRealName: "staff/enterpriseRealName",
	empowerState: "staff/empowerState",
	empower: "staff/empower",
	findCompanyCertInfo: "staff/findCompanyCertInfo",
	realNameCertificateApply: "staff/realNameCertificateApply",
	deleteTrialWorkExperience: "staff/deleteTrialWorkExperience",
	deleteTrialFamilyMember: "staff/deleteTrialFamilyMember",
	updateTrialStaff: "staff/updateTrialStaff",
	updateStaffState: "staff/updateStaffState",
	employeeToStaff: "acn/employeeToStaff",
	staffToEmployee: "acn/staffToEmployee",
	addTrainRecord: "staff/addTrainRecord",
	getWorkType: "staff/getWorkType",
	getWorkTypeByTrialId: "staff/getWorkTypeByTrialId",
	getWorkTypeAndProduct: "staff/getWorkTypeAndProduct",
	startWorkTypeAuth: "staff/startWorkTypeAuth",
	getProductByWorkTypeId: "staff/getProductByWorkTypeId",
	getEmployeeProduct: "staff/getEmployeeProduct",
	restartStaffExamRecord: "staff/restartStaffExamRecord",
	quickPutStaff: "staff/quickPutStaff",

	// 考试模块
	listUnionExam: "exam/listUnionExam",
	checkExamRecord: "exam/checkExamRecord",
	getUnionExamRank: "exam/getUnionExamRank",
	listUnionExamRecord: "exam/listUnionExamRecord",
	restartExamRecord: "exam/restartExamRecord",
	answerAllQuestion: "exam/answerAllQuestion",
	getExamQuestion: "exam/getExamQuestion",
	getExamQuestionAnalysis: "exam/getExamQuestionAnalysis",

	getPromotionProductById: "work/getPromotionProductById",
	cleanContract: "openapi/cleanContract",
	callPrivatePhone: "openapi/callPrivatePhone",
	searchBaomuList: "acn/searchBaomuList",
	contractSuppolyList: "order/getContractSupply",
	saveContractSupply: "order/saveContractSupply",
	getContractSigin: "order/getContractSigin",
	getContractByNo: 'order/getContractByNo',
	getContractEnclosureById: 'order/contractEnclosureById',
	updateContractEnclosure: 'order/updateContractEnclosure',
	uploadImage: 'system/imageUpload',
	getExcellentEmployeeList: "content/excellentEmployeeList",
	getAgentStoreInfo: "acn/getAgentStoreInfo",
	getAgentStoreInfoList: "acn/getAgentStoreInfoList",
	getStoreServiceScore: "task/getStoreServiceScore",
	listAllowApprovalRule: "task/listAllowApprovalRule",
	listServiceScoreRuleApproval: "task/listServiceScoreRuleApproval",
	insertServiceScoreRuleApproval: "task/insertServiceScoreRuleApproval",
	getServiceScoreRuleApprovalById: "task/getServiceScoreRuleApprovalById",
	listAgentStoreRank: "acn/listAgentStoreRank",
	listStoreLevelRank: "acn/listStoreLevelRank",
	updateServiceScore: "task/updateServiceScore",
	getStoreInfo: "acn/getStoreInfo",
	applyApprove: "acn/applyApprove",
	getContractByMenberPhone: "order/getContractByMemberPhone",
	getEmployeeByNo: "employee/getEmployeeByNo",
	getStoreById: "system/getStoreById",
	upgradeStore: "system/upgradeStore",
	getAreaByCityId: "system/getAreaByCityId",
	updateStoreIntroduce: "system/updateStoreIntroduce",
	getOpenProductSelectList: "product/getOpenProductSelectList",

	// 优秀员工
	getExcellentEmployee: "content/getExcellentEmployee",
	pageExcellentEmployee: "content/pageExcellentEmployee",
	excellentEmployeeList: "content/excellentEmployeeList",
	insertExcellentEmployee: "content/insertExcellentEmployee",
	updateExcellentEmployee: "content/updateExcellentEmployee",
	pageExcellentEmployeeComment: "content/pageExcellentEmployeeComment",
	listExcellentEmployeeComment: "content/listExcellentEmployeeComment",
	getExcellentEmployeeComment: "content/getExcellentEmployeeComment",
	updateExcellentEmployeeComment: "content/updateExcellentEmployeeComment",
	deleteExcellentEmployeeCommentById: "content/deleteExcellentEmployeeCommentById",
	rewardExcellentEmployee: "content/rewardExcellentEmployee",

	listExcellentEmployeeImgById: "content/listExcellentEmployeeImgById",
	updateExcellentEmployeeImgList: "content/updateExcellentEmployeeImgList",
	deleteExcellentEmployeeImg: "content/deleteExcellentEmployeeImg",

	listExcellentEmployeeLike: "content/listExcellentEmployeeLike",
	updateExcellentEmployeeLike: "content/updateExcellentEmployeeLike",

	listExcellentEmployeeVote: "content/listExcellentEmployeeVote",
	insertExcellentEmployeeVote: "content/insertExcellentEmployeeVote",
	listExcellentEmployeeRewardType: "content/listExcellentEmployeeRewardType",
	listExcitationEmployee: "content/listExcitationEmployee",
	getExcellentEmployeeVoteVenueById: "content/getExcellentEmployeeVoteVenueById",

	// 视频会议相关模块
	getMeetingRoomById: "acn/getMeetingRoomById",
	insertMeetingRoom: "acn/insertMeetingRoom",
	callPrivatePhoneByBillNo: "openapi/callPrivatePhoneByBillNo",
	//订单改时列表
	placeOrderTime: "order/placeOrderTime",
	getStoreNameList: "system/getStoreNameList",
	//合同下载
	downloadPdfUrl: "openapi/downloadPdfUrl",
	//生成海报
	createPoster: "system/imageQrSynthesis",

	// 成就相关
	listAchievement: 'acn/listAchievement',
	listAchievementGroupByLevel: 'acn/listAchievementGroupByLevel',
	getAchievementDetail: 'acn/getAchievementDetail',
	getAchievementTarget: 'acn/getAchievementTarget',
	listAchievementRecord: 'acn/listAchievementRecord',
	insertAchievementRecord: 'acn/insertAchievementRecord',
	getRecordOverview: 'acn/getRecordOverview',
	listAchievementType: 'acn/listAchievementType',
	orderBuRePayOtherPay: 'openapi/orderBuRePayOtherPay',
	productCheckList: 'product/productCheckList',

	//报险
	submitReportInsurance: 'order/submitReportInsurance',
	getReportInsurance: 'order/getReportInsurance',
	updateReportInsurance: 'order/updateReportInsurance',
	//上保
	doInsurance: 'openapi/doInsurance',
	insuranceByNo: 'openapi/insuranceByNo',
	//电子签生链接
	createSignByTencent: 'openapi/tencentSign/showSignUrl',
	//查看腾讯电子签合同
	showSignContractByTencent: 'openapi/tencentSign/viewSignPdf',
	//清除腾讯电子签合同
	cleanTencentSign: 'openapi/tencentSign/cleanSign',
	pageDebtOrder: 'task/pageDebtOrder',
	getEmployeeHandleSign: 'employee/getEmployeeHandleSign',
	signHandbook: 'openapi/tencentSign/signHandbook',
	viewSignPdfByFlowId: 'openapi/tencentSign/viewSignPdfByFlowId',
	checkInsurance: 'openapi/checkInsurance',
}