<!--
* @author: Jay
* @description: 小程序隐私协议 弹窗
* @createTime: 2023-08-31 10:07:54
 -->
<template>
	<view v-if="showPop">
		<view class="privacy-mask">
			<view class="privacy-wrap">
				<view class="privacy-title">
					用户隐私保护提示
				</view>
				<view class="privacy-desc">
					感谢您使用本小程序，在使用前您应当阅读井同意
					<text class="privacy-link" @tap="openPrivacyContract">《用户隐私保护指引》</text>，
					当点击同意并继续时，即表示您已理解并同意该条款内容，该条款将对您产生法律约束力；如您不同意，将无法继续使用小程序相关功能。
				</view>
				<view class="privacy-button-flex">
					<button class="privacy-button-btn bg-disagree" @tap="handleDisagree">
						不同意
					</button>
					<button id="agree-btn" class="privacy-button-btn bg-agree" open-type="agreePrivacyAuthorization"
						@agreeprivacyauthorization="handleAgree">
						同意并继续
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
		    showPop: {
		      type: Boolean,
		      default: false
		    }
		  },
		data() {
			return {
				privacyAuthorization: null,
				privacyResolves: new Set(),
				closeOtherPagePopUpHooks: new Set(),
			}
		},
		mounted() {
			this.init()
			this.curPageShow()
		},
		created() {
			//查询微信侧记录的用户是否有待同意的隐私政策信息
			let that = this;
			wx.getPrivacySetting({
				success(res) {
					console.log('隐私政策信息', res)
					if (res.needAuthorization) {
					// 	//打开弹窗
						that.popUp()
					}
				}
			});
		},
		methods: {
			// 监听何时需要提示用户阅读隐私政策
			init() {
				let that = this;
				if (wx.onNeedPrivacyAuthorization) {
					wx.onNeedPrivacyAuthorization((resolve) => {
						if (typeof that.privacyAuthorization === 'function') {
							that.privacyAuthorization(resolve)
						}
					})
				}
			},
			//初始化监听程序
			curPageShow() {
				this.privacyAuthorization = resolve => {
					this.privacyResolves.add(resolve)
					//打开弹窗
					this.popUp()
					// 额外逻辑：当前页面的隐私弹窗弹起的时候，关掉其他页面的隐私弹窗
					this.closeOtherPagePopUp(this.disPopUp)
				}
				this.closeOtherPagePopUpHooks.add(this.disPopUp)
			},
			// 额外逻辑：当前页面的隐私弹窗弹起的时候，关掉其他页面的隐私弹窗
			closeOtherPagePopUp(closePopUp) {
				this.closeOtherPagePopUpHooks.forEach(hook => {
					if (closePopUp !== hook) {
						hook()
					}
				})
			},
			//打开隐私协议
			openPrivacyContract() {
				wx.openPrivacyContract({
					success(res) {
						console.log('打开隐私协议', res);
					},
					fail(err) {
						console.error('打开隐私协议失败', err)
					}
				});
			},
			// 不同意
			handleDisagree() {
				this.privacyResolves.forEach(resolve => {
					resolve({
						event: 'disagree',
					})
				})
				this.privacyResolves.clear()
				//关闭弹窗
				this.disPopUp()
				this.$emit('disagree')
				//退出小程序
				// wx.exitMiniProgram();
			},
			// 同意并继续
			handleAgree() {
				this.privacyResolves.forEach(resolve => {
					resolve({
						event: 'agree',
						buttonId: 'agree-btn'
					})
				})
				this.privacyResolves.clear()
				//关闭弹窗
				this.disPopUp()
				this.$emit('agree')
			},
			//打开弹窗
			popUp() {
				if (this.showPop === false) {
					this.showPop = true
				}
			},
			//关闭弹窗
			disPopUp() {
				if (this.showPop === true) {
					this.showPop = false
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.privacy-mask {
		position: fixed;
		z-index: 5000;
		top: 0;
		right: 0;
		left: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.2);
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.privacy-wrap {
		width: 632rpx;
		padding: 48rpx 30rpx;
		box-sizing: border-box;
		background: #fff;
		border-radius: 16rpx;
	}

	.privacy-title {
		padding: 0rpx 30rpx 40rpx 30rpx;
		font-weight: 700;
		font-size: 36rpx;
		text-align: center;
	}

	.privacy-desc {
		font-size: 30rpx;
		color: #555;
		line-height: 2;
		text-align: left;
		padding: 0 40rpx;
	}

	.privacy-link {
		color: #2f80ed;
	}

	.privacy-button-flex {
		display: flex;
		padding: 20rpx 40rpx;
	}

	.privacy-button-btn {
		color: #FFF;
		font-size: 30rpx;
		font-weight: 500;
		line-height: 100rpx;
		text-align: center;
		height: 100rpx;
		border-radius: 20rpx;
		border: none;
		background: #07c160;
		flex: 1;
		margin-right: 30rpx;
		justify-content: center;
	}

	.privacy-button-btn::after {
		border: none;
	}

	.bg-disagree {
		color: #07c160;
		background: #f2f2f2;
	}

	.bg-agree {
		margin-right: 0rpx;
	}
</style>