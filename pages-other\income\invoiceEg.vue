<template>
	<view class="invoice-container">
		
		<!-- 开票规则部分 -->
		<view class="section">
			<view class="section-title">开票规则</view>
			<view class="rules-container">
				<view class="rule-item important">开票金额价税合计金额为订单金额</view>
				<view class="rule-item important">发票类型：专用发票</view>

				<view class="rule-group">
					<view class="rule-item important">三嫂单发票项目：</view>
					<view class="rule-detail">*经纪代理服务*中介服务费</view>
				</view>

				<view class="rule-group">
					<view class="rule-item important">标准单发票项目：</view>
          <view class="rule-detail">通用：*生活服务*其他生活服务</view>
					<view class="rule-detail">搬家类业务：*物流辅助服务*搬运费</view>
					<view class="rule-detail">空调清洗类：*生活服务*清洗费</view>
					<view class="rule-detail">保洁类业务：*生活服务*保洁费</view>
				</view>
			</view>
		</view>
		
		<!-- 购买方信息部分 -->
		<view class="section">
			<view class="section-title">购买方信息</view>
			<uni-table border class="info-table">
				<uni-tr>
					<uni-td class="table-cell">
						<view class="info-item">
							<text class="label">名称：</text>
							<text class="value">厦门小羽佳家政股份有限公司</text>
						</view>
					</uni-td>
				</uni-tr>
				<uni-tr>
					<uni-td class="table-cell">
						<view class="info-item">
							<text class="label">电话：</text>
							<text class="value">05922619629</text>
						</view>
					</uni-td>
				</uni-tr>
				<uni-tr>
					<uni-td class="table-cell">
						<view class="info-item">
							<text class="label">税号：</text>
							<text class="value">913502005562167719</text>
						</view>
					</uni-td>
				</uni-tr>
				<uni-tr>
					<uni-td class="table-cell">
						<view class="info-item">
							<text class="label">卡号：</text>
							<text class="value">129400100100164502</text>
						</view>
					</uni-td>
				</uni-tr>
				<uni-tr>
					<uni-td class="table-cell">
						<view class="info-item">
							<text class="label">开户行：</text>
							<text class="value">兴业银行厦门莲前支行</text>
						</view>
					</uni-td>
				</uni-tr>
				<uni-tr>
					<uni-td class="table-cell">
						<view class="info-item">
							<text class="label">地址：</text>
							<text class="value">厦门火炬高新创业园轩业楼3031室</text>
						</view>
					</uni-td>
				</uni-tr>
			</uni-table>
		</view>

		<!-- 操作按钮区域 -->
		<view class="action-container" v-if="showActionButton">
			<view class="action-button" @click="confirmAndGoBack">
				我已知晓，去提现
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			showActionButton: false
		}
	},
	onLoad(options) {
		// 检查是否从 rollout 页面跳转过来
		if (options.from === 'rollout') {
			this.showActionButton = true
		}
	},
	methods: {
		// 确认知晓并返回提现页面
		confirmAndGoBack() {
			uni.navigateBack({
				delta: 1
			})
		}
	}
}
</script>

<style scoped>
.invoice-container {
	padding: 32rpx;
	background-color: #f8f9fa;
	min-height: 100vh;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.section {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	border: 1rpx solid #e9ecef;
}

.section-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #2c3e50;
	margin-bottom: 24rpx;
	padding-bottom: 16rpx;
	border-bottom: 3rpx solid #3498db;
	position: relative;
}

.section-title::before {
	content: '';
	position: absolute;
	left: 0;
	bottom: -3rpx;
	width: 60rpx;
	height: 3rpx;
	background-color: #e74c3c;
}

.info-table {
	width: 100%;
	border-radius: 12rpx;
	overflow: hidden;
	border: 1rpx solid #dee2e6;
}

.table-cell {
	padding: 24rpx !important;
	background-color: #ffffff;
	border-bottom: 1rpx solid #f1f3f4;
	transition: background-color 0.2s ease;
}

.table-cell:hover {
	background-color: #f8f9fa;
}

.info-item {
	display: flex;
	align-items: center;
	line-height: 1.6;
}

.label {
	font-weight: 500;
	color: #6c757d;
	min-width: 120rpx;
	font-size: 28rpx;
}

.value {
	color: #2c3e50;
	font-size: 28rpx;
	flex: 1;
	word-break: break-all;
}

.rules-container {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.rule-item {
	font-size: 30rpx;
	line-height: 1.6;
	padding: 16rpx 20rpx;
	border-radius: 8rpx;
	background-color: #fff5f5;
	border-left: 6rpx solid #e74c3c;
}

.rule-item.important {
	color: #c0392b;
	font-weight: 600;
	background-color: #ffeaea;
}

.rule-group {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
	margin-top: 8rpx;
}

.rule-detail {
	font-size: 28rpx;
	color: #34495e;
	padding: 12rpx 20rpx;
	margin-left: 32rpx;
	background-color: #f8f9fa;
	border-radius: 6rpx;
	border-left: 3rpx solid #95a5a6;
	line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.invoice-container {
		padding: 24rpx;
	}

	.section {
		padding: 24rpx;
		margin-bottom: 24rpx;
	}

	.section-title {
		font-size: 32rpx;
	}

	.label {
		min-width: 100rpx;
		font-size: 26rpx;
	}

	.value {
		font-size: 26rpx;
	}

	.rule-item {
		font-size: 28rpx;
	}

	.rule-detail {
		font-size: 26rpx;
		margin-left: 24rpx;
	}
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
	.invoice-container {
		background-color: #1a1a1a;
	}

	.section {
		background-color: #2d2d2d;
		border-color: #404040;
	}

	.section-title {
		color: #ffffff;
	}

	.table-cell {
		background-color: #2d2d2d !important;
		border-color: #404040;
	}

	.table-cell:hover {
		background-color: #3a3a3a !important;
	}

	.label {
		color: #b0b0b0;
	}

	.value {
		color: #ffffff;
	}

	.rule-item {
		background-color: #3a2a2a;
		color: #ff6b6b;
	}

	.rule-item.important {
		background-color: #4a2a2a;
		color: #ff8a8a;
	}

	.rule-detail {
		background-color: #3a3a3a;
		color: #e0e0e0;
		border-color: #606060;
	}
}

/* 打印样式 */
@media print {
	.invoice-container {
		background-color: white;
		padding: 0;
	}

	.section {
		box-shadow: none;
		border: 1rpx solid #ccc;
		page-break-inside: avoid;
	}

	.section-title {
		color: #000;
	}

	.rule-item.important {
		color: #000;
		background-color: #f5f5f5;
	}
}

/* 操作按钮样式 */
.action-container {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 32rpx;
	background-color: #ffffff;
	border-top: 1rpx solid #e9ecef;
	box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
	z-index: 999;
}

.action-button {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #ffffff;
	font-size: 32rpx;
	font-weight: 600;
	letter-spacing: 2rpx;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.action-button::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
	transition: left 0.5s ease;
}

.action-button:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.action-button:active::before {
	left: 100%;
}

/* 为按钮区域预留空间 */
.invoice-container {
	padding-bottom: 160rpx;
}

/* 响应式按钮样式 */
@media (max-width: 750rpx) {
	.action-container {
		padding: 24rpx;
	}

	.action-button {
		height: 80rpx;
		font-size: 30rpx;
	}

	.invoice-container {
		padding-bottom: 140rpx;
	}
}

/* 深色模式按钮样式 */
@media (prefers-color-scheme: dark) {
	.action-container {
		background-color: #2d2d2d;
		border-color: #404040;
	}

	.action-button {
		background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
		box-shadow: 0 8rpx 24rpx rgba(74, 85, 104, 0.3);
	}

	.action-button:active {
		box-shadow: 0 4rpx 12rpx rgba(74, 85, 104, 0.4);
	}
}
</style>