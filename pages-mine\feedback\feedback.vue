<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 栏目轮播 -->
		<view class="swiperBg">
			<swiper class="swiper" :indicator-dots="true" :autoplay="true" indicator-color="rgba(255, 255, 255, .5)"
				indicator-active-color="#fff" :interval="4000" :duration="1500">
				<swiper-item v-for="(item,index) in menuList" :key="index" v-if="item.show">
					<view class="swiper-item" @click="openDetail(index)">
						<img :src="item.img||blankImg" mode="widthFix" />
					</view>
				</swiper-item>
			</swiper>
		</view>

		<!-- 分类菜单 -->
		<view class="w95 flac-row-a" style="margin: 50rpx auto;">
			<view class="flex-col-c" v-for="(item,index) in menuList" :key="index" @click="openDetail(index)"
				v-if="item.show">
				<img :src="item.icon" style="width: 100rpx;height: 100rpx;" />
				<view class="f15 fb c6 lh35">{{item.title}}</view>
			</view>
		</view>

		<!-- 评价栏目 -->
		<view class="boxStyle bacf">
			<view class="flac-row-b lh50">
				<view class="fb f16">最新反馈</view>
				<view class="c6 flac-row">时间排序<u-icon name="arrow-down-fill" size="10" /></view>
			</view>

			<!-- 反馈列表 -->
			<view class="commentBox">
				<view class="listStyle" v-for="(item,index) in postsList" :key="index" v-if="!item.is_admin">
					<view class="flac-row-b" @click="openPost(index)">
						<view class="flac-row">
							<u-avatar :src="item.avatar_url||blankHeadImg" size="28" shape="circle" />
							<view class="t-indent fb">{{item.nick_name || '匿名用户'}}</view>
						</view>
						<view class="f12 c9">{{item.updated_at || ''}} | {{item.time || ''}}</view>
					</view>
					<view v-if="!item.contentDetail" @click="openPost(index)">
						<view :class="item.isShow ? '':'textStyle'">
							{{formatContent(index) || '暂无评价内容'}}
						</view>
					</view>
					<view v-else @click="openPost(index)">
						<view class="fb">
							{{item.contentDetail.title}}
						</view>
						<view>
							{{item.contentDetail.content?'痛点：'+item.contentDetail.content:''}}
						</view>
						<view>
							{{item.contentDetail.solution?'想法：'+item.contentDetail.solution:''}}
						</view>
					</view>
					<view class="imgBox">
						<img class="imgStyle" v-for="(item1,index1) in item.images"
							v-if="item.isShow||index1<maxShowPhoto" :key="index1" :src="item1.original_url"
							mode="widthFix" @click="openImgPreview(item1.original_url)">
						</img>
					</view>
					<view class="text-r" style="color: #1e1848;" @click="item.isShow = !item.isShow"
						v-if="item.content.length>maxTextLength">{{item.isShow ? '收起':'展开'}}
					</view>
				</view>

				<u-empty v-if=" !postsList.length" text="暂无反馈" icon="http://cdn.uviewui.com/uview/empty/data.png" />

				<view class="list-bottom" v-if="postsList.length">
					<text>已显示全部内容</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 可设置
				// 兔小巢渠道
				source: 'xyjAcn',
				// 兔小巢产品id
				productId: '',
				// 反馈最大显示字数
				maxTextLength: 40,
				// 评价最大照片显示数
				maxShowPhoto: 3,

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},

				memberId: uni.getStorageSync('memberId') || '',
				account: uni.getStorageSync('account') || '',
				employeeNo: uni.getStorageSync('employeeNo') || '',
				memberName: uni.getStorageSync('memberName') || '',
				employeeName: uni.getStorageSync('employeeName') || '',
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1664173884015192x192.png",
				blankHeadImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png',
				menuList: [{
					index: 0,
					img: 'https://xyj-public-static.obs.myhuaweicloud.com/jj-icon/常见问题.png',
					icon: 'https://xyj-public-static.obs.myhuaweicloud.com/jj-icon/常见问题.png',
					title: 'QA常见问题',
					path: 'scenes/embed__faqs-more/index',
					url: 'https://txc.qq.com/embed/phone/PRODUCT_ID/faqs-more',
					show: true,
				}, {
					index: 1,
					img: 'https://xyj-public-static.obs.myhuaweicloud.com/jj-icon/更新日志.png',
					icon: 'https://xyj-public-static.obs.myhuaweicloud.com/jj-icon/更新日志.png',
					title: '更新日志',
					path: 'scenes/embed__change-log/index',
					url: 'https://txc.qq.com/embed/phone/PRODUCT_ID/change-log',
					show: true,
				}, {
					index: 2,
					img: 'https://xyj-public-static.obs.myhuaweicloud.com/jj-icon/功能投票.png',
					icon: 'https://xyj-public-static.obs.myhuaweicloud.com/jj-icon/功能投票.png',
					title: '功能投票',
					path: 'scenes/embed__roadmap/index',
					url: 'https://txc.qq.com/embed/phone/PRODUCT_ID/roadmap',
					show: true,
				}, {
					index: 3,
					img: 'https://xyj-public-static.obs.myhuaweicloud.com/jj-icon/建议反馈.png',
					icon: 'https://xyj-public-static.obs.myhuaweicloud.com/jj-icon/建议反馈.png',
					title: '建议反馈',
					path: '',
					url: 'https://txc.qq.com/embed/phone/PRODUCT_ID',
					show: true,
				}],
				searchCondition: {
					maxId: '',
					count: 30,
				},
				postsList: [],
				path: '',
				urlPath: '',
				showIndex: 0,
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 格式化评价
			formatContent(index) {
				let post = this.postsList[index].content || ""
				let isShow = this.postsList[index].isShow || false
				let maxTextLength = this.maxTextLength
				if (!isShow && post.length > maxTextLength) {
					post = post.substring(0, maxTextLength) + "..."
				}
				return post
			},
			// 打开图片浏览
			openImgPreview(url) {
				let data = []
				data.push(url)
				uni.previewImage({
					urls: data,
					current: url
				})
			},
			openPost(index) {
				let id = this.postsList[index].id
				let path = 'scenes/embed__post/index?id=' + id
				let url = 'https://txc.qq.com/embed/phone/PRODUCT_ID/post/' + id
				this.jumpToTxc(1, path, url)
			},
			openDetail(index) {
				let path = this.menuList[index].path
				let url = this.menuList[index].url
				this.jumpToTxc(1, path, url)
			},
			// 获取兔小巢反馈列表
			listTxcPosts() {
				this.$set(this.searchCondition, 'source', this.source)
				this.http({
					outsideUrl: "https://api.xiaoyujia.com/openapi/listTxcPosts",
					data: this.searchCondition,
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.postsList = res.data
							this.postsList.forEach(item => {
								this.$set(item, 'isShow', false)
								if (item.content.includes('title')) {
									this.$set(item, 'contentDetail', JSON.parse(item.content))
								}
							})
						}
					}
				})
			},
			// 跳转到兔小巢
			jumpToTxc(value, path, url) {
				if (value == 0) {
					return
				}
				// #ifndef MP-WEIXIN
				url = url || 'https://txc.qq.com/embed/phone/PRODUCT_ID'
				let data = JSON.stringify({
					url: url.replace('PRODUCT_ID', this.productId)
				})
				uni.navigateTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
				})
				// #endif
				// #ifdef MP-WEIXIN
				let data = {
					appId: "wx8abaf00ee8c3202e",
					extraData: {
						id: this.productId,
						customInfo: 'memberId/' + this.memberId + '*phone/' + uni.getStorageSync('account') +
							'*employeeNo/' + this.employeeNo || '-' +
							'*realName/' + (this.employeeName || this.memberName),
					},
				}
				if (path) {
					this.$set(data, 'path', path)
				}
				wx.openEmbeddedMiniProgram(data)
				// #endif
			},
			// 获取兔小巢配置
			getTxcConfig() {
				this.http({
					outsideUrl: "https://api.xiaoyujia.com/openapi/getTxcConfig",
					path: this.source,
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.productId = res.data.productId
							// 当showIndex值为1时，则进入页面后立即进行跳转，反之不跳转
							this.jumpToTxc(this.showIndex, this.path, this.urlPath)
							this.listTxcPosts()
						}
					}
				})

			}
		},
		// 分享到好友或朋友圈
		onShareAppMessage(res) {
			let url = '/pages-mine/feedback/feedback?showIndex' + this.showIndex
			url = this.path ? url + '&path=' + this.path : url
			url = this.urlPath ? url + '&url=' + this.urlPath : url
			return {
				title: '用户中心',
				path: url,
				mpId: 'wx8342ef8b403dec4e'
			}
		},
		onLoad(options) {
			this.path = options.path || null
			this.urlPath = options.url || null
			this.showIndex = options.showIndex || 0
			this.getTxcConfig()
		}
	}
</script>

<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
	}

	/deep/.swiperBg {
		width: 100%;
		padding: 40rpx 0;
		background: url('https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/studyBg.png') no-repeat center;
		background-size: 100% 100%;
	}

	.swiper {
		width: 100%;
		margin: auto;
		height: 350rpx;
	}

	.swiper-item {
		img {
			display: block;
			width: 350rpx;
			height: 350rpx;
			margin: 0 auto;
		}
	}

	.ce7405d {
		color: #e7405d;
	}

	.boxStyle {
		padding: 20rpx 40rpx;
		margin-bottom: 30rpx;
	}

	.tagStyle {
		display: flex;
		flex-wrap: nowrap;
		align-items: center;
		width: auto;
		margin: 10rpx;
		display: inline-block;

	}

	/deep/.u-tag__text--medium {
		line-height: 26px;
	}

	.commentBox {
		border-top: 2rpx solid #eee;
		margin: 60rpx auto;
	}

	.listStyle {
		padding: 40rpx 20rpx;
	}

	.iconStyle {
		background-color: #f3efeb;
		color: #8f755d;
		padding: 0 10rpx;
		border-radius: 4rpx;
	}

	.textStyle {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box; //作为弹性伸缩盒子模型显示。
		-webkit-box-orient: vertical; //设置伸缩盒子的子元素排列方式--从上到下垂直排列
		-webkit-line-clamp: 3; //显示的行
	}

	.imgBox {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
	}

	.imgStyle {
		display: block;
		width: 30%;
		margin: 10rpx;
		border-radius: 10rpx;
	}

	.list-bottom {
		width: 100%;
		height: 60rpx;
		line-height: 60rpx;
		margin-bottom: 200rpx;

		text {
			display: block;
			text-align: center;
			color: #909399;
		}
	}
</style>