<template>
	<view>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 分享图片 -->
		<image style="width:100%;display:block" mode="widthFix"
			src="https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1661935544270fenxiangye.jpg"
			@click="share"></image>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				type: 'center',
				type1: 'success',
				msgType: "success",
				msgText: "",
				// 活动ID和代码
				activityId: 1,
				activityCode: '',
				memberId: uni.getStorageSync('memberId'),
				accaptMemberId: 0
			};
		},
		methods: {
			share() {
				uni.showToast({
					title: '请点击右上角...进行分享',
					icon: "none",
					duration: 5000
				})
				// 分享后调用
				this.shareAvtivity()

			},
			shareAvtivity() {
				// 请求：兑换奖品
				let data = {
					shareMemberId: this.memberId,
					accaptMemberId: this.accaptMemberId,
					activityId: this.activityId
				}
				this.http({
					url: 'shareActivity',
					method: 'POST',
					data: JSON.stringify(data),
					header: {
						"contentType": "application/json;charset=UTF-8 "
					},
					success: res => {
						// 请求成功之后
						if (res.code == 0) {
							this.$refs.uNotify.success("分享成功！博饼次数+1")
						} else {
							this.$refs.uNotify.error("分享失败！" + res.msg)
						}
					},
					fail: err => {
						console.log('分享活动-请求失败！' + res.msg)
					}
				})
			},
		},
		onLoad(e) {
			console.log('活动编号:' + e.activityCode)
			this.activityCode = e.activityCode;
		},
		onShareAppMessage(res) {
			let shareobj = {
				title: '小羽佳-中秋博饼分享', //分享的标题
				path: '/pages-mine/activity/bobingtoshare?activityCode=' + this.activityCode + '&shareMemberId=' + this
					.memberId, //好友点击分享之后跳转的页面
				imageUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662098026925sharett.jpg", //分享的图片
			}

			return shareobj;
		},
		// 页面载入后
		mounted() {

		}
	}
</script>

<style>
</style>
