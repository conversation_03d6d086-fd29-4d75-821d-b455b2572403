<template>
	<view>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 操作确认弹窗 -->
		<uni-popup ref="popupCheck" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="确认" :title="checkTitle" :content="checkText"
				@confirm="popupCheck()"></uni-popup-dialog>
		</uni-popup>

		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>


		<!-- 栏目图片 -->
		<view class="head-img">
			<img :src="examRankImg" mode="widthFix" />
			<text>{{examTitle}}</text>
			<view class="btn-switch">
				<uni-icons type="loop" size="20" color="#F9AE3D" @click="showRealName=!showRealName"></uni-icons>
			</view>
		</view>

		<!-- 排行榜-自己 -->
		<view class="list">
			<listItem class="list-item" :item="JSON.stringify(examRankMine)=='{}'?examRankBlank:examRankMine"
				:showRealName="showRealName" />
		</view>

		<view style="box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(222,222,222,0.5) inset;height: 20rpx;">
		</view>

		<!-- 排行榜 -->
		<view class="list">
			<listItem class="list-item" v-for="(item,index) in examRank" :key="index" :item="item"
				:showRealName="showRealName" />
		</view>

		<view class="list-bottom" v-if="searchCondition.current>=pageCount">
			<text>已显示全部内容</text>
		</view>

	</view>
</template>

<script>
	import listItem from "@/pages-mine/common/components/list-item.vue";

	export default {
		components: {
			listItem
		},
		data() {
			return {
				// 可设置
				// 显示真名
				showRealName: false,

				// 确认弹框
				checkType: 0,
				checkTitle: "",
				checkText: "",

				loadMore: 0,
				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#ffffff'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},

				service: "https://biapi.xiaoyujia.com/",
				rankIcon: [
					"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_icon1.png",
					"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_icon2.png",
					"https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/rank_icon3.png"
				],
				examRankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/exam/exam-ranking.png",
				blankImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png',

				pageCount: 0,
				searchCondition: {
					examId: null,
					memberId: null,
					recordState: 2,
					orderBy: "recordScore DESC",
					current: 1,
					size: 20,
				},
				examId: 0,
				memberId: null,
				examRankIndex: -1,
				examTitle: "",
				examRank: [],
				examRankMine: {},
				examRankBlank: {
					index: 99999,
					name: uni.getStorageSync("memberName") || "",
					headImg: uni.getStorageSync("memberHeadImg") || "",
					realName: uni.getStorageSync("employeeName") || "",
					headPortrait: uni.getStorageSync("employeeHeadImg") || "",
					memberId: uni.getStorageSync("memberId") || "",
					employeeId: uni.getStorageSync("employeeId") || "",
					recordScore: null
				}
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			// 获取考试排行榜
			getExamRank(value) {
				this.searchCondition.examId = this.examId
				this.searchCondition.memberId = this.memberId

				if (value == 0) {
					this.searchCondition.size = 10
				} else if (value == 1) {
					this.searchCondition.size = 1000
				}
				this.http({
					url: 'getUnionExamRank',
					method: 'POST',
					hideLoading: true,
					data: this.searchCondition,
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							if (value == 0) {
								this.examRank = this.examRank.concat(res.data.records)
								this.pageCount = res.data.pages
								if (this.searchCondition.current == 1) {
									this.showRealName = res.data.records[0].showRealName == 1 ? true : false
								}
								this.formatRecord()
							} else if (value == 1) {
								this.listUnionExamRecord(res.data.records[0].rankNum, res.data.records[0]
									.rankNumId)
							}
						} else {
							console.log('获取考试排行榜-返回错误！' + res.msg)
						}
					}
				});
			},
			// 获取本人成绩排行
			listUnionExamRecord(rankNum, rankNumId) {
				// 暂无排名
				if (rankNum == -1) {
					this.examRankMine = {}
					return
				}

				this.http({
					url: 'listUnionExamRecord',
					method: 'POST',
					hideLoading: true,
					data: {
						id: rankNumId
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.examRankMine = res.data[0]
							this.examTitle = this.examRankMine.examTitle
							this.$set(this.examRankMine, "index", rankNum - 1)
						}
					}
				});
			},
			// 格式化成绩
			formatRecord() {
				for (let i = 0; i < this.examRank.length; i++) {
					let item = this.examRank[i]
					this.$set(item, "index", i)
				}
			},
			// 打开确认框
			openCheck(checkType, checkTitle, checkText) {
				this.checkType = checkType
				this.checkTitle = checkTitle
				this.checkText = checkText
				this.$refs.popupCheck.open()
			},
			// 确认框功能
			popupCheck() {
				// Tpye的值可以控制，0：xxx确认
				if (this.checkType == 0) {}
			},
		},
		onLoad(options) {
			this.examId = options.id
			this.examTitle = options.examTitle || ""
			this.memberId = uni.getStorageSync("memberId")

			// 测试时加上
			// this.examId = 2
			// this.service = "http://localhost:8063/"

			this.getExamRank(1)
			this.getExamRank(0)
		},
		watch: {
			loadMore: {
				handler(newValue, oldVal) {
					this.searchCondition.current++
					this.getExamRank(0)
				},
				deep: true
			}
		},
		onReachBottom() {
			this.loadMore++
		},
	}
</script>


<style lang="scss">
	@import "@/pages-mine/common/css/exam.scss";

	.tag-small {
		position: absolute;
		width: 100%;

		text {
			display: block;
			height: 70rpx;
			line-height: 70rpx;
			width: 260rpx;

			font-size: 32rpx;
			border-radius: 30rpx;
			color: #ffffff;
			background-color: #1e1848;
		}
	}

	.btn-switch {
		position: absolute;
		left: 250rpx;
		top: 64rpx;
	}

	.list-bottom {
		width: 100%;
		height: 120rpx;
		line-height: 120rpx;

		text {
			display: block;
			text-align: center;
			color: #909399;
		}
	}
</style>