<template>
	<view>

		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<view class="shadow">
			<view style="height: 25rpx;"/>
			<u-steps current="3" activeColor="#1e1848" class="steps">
				<u-steps-item title="认证缴费" />
				<u-steps-item title="主体信息" />
				<u-steps-item title="商户信息" />
				<u-steps-item title="平台审核" />
				<u-steps-item title="账户验证" />
			</u-steps>
			<view style="height: 25rpx;"/>
		</view>

		<view v-if="flag==1">
			<view class="shadow">
				<view>
					<image class="img" :src="timeUrl" />
				</view>
				<view class="companyMsg">
					<text>入驻资料提交成功</text>
				</view>
				<view class="text-state">
					<text>预计在1~3个工作日反馈审核结果，请耐心等待</text>
				</view>
				<view class="button-state" >
					<u-button @click="previewMsg" customStyle="color:#f6cc70" color="#1e1848" >预览审核信息</u-button>
				</view>
			</view>
		</view>

		<view v-if="flag==2">
			<view>
				<image class="img" :src="successUrl" />
			</view>
			<view class="companyMsg">
				<text>恭喜你！平台审核通过！</text>
			</view>
			<view class="button-state">
				<u-button customStyle="color:#f6cc70" color="#1e1848" @click="goaccountVerify">前往账户验证</u-button>
			</view>
		</view>

		<view v-if="flag==0">
			<view>
				<image class="img" :src="errorUrl" />
			</view>
			<view class="companyMsg">
				<text style="line-height: 30rpx;">抱歉！平台审核不通过，请修改并检查信息无误后重新提交！</text>
				<view>
					<text v-if="remark">驳回原由：{{remark}}</text>
				</view>
			</view>
			<view class="button-stateA">
				<u-button @click="updateMsg"  customStyle="color:#f6cc70" color="#1e1848" >修改审核信息</u-button>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				timeUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/****************时间.png",
				successUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/*************通过.png",
				errorUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/*************不通过1.png",
				msgType: "success",
				msgText: "",
				remark: null,
				flag: null
			}
		},

		methods: {
			goaccountVerify() {
				uni.navigateTo({
					url: '/pages-mine/franchise/enter/accountVerify'
				})
			},
			updateMsg() {
				uni.navigateTo({
					url: '/pages-mine/franchise/enter/bodyMsg?type=3'
				})
			},
			getState() {
				this.http({
					url: "getFranchiseMsgById",
					method: 'GET',
					path: uni.getStorageSync("memberId"),
					success: res => {
						if (res.code == 0) {
							this.flag = res.data.status
							this.remark = res.data.remark
						} else {
							this.$refs.uNotify.error(res.msg)
						}
					},
					fail: err => {
						console.log('请求失败！' + res)
					}
				})
			},
			previewMsg() {
				uni.navigateTo({
					url: '/pages-mine/franchise/enter/bodyMsg?type=1'
				})
			},// 分享到好友或朋友圈
			onShareAppMessage(res) {
				return {
					title: '小羽佳-家姐联盟',
					path: '/pages-mine/franchise/enter/topUpMoney?id=' + uni.getStorageSync("memberId"),
					mpId: 'wx8342ef8b403dec4e'
				}
			},
			onShareTimeline(res) {
				return {
					title: '小羽佳-家姐联盟',
					type: 0,
					summary: "欢迎加入小羽佳-家姐联盟！在这里你可以收获小羽佳最新家政动态，并加入我们，成为小羽佳的一员！",
				}
			}
		},
		// 页面加载后
		mounted() {
			this.getState()
		}
	}
</script>

<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
		font-size: 32rpx;
	}

	.img {
		width: 60rpx;
		height: 60rpx;
		margin-left: 4%;
		margin-top: 4%;
	}

	.button-state {
		margin-top: 900rpx;
	}
	
	.button-stateA {
		margin-top: 900rpx;
	}

	.text-state {
		color: darkgrey;
		margin-top: -1%;
		margin-left: 30rpx;
	}

	.steps {
		padding-bottom: 30rpx;
		padding-top: 25rpx;
	}

	.shadow {
		height: auto;
		width: 100%;
		margin-bottom: 30rpx;
		// 添加栏目底部阴影
		box-shadow: 0 4rpx 20rpx #dedede;
	}

	.companyMsg {
		color: black;
		margin-left: 14%;
		margin-top: -12%;
		line-height: 100rpx;
	}
</style>
