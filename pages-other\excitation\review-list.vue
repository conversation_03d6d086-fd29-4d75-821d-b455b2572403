<template>
	<view>
		<u-back-top :scroll-top="scrollTop" icon="arrow-up" :iconStyle="backTopStyle.iconStyle"
			:customStyle="backTopStyle.customStyle" :duration="200"></u-back-top>
		<!-- 上方信息弹窗 -->
		<u-notify ref="uNotify"></u-notify>

		<!-- 头部显示 -->
		<view class="back-img flac-col">
			<view class="head-tab flac-row-b f16">
				<view class="w2" @click="goPage('/pages-other/excitation/index')">
					<button plain="true" style="border: none;">
						<uni-icons type="person" size="20" color="#fff"></uni-icons>
					</button>
				</view>
				<!-- 				<view class="w2" @click="shareType=0">
					<button plain="true" open-type="share" style="border: none;">
						<uni-icons type="redo" size="20" color="#fff"></uni-icons>
					</button>
				</view> -->
			</view>
			<img :src="backImg" alt="" />
			<view class="head-tab1">
				<img :src="headImg||blankHeadImg" class="" @click="openImgPreview(headImg)" />
				<text class="f18">{{userName}}</text>
			</view>
		</view>

		<!-- 搜索 -->
		<view class="w9" style="margin: 20rpx auto;" v-if="!showMine">
			<u-search bgColor="#fff" :clearabled="true" :showAction="true" :animation="true"
				v-model="searchCondition.search" placeholder="通过姓名/工号/部门进行搜索" @search="search" @custom="search"
				@clear="searchCondition.search='';" />
		</view>

		<!-- 员工列表 -->
		<view class="main-bar f16" v-for="(item,index) in list" :key="index">
			<view class="flac-col" style="padding: 0 20rpx;">
				<view class="flac-row">
					<view class="w2" @click="openImgPreview(item.headImg)">
						<u-avatar :src="item.headImg" size="50" />
					</view>
					<view class="flac-col w8" style="padding: 10rpx 0;" @click="openDetail(index)">
						<view class="flac-row-b">
							<text class="main-text f18" style="display: block;">{{item.employeeName || '匿名用户'}}</text>
							<text class="f14" style="display: block;">{{item.createTime}}</text>
						</view>
						<view style="margin: 15rpx 0rpx;" v-if="item.departName">
							{{item.departName}}
						</view>
					</view>
				</view>
				<view class="w8 lh30" style="margin: 0 20%;">
					<view>
						<view @click="copyText(item.flowContent)">
							<text class="fb flac-col">流程一句话：</text>
							{{item.flowContent||'暂无'}}
							<uni-icons type="paperclip" style="margin-left: 5rpx;display: inline-block;" size="18"
								color="#909399">
							</uni-icons>
						</view>
						<view @click="openDetail(index)">
							<view>
								<text class="fb">每日目标：</text>
								{{item.dailyGoal||'暂无'}}{{item.dailyGoalRate?'-'+item.dailyGoalRate+'%':''}}
							</view>
							<view>
								<text class="fb">每周目标：</text>
								{{item.weeklyGoal||'暂无'}}{{item.weeklyGoalRate?'-'+item.weeklyGoalRate+'%':''}}
							</view>
							<view>
								<text class="fb">团队目标：</text>
								{{item.teamGoal||'暂无'}}
							</view>

							<view>
								<text class="fb">左手动作：</text>
								{{item.leftHand||'暂无'}}
							</view>
							<view>
								<text class="fb">左手工作项：</text>
								{{formatText(item.dailyThing)}}
							</view>
							<view>
								<text class="fb">做对的动作：</text>
								{{formatText(item.dailyImprovement)}}
							</view>
							<view>
								<text class="fb">协同人：</text>
								{{item.collaborator||'暂无'}}
							</view>
						</view>
						<view class="flac-row">
							<text class="fb">是否自荐标杆：</text>
							<uni-icons :type="item.selfRecommend==1?'checkmarkempty':'closeempty'" size="24"
								:color="item.selfRecommend==1?'#19be6b':'#ff4d4b'"></uni-icons>
							<view class="btn-commit" v-if="item.selfRecommend==0"
								@click="dailyReviewSelfRecommendation(index)">
								一键推荐
							</view>
						</view>
						<view v-if="showMine" @click="goPage('/pages-other/excitation/review?id='+item.id)">
							<text class="fb">编辑复盘：</text>
							<uni-icons type="compose" style="margin-left: 5rpx;display: inline-block;" size="18"
								color="#909399">
							</uni-icons>
						</view>
					</view>
				</view>
			</view>
		</view>

		<u-empty v-if="list.length==0" text="暂无复盘" icon="http://cdn.uviewui.com/uview/empty/data.png" />

		<view class="list-bottom" v-if="list.length">
			<text v-if="searchCondition.current>=pageCount">已显示全部内容</text>
			<text v-else>加载中...</text>
		</view>

		<u-gap height="400"></u-gap>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 可设置
				// 分享模式（0：本页面分享 1：详情分享）
				shareType: 0,
				// 只显示自己
				showMine: false,

				scrollTop: 0,
				backTopStyle: {
					iconStyle: {
						fontSize: '32rpx',
						color: '#f6cc70'
					},
					customStyle: {
						backgroundColor: '#1e1848'
					}
				},
				baseUrl: 'https://api.xiaoyujia.com/',
				// baseUrl: 'http://localhost:8080/',
				choiceIndex: 0,
				blankHeadImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo_fill.png",
				backImg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/excitation/excitation_back.png',
				headImg: uni.getStorageSync("employeeHeadImg") || uni.getStorageSync("memberHeadImg") || '',
				userName: uni.getStorageSync("employeeName") || uni.getStorageSync("memberName") || '匿名用户',
				memberId: uni.getStorageSync("memberId") || null,
				employeeId: uni.getStorageSync("employeeId") || null,

				total: 0,
				pageCount: 0,
				searchCondition: {
					search: '',
					orderBy: "t.createTime DESC",
					current: 1,
					size: 10
				},
				list: [],
				id: null,
			}
		},
		methods: {
			onPageScroll(e) {
				this.scrollTop = e.scrollTop
			},
			copyText(text) {
				uni.setClipboardData({
					data: text,
					success: () => {
						this.$refs.uNotify.success('内容复制成功!')
					}
				})
			},
			// 格式化内容
			formatText(text) {
				if (!text) {
					return '暂无'
				}
				let count = 1
				if (!text.includes('|')) {
					return text
				} else {
					let array = text.split('|')
					let textDeal = ''
					for (let i = 0; i < array.length; i++) {
						textDeal += count + '、' + array[i]
						count++
					}
					return textDeal
				}
			},
			goPage(url) {
				uni.navigateTo({
					url: url
				})
			},
			// 打开详情
			openDetail(index) {
				uni.navigateTo({
					url: '/pages-other/excitation/review-detail?id=' + this.list[index].id
				})
			},
			openImgPreview(img) {
				let data = []
				data.push(img)
				uni.previewImage({
					urls: data,
					current: img
				})
			},
			// 格式化时间
			formatDate(value) {
				if (value == null) {
					return "-"
				}
				if (typeof(value) == "string" && value.indexOf('-') != -1) {
					value = value.replace(/-/g, '/')
				}
				let date = new Date(value)
				let y = date.getFullYear()
				let MM = date.getMonth() + 1
				MM = MM < 10 ? ('0' + MM) : MM
				let d = date.getDate()
				d = d < 10 ? ('0' + d) : d
				return y + '-' + MM + '-' + d
			},
			// 查询
			search() {
				this.list = []
				this.searchCondition.current = 1
				this.pageDailyReview()
			},
			// 每日复盘自荐
			dailyReviewSelfRecommendation(index) {
				let id = this.list[index].id
				uni.showModal({
					title: '确定进行一键推荐吗？',
					content: '将把此复盘设为自荐，并发布对应表彰！',
					success: res => {
						if (res.confirm) {
							this.http({
								outsideUrl: this.baseUrl + 'content/dailyReviewSelfRecommendation',
								method: 'GET',
								path: id,
								success: res => {
									if (res.code == 0) {
										this.$refs.uNotify.success("已推荐为标杆！")
										this.$set(this.list[index], 'selfRecommend', 1)
									} else {
										this.$refs.uNotify.error(res.msg)
									}
								},
							})
						}
					}
				})
			},
			// 获取每日复盘列表
			pageDailyReview() {
				this.$set(this.searchCondition, 'memberId', this.showMine ? this.memberId : null)
				this.http({
					outsideUrl: this.baseUrl + 'content/pageDailyReview',
					method: 'POST',
					hideLoading: true,
					header: {
						'content-type': 'application/json;charset=UTF-8'
					},
					data: this.searchCondition,
					success: res => {
						if (res.code == 0) {
							this.total = res.data.total
							this.pageCount = res.data.pages
							this.list = this.list.concat(res.data.records)
						}
					},
				})
			},
			checkLogin() {
				if (!uni.getStorageSync('memberId')) {
					this.$refs.uNotify.warning("您还未进行登录哦，请先登录吧！")
					uni.setStorageSync('redirectUrl', '/pages-other/excitation/review-list')
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages-mine/login/login'
						})
					}, 2000)
					return false
				} else {
					return true
				}
			},
		},
		onReachBottom() {
			this.searchCondition.current++
			this.pageDailyReview()
		},
		onLoad(options) {
			if (options.scene !== undefined) {
				let scene = decodeURIComponent(options.scene)
				let obj = {}
				for (let i = 0; i < scene.split('*').length; i++) {
					let arr = scene.split('*')[i].split('/');
					obj[arr[0]] = arr[1];
				}
			}
			this.showMine = options.showMine ? true : false
			this.pageDailyReview()
		},
	}
</script>
<style lang="scss">
	@import "@/pages-mine/common/css/tab-menu.scss";
	@import "@/pages-mine/common/css/resume-tab.scss";

	page {
		height: auto;
		background-color: #ffffff;
	}

	.back-img {
		img {
			display: block;
			width: 100%;
			height: 550rpx;
		}
	}

	.swiper {
		width: 100%;
		height: 240rpx;
	}

	.post-img {
		display: block;
		width: 90%;
		margin: 20rpx auto;
	}

	.head-tab {
		position: absolute;
		width: 100%;
		top: 20rpx;
		left: 0%;
		color: #fff;
	}

	.head-tab1 {
		position: absolute;
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 40%;
		top: 100rpx;
		left: 30%;

		img {
			display: block;
			width: 180rpx;
			height: 180rpx;
			border-radius: 50%;
		}

		text {
			margin-top: 20rpx;
			text-align: center;
			color: #ffffff;
		}
	}

	.head-tab2 {
		position: absolute;
		width: 80%;
		top: 380rpx;
		left: 10%;
		color: #fff;
	}

	.text-bar {
		margin-top: 10rpx;
		width: 160rpx;
		height: 45rpx;
		line-height: 45rpx;
		background-color: rgba(47, 47, 47, 0.1);
		border-radius: 40rpx;
		text-align: center;
		font-size: 28rpx;
	}


	.tab-icon {
		display: block;
		width: 80rpx;
		height: 80rpx;
		-webkit-border-radius: 30rpx;
		-moz-border-radius: 30rpx;
		box-shadow: 5rpx 10rpx 20rpx #dedede;
	}

	.main-text {
		color: #1e1848;
		font-weight: bold;
	}

	.main-bar {
		width: 94%;
		height: auto;
		padding: 40rpx 3%;
		margin: 20rpx 0;
		border-bottom: 1rpx #dedede solid;
	}

	.bar {
		width: 100%;
		height: 80rpx;
		border-top: 1rpx #dedede dashed;
		border-bottom: 1rpx #dedede dashed;
		margin: 40rpx 0 20rpx 0;
	}

	.small-tab {
		width: 100%;
		height: 140rpx;
		border-bottom: 2rpx #dedede solid;
		margin: 20rpx 0;
	}

	.list-bottom {
		width: 100%;
		height: 40rpx;
		line-height: 40rpx;

		text {
			display: block;
			text-align: center;
			font-weight: 100;
		}
	}

	.scroll-Y {
		display: block;
		width: 90%;
		height: 850rpx;
		margin: 0rpx 5%;
	}

	/*
		排行榜
	*/
	.rank-img {
		width: 33.33%;
		height: 200rpx;
		padding: 30rpx 0;

		img:nth-child(1) {
			display: block;
			width: 120rpx;
			height: 120rpx;
			margin: 0 50rpx;
			border-radius: 50%;
		}

		img:nth-child(2) {
			position: absolute;
			width: 140rpx;
			height: 140rpx;
			margin: -130rpx 44rpx;
		}
	}

	.rank-tab {
		height: auto;
		width: 90%;
		margin: 0 auto;
		padding: 20rpx 0 60rpx 0;
		box-shadow: 0 4rpx 20rpx #dedede;
	}

	.btn-commit {
		margin-left: 20rpx;
		text-align: center;
		width: 175rpx;
		height: 50rpx;
		line-height: 50rpx;
		color: #ffffff;
		background-color: #1e1848;
		border-radius: 30rpx;
	}
</style>