<template>
	<view>
		<u-sticky bgColor="#fff">
			<image src="https://xyj-public-static.obs.myhuaweicloud.com/jj-icon/img-robot.png" class="w10"
				mode="widthFix">
			</image>
			<u-search placeholder="搜一搜遇到的疑问" @custom="getQADataBySearch" v-model="searchVal" bgColor="#f7f8fa"
				borderColor="#f5f5f5" :clearabled="true">
			</u-search>
			<u-tabs :list="tabsList" @click="choiceMenu" :current="tabsIndex" lineWidth="20" lineHeight="8"
				:lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
            color: '#1f2120',
            fontWeight: 'bold',
            transform: 'scale(1.1)'
        }" :inactiveStyle="{
            color: '#333',
            transform: 'scale(1)'
        }" itemStyle="width:20%;margin:auto;height:100rpx;">
			</u-tabs>
		</u-sticky>
		<view class="w9 bacf" style="margin: 20rpx auto;">
			<view class="flac-row-b lh50" v-for="(item,i) in unionQaList" :key="i" @click="clickDetail(i)">
				<view class="flac-row">
					<u-icon name="https://xyj-public-static.obs.myhuaweicloud.com/jj-icon/icon-question.png"
						size="25" />
					<view class="f16 fb t-indent">{{item.title}}</view>
				</view>
				<u-icon name="arrow-right" size="20"></u-icon>
			</view>
			<u-empty v-if="!unionQaList.length" text="暂无数据" icon="http://cdn.uviewui.com/uview/empty/data.png" />
		</view>
		<u-popup :show="showPopup" @close="showPopup=false" :closeable="true" mode="bottom" round="10">
			<view style="padding: 30rpx;" @click="openDetail">
				<view class="f18 fb lh40">{{unionQaData.title}}</view>
				<view class="f15 c3 lh20">{{unionQaData.replyText||replyList.length?'':'未配置答复'}}</view>
			</view>
			<scroll-view scroll-y="true" class="scroll-Y" style="height: 700rpx;">
				<view v-if="unionQaData.replyText" style="padding: 0rpx 5%;width: 90%;" @click="openDetail">
					<view v-html="unionQaData.replyText">
					</view>
				</view>
				<view v-else-if="replyList.length" style="padding: 25rpx;height: calc(62vh - 50px);">
					<view v-for="(item,i) in replyList" :key="i">
						<view class="f15 c3 lh20" style="padding-bottom: 20rpx;">
							{{item.sort}}、{{item.reply}}
						</view>
						<image :src="item.pictureUrl"
							style="display: block; width: 70%;height: 70%;border-radius: 5px;margin: 0 auto;margin-top: 20rpx;padding-bottom: 20rpx;"
							mode="widthFix" v-if="item.pictureUrl" />
						<view class="f15 c3 lh20" style="color:deepskyblue" v-if="item.routeUrl"
							@click="gotoRoute(item.routeUrl)">点击跳转</view>
					</view>
				</view>
			</scroll-view>
			<view style="padding: 25rpx;">
				<view class="border-bottom-2se" style="margin: 40rpx auto;"></view>
				<view class="flac-row-a" style="margin: 20rpx auto;">
					<view class="w5 mg-at">
						<u-icon name="kefu-ermai" size="22" label="联系客服" labelPos="right" labelSize="16"
							@click="clickCustomer" />
					</view>
					<view class="w4 mg-at mg-at flac-row-a">
						<u-icon name="thumb-up" size="22" :label="unionQaData.ofUse" labelPos="right" labelSize="16"
							:color="upColor" :labelColor="upLabelColor" @click="clickUse(1)" />
						<u-icon name="thumb-down" size="22" :label="unionQaData.noUse" labelPos="right" labelSize="16"
							:color="downColor" :labelColor="downLabelColor" @click="clickUse(0)" />
						<button plain open-type='share' style="width: 80rpx;margin: 0 0;padding: 0;" @click="share()">
							<uni-icons type="redo" size="22"></uni-icons>
						</button>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				searchVal: '',
				lineBg: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-smile.png',
				tabsIndex: 0,
				tabsList: [],
				unionQaList: [],
				replyList: [],
				answerList: [{
					title: '',
					content: ''
				}],
				showPopup: false,
				qaDataId: 0,
				unionQaData: {},
				upColor: '',
				upLabelColor: '',
				downColor: '',
				downLabelColor: '',
				typeID: null,
			};
		},
		onLoad() {
			this.getAllUnionType()
		},
		methods: {
			// 分享
			share() {
				//  #ifdef H5
				let url = 'https://jiajie.xiaoyujia.com/pages-mine/helpCenter/helpDetail?id=' + this.qaDataId
				uni.setClipboardData({
					data: url,
					success: () => {
						this.$refs.uNotify.success(name + '分享链接复制成功!')
					}
				})
				// #endif
			},
			gotoRoute(val) {
				uni.navigateTo({
					url: val
				})
			},
			openDetail() {
				let url = '/pages-mine/helpCenter/helpDetail?id=' + this.unionQaData.id
				console.log(url)
				uni.navigateTo({
					url: url
				})
			},
			// 打开图片预览
			openImgPreview(url) {
				let data = []
				data.push(url)
				uni.previewImage({
					urls: data,
					current: url
				})
			},
			getQADataBySearch() {
				if (!this.searchVal) {
					this.tabsIndex = 0
					return this.getAllUnionType()
				}
				this.http({
					url: 'getQADataBySearch',
					method: 'GET',
					hideLoading: true,
					data: {
						search: this.searchVal
					},
					success: res => {
						if (res.code == 0) {
							if (res.data) {
								this.typeID = res.data
								for (var i = 0; i < this.tabsList.length; i++) {
									if (this.tabsList[i].id == res.data) {
										this.tabsIndex = i
										break;
									}
								}
								this.getUnionQaByType()
							} else {
								uni.showToast({
									icon: 'none',
									title: '未查询到相关问答!'
								})
							}
						} else {
							uni.showToast({
								icon: 'none',
								title: '系统错误!'
							})
						}
					}
				})
			},
			getAllUnionType() {
				this.http({
					url: 'getAllUnionType',
					method: 'GET',
					hideLoading: true,
					success: res => {
						if (res.code == 0) {
							this.typeID = res.data[0].id
							this.tabsList = res.data
							this.getUnionQaByType()
						}
					}
				})
			},
			getUnionQaByType() {
				this.http({
					url: 'getUnionQaByType',
					method: 'GET',
					hideLoading: true,
					data: {
						typeID: this.typeID
					},
					success: res => {
						if (res.code == 0) {
							this.unionQaList = res.data
						}
					}
				})
			},
			getUnionQaById() {
				this.qaDataId = this.unionQaData.id
				this.http({
					url: 'getUnionQaById',
					method: 'GET',
					hideLoading: true,
					data: {
						id: this.unionQaData.id,
					},
					success: res => {
						if (res.code == 0) {
							this.unionQaData = res.data.unionQa
							this.replyList = res.data.replyList
							this.getUseLogData()
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg
							})
						}
					}
				})
			},
			getUseLogData() {
				this.http({
					url: 'getUseLogData',
					method: 'GET',
					hideLoading: true,
					data: {
						qaId: this.unionQaData.id,
						memberId: uni.getStorageSync('memberId')
					},
					success: res => {
						if (res.code == 0) {
							if (res.data == 1) {
								this.downColor = "#4991f7"
								this.upColor = ""
							} else if (res.data == 2) {
								this.upColor = "#4991f7"
								this.downColor = ""
							} else {
								this.upColor = ""
								this.downColor = ""
							}
						}
					}
				})
			},
			clear() {
				this.searchVal = ''
			},
			choiceMenu(e) {
				this.tabsIndex = e.index
				this.typeID = e.id
				this.getUnionQaByType()
			},
			clickDetail(i) {
				this.unionQaData = this.unionQaList[i]
				this.showPopup = true
				this.getUnionQaById()
				this.getUseLogData()
			},
			clickCustomer() {
				// #ifdef  MP-WEIXIN
				wx.openCustomerServiceChat({
					extInfo: {
						url: 'https://work.weixin.qq.com/kfid/kfc51d7db04b2a4a1d7' //客服地址链接
					},
					corpId: 'wx25c9a236883d741d', //必须和你小程序上的一致
					success(res) {
						console.log(res, 1)
					},
					fail(res) {
						console.log(res, 2)
					},
				})
				// #endif
				// #ifdef  MP-TOUTIAO || H5
				uni.showToast({
					icon: 'none',
					title: '请在小程序内操作!'
				})
				// #endif
			},
			clickUse(val) {
				this.http({
					url: 'saveQaUseLog',
					method: 'POST',
					hideLoading: true,
					data: {
						memberId: uni.getStorageSync('memberId'),
						clickType: val,
						qaId: this.unionQaData.id
					},
					header: {
						"content-type": "application/json;charset=UTF-8"
					},
					success: res => {
						if (res.code == 0) {
							this.getUnionQaById()
							setTimeout(() => {
								this.getUseLogData()
							}, 500)
						} else {
							uni.showToast({
								icon: 'none',
								title: '操作失败!'
							})
						}
					}
				})
			},
			// 分享
			onShareAppMessage(res) {
				let title = '帮助中心-' + this.unionQaData.title
				return {
					title: title,
					path: '/pages-mine/helpCenter/helpDetail?id=' + this.qaDataId,
					mpId: 'wx8342ef8b403dec4e'
				}
			},
		},
	}
</script>

<style lang="scss" scoped>
	/deep/.uni-searchbar__box {
		border: 2rpx solid #eee;
		justify-content: unset;
	}

	button[plain] {
		border: 0
	}
</style>