<template>
	<view v-if="false">
		<view class="head-img">
			<img src="" alt="" :src="headImg">
		</view>
		<!-- 		<view class="introduce-img">
			<img :src="logo1" mode="widthFix" @click="openImgPreview()" />
		</view>
		<view style="text-align: center;line-height: 60rpx;">
			<text style="font-size: 36rpx;display: block;">点击图片可查看体验功能！</text>
		</view> -->

		<view class="welcome-title cf262462">
			<text>您想用家姐联盟做什么?</text>
		</view>

		<view class="agreement1">
			<image @tap="agreement = !agreement"
				:src="agreement==true?'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-select.png':'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/icon-noselect.png'">
			</image>
			<text style="text-indent: 16rpx;" @tap="agreement = !agreement">同意</text>


			<navigator @click="goview('https://agent.xiaoyujia.com/ServiceAgreement.html')">
				《服务协议》</navigator>
			<navigator @click="goview('https://agent.xiaoyujia.com/PrivacyAgreement.html')">
				《隐私协议》</navigator>
		</view>

		<view class="button-big">
			<button class="btnStyle" @click="openFranchise()">加入联盟</button>
		</view>

		<view class="button-big">
			<button class="btnStyle" @click="openResume()">看一看</button>
		</view>

	</view>
</template>

<script>
	const afterLoginUrl = "/pages/index/index"
	const beforeLoginUrl = "/pages-mine/login/login"
	export default {
		data() {
			return {
				memberId: uni.getStorageSync('memberId'),
				headImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/jj-icon/logo.png",
				blankImg: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1662515442164blank_img.png",
				logo: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671186988756logo_img.png",
				logo1: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/publicImage/1671715624263logo_img_1.png",
				agreement: true,
				agreementTitle: "您可以先阅读《软件用户协议》，同意后才可体验哦～"
			}
		},
		methods: {
			// 打开图片预览
			openImgPreview() {
				let data = []
				data.push(this.logo)
				uni.previewImage({
					urls: data,
					current: this.logo
				})
			},
			goview(ur) {
				let param = {
					url: ur
				}
				let data = JSON.stringify(param);
				uni.navigateTo({
					url: `/pages-mine/webview/web?param=${encodeURIComponent(data)}`
				})
			},
			openFranchise() {
				if (this.agreement == false) {
					return uni.showToast({
						icon: 'none',
						title: this.agreementTitle
					})
				}
				// uni.setStorageSync("redirectUrl", "/pages-mine/franchise/enter/topUpMoney")
				uni.reLaunch({
					url: "/pages-mine/login/login"
				})
			},
			openResume() {
				if (!this.agreement) {
					return uni.showToast({
						icon: 'none',
						title: this.agreementTitle
					})
				}

				// uni.reLaunch({
				// 	url: "/pages-mine/resume/resume"
				// })
				uni.setStorageSync("redirectUrl", "/pages-mine/resume/resume-simplify")
				uni.reLaunch({
					url: "/pages-mine/login/login"
				})
			},
		},
		onLoad() {
			let memberId = uni.getStorageSync('memberId')
			console.log("检查用户ID：" + memberId)
			if (memberId !== null && memberId !== "") {
				return uni.reLaunch({
					url: afterLoginUrl
				});
			} 
			// 未登录则直接跳转到登录页面，此页面废弃
			else {
				return uni.reLaunch({
					url: beforeLoginUrl
				});
			}
		},
		mounted() {},
	}
</script>

<style lang="scss">
	page {
		height: auto;
		background-color: #ffffff;
	}

	.head-img {
		width: 100%;
		height: 300rpx;

		img {
			display: block;
			width: 200rpx;
			height: 200rpx;
			margin: 100rpx auto;
			border-radius: 50%;

		}
	}

	.welcome-title {
		width: 100%;
		text-align: center;
		margin-top: 20rpx;

		text {
			font-size: 40rpx;
			font-weight: bold;
			line-height: 40rpx;
		}
	}

	.agreement1 {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
		margin-top: 80rpx;
		text-align: center;
		height: 40rpx;
		line-height: 40rpx;
	}

	.agreement1 image {
		width: 40rpx;
		height: 40rpx;
	}

	.button-big {
		width: 100%;
		margin: 40rpx 0;

		button {
			margin: 0rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			border-radius: 10rpx;
			border-radius: 50rpx;
			font-size: 36rpx;
		}

	}

	.introduce-img {
		width: 100%;
		height: auto;

		img {
			display: block;
			width: 100%;
			height: auto;
			margin: 0 auto;
		}
	}
</style>