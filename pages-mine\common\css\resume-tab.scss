	/*
		工作简历栏目--通用样式
	*/

	// 单个栏目
	.resume-tab {
		height: auto;
		width: 100%;
		margin-bottom: 20rpx;
		// 添加栏目底部阴影
		box-shadow: 0 4rpx 20rpx #dedede;
	}

	.tab {
		overflow: auto;
		min-height: 200rpx;
		height: auto;
		padding-bottom: 20rpx;
	}

	/*
		栏目头部以及标题部分
	*/
   
	// 提示横幅
	.tab-tips {
		position: fixed;
		z-index: 1;
		// top: 80rpx;
		width: 100%;
		line-height: 80rpx;
		height: 80rpx;
		font-size: 32rpx;
		color: #ffffff;
		background-color: #1e1848;
		// background-color: rgba(249,174,61, 0.8);
		text {
			margin: 0 40rpx;
		}
	}
	// 栏目提示-小字
	.tab-tips-small {
		width: 90%;
		margin: 0 auto;
		line-height: 60rpx;
		height: 60rpx;
		font-size: 36rpx;
		color: #1e1848;
		background-color: #fff;
    border: 2rpx solid #1e1848;
		border-radius: 40rpx;
		text {
			margin: 0 40rpx;
		}
	}

	// 栏目大标题
	.tab-title {
		display: block;
		width: 80%;
		height: 60rpx;
		line-height: 60rpx;
		text-align: left;
		font-size: 40rpx;
		font-weight: bold;
		// padding: 20rpx 40rpx;
	}

	// 栏目头部
	.tab-head {
		width: 100%;
		height: 100rpx;
		background-color: #ffffff;
		
		text {
			display: block;
			float: left;
			line-height: 100rpx;
			height: 10%;
		}
		
		text:first-child {
			font-size: 46rpx;
			padding: 0 0 0 30rpx;
			font-weight: bold;
		}
		
		text:nth-child(2),text:nth-child(3) {
			font-size: 32rpx;
			color: #909399;
			margin-left: 20rpx;
		}
	}

	.tab-head-smail {
		width: 90%;
		height: auto;
		background-color: #ffffff;
		padding: 0 40rpx;
		
		text {
			font-size: 36rpx;
			line-height: 80rpx;
		}
	}

	/*
		栏目输入框部分
	*/

	// 栏目输入框（单行高）
	.tab-inputbox {
		display: block;
		margin: 20rpx auto;
		width: 90%;
		height: 100rpx;
		background-color: #f9f9f9;
		border-radius: 20rpx;
		box-shadow: 2rpx 2rpx 10rpx #dedede;
	}
	
	.tab-inputbox-high {
		display: block;
		width: 90%;
		margin: 30rpx 40rpx;
	}

	// 单行输入框
	.single-input {
		display: block;
		float: left;
		width: 80%;
		height: 80rpx;
		line-height: 80rpx;
		padding-left: 30rpx;
		font-size: 32rpx;
		text-align: left;
		margin: 10rpx 0 0 20rpx;
		border-style: hidden;
	}
	
	// 多行输入框
	.multiline-input {
		padding: 20rpx 20rpx;
		width: 100%;
		height: 300rpx;
		line-height: 100rpx;
		border-radius: 20rpx;
		color: #000000;
		font-size: 36rpx;
	}
	
	// 选择输入框
	.tab-picker {
		display: block;
		margin: 20rpx auto;
		width: 90%;
		height: 100rpx;
		background-color: #f9f9f9;
		border-radius: 20rpx;
		box-shadow: 2rpx 2rpx 10rpx #dedede;
		
	}
	
	.picker-text {
		float: left;
		display: block;
		width: 80%;
		height: 100rpx;
		line-height: 100rpx;
		padding-left: 40rpx;
		color: #87878c;
		font-size: 32rpx;
	}

	// 小箭头
	.picker-text-arrow {
		display: block;
		float: right;
		height: 100rpx;
		line-height: 100rpx;
		font-size: 36rpx;
		padding-right: 40rpx;
		color: #88888c;
	}

	/*
		栏目选框部分
	*/

	// 单选/多选框
	.tab-checkbox {
		float: left;
		display: block;
		width: 190rpx;
		height: 80rpx;
		line-height: 80rpx;
		margin: 30rpx 20rpx 20rpx 30rpx;
		
		text {
			display: block;
			width: 100%;
			text-align: center;
			font-size: 32rpx;
			
			white-space: nowrap;  /*强制span不换行*/
			overflow: hidden;  /*超出宽度部分隐藏*/
			text-overflow: ellipsis;  /*超出部分以点号代替*/

		}
	}

	.checkbox {
		border-radius: 20rpx;
		background-color: #f9f9f9;
	}

	// 选框选中后样式
	.activeBox {
		color: #1e1848;
		border: #1e1848 3rpx solid;
		background-color: #fff;
		line-height: 74rpx;
	}

	/*
		按钮部分
	*/

	// 大按钮
	.btn-big {
		padding-bottom: 60rpx;

		button {
			margin: 80rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	// 底部按钮
	.btn-bottom {
		button {
			bottom: 20rpx;
			margin: 20rpx auto;
			width: 80%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}

	// 底部固定按钮
	.btn-bottom-fixed {
		position: fixed;
		z-index: 999;
		bottom: 0rpx;
		width: 100%;
		height: 120rpx;
		background-color: #ffffff;

		button {
			margin: 20rpx 5%;
			width: 90%;
			height: 80rpx;
			line-height: 80rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}
	
	// 按钮组
	.btn-group {
		width: 100%;
		height: 90rpx;
		display: flex;
		flex-direction: row;
		padding: 80rpx 0 200rpx 0;
	
		button {
			width: 40%;
			height: 90rpx;
			line-height: 90rpx;
			color: #f6cc70;
			background-color: #1e1848;
			border-radius: 50rpx;
			font-size: 36rpx;
		}
	}
